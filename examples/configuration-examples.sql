-- ============================================================================
-- Enhanced Data Initialization Script - Configuration Examples
-- ============================================================================
-- This file contains pre-configured examples for common usage scenarios.
-- Copy the desired configuration section and modify the main script accordingly.
-- ============================================================================

-- ============================================================================
-- DEVELOPMENT ENVIRONMENT CONFIGURATION
-- ============================================================================
-- Use Case: Local development, quick testing, debugging
-- Dataset Size: 1,000 saga transactions
-- Expected Time: 30 seconds - 2 minutes
-- Memory Usage: ~5MB
-- ============================================================================

-- Development Configuration
SET @total_sagas = 1000;
SET @batch_size = 500;
SET @business_types = 5;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = FALSE;  -- Keep normal settings for debugging

-- Detailed logging for development
SET @log_batch_progress = TRUE;
SET @log_performance_metrics = TRUE;
SET @log_memory_usage = TRUE;
SET @log_error_details = TRUE;

-- Conservative performance settings
SET @bulk_insert_buffer_size = '64M';
SET @read_buffer_size = '2M';
SET @sort_buffer_size = '4M';
SET @tmp_table_size = '128M';
SET @max_heap_table_size = '128M';

-- Standard status distribution for realistic testing
SET @status_running_pct = 50;
SET @status_completed_pct = 25;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 10;
SET @status_failed_pct = 5;

-- Balanced step distribution
SET @steps_3_pct = 20;
SET @steps_4_pct = 40;
SET @steps_5_pct = 40;

-- Standard mode distribution
SET @auto_mode_pct = 75;
SET @manual_mode_pct = 25;

-- Enable all validation for development
SET @validate_referential_integrity = TRUE;
SET @validate_status_distribution = TRUE;
SET @validate_step_counts = TRUE;
SET @validate_json_data = TRUE;
SET @tolerance_percentage = 2.0;

-- ============================================================================
-- TESTING ENVIRONMENT CONFIGURATION
-- ============================================================================
-- Use Case: Integration testing, CI/CD pipelines, QA testing
-- Dataset Size: 100,000 saga transactions
-- Expected Time: 3-8 minutes
-- Memory Usage: ~80MB
-- ============================================================================

-- Testing Configuration
SET @total_sagas = 100000;
SET @batch_size = 10000;
SET @business_types = 5;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = TRUE;

-- Moderate logging for testing
SET @log_batch_progress = TRUE;
SET @log_performance_metrics = TRUE;
SET @log_memory_usage = FALSE;  -- Reduce overhead
SET @log_error_details = TRUE;

-- Optimized performance settings
SET @bulk_insert_buffer_size = '128M';
SET @read_buffer_size = '4M';
SET @sort_buffer_size = '8M';
SET @tmp_table_size = '256M';
SET @max_heap_table_size = '256M';

-- Realistic production-like distribution
SET @status_running_pct = 45;
SET @status_completed_pct = 30;
SET @status_pending_pct = 12;
SET @status_compensating_pct = 8;
SET @status_failed_pct = 5;

-- Standard step distribution
SET @steps_3_pct = 20;
SET @steps_4_pct = 40;
SET @steps_5_pct = 40;

-- Standard mode distribution
SET @auto_mode_pct = 75;
SET @manual_mode_pct = 25;

-- Enable key validations
SET @validate_referential_integrity = TRUE;
SET @validate_status_distribution = TRUE;
SET @validate_step_counts = TRUE;
SET @validate_json_data = FALSE;  -- Skip for performance
SET @tolerance_percentage = 3.0;

-- ============================================================================
-- PERFORMANCE TESTING CONFIGURATION
-- ============================================================================
-- Use Case: Load testing, performance benchmarking, stress testing
-- Dataset Size: 1,000,000 saga transactions
-- Expected Time: 8-15 minutes
-- Memory Usage: ~400MB
-- ============================================================================

-- Performance Testing Configuration
SET @total_sagas = 1000000;
SET @batch_size = 50000;
SET @business_types = 5;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = TRUE;

-- Minimal logging for performance
SET @log_batch_progress = TRUE;
SET @log_performance_metrics = FALSE;  -- Reduce overhead
SET @log_memory_usage = FALSE;
SET @log_error_details = TRUE;

-- High-performance settings
SET @bulk_insert_buffer_size = '256M';
SET @read_buffer_size = '8M';
SET @sort_buffer_size = '16M';
SET @tmp_table_size = '512M';
SET @max_heap_table_size = '512M';

-- Production-realistic distribution
SET @status_running_pct = 50;
SET @status_completed_pct = 25;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 10;
SET @status_failed_pct = 5;

-- Standard step distribution
SET @steps_3_pct = 20;
SET @steps_4_pct = 40;
SET @steps_5_pct = 40;

-- Standard mode distribution
SET @auto_mode_pct = 75;
SET @manual_mode_pct = 25;

-- Selective validation for performance
SET @validate_referential_integrity = TRUE;
SET @validate_status_distribution = FALSE;  -- Skip for speed
SET @validate_step_counts = FALSE;
SET @validate_json_data = FALSE;
SET @tolerance_percentage = 5.0;

-- ============================================================================
-- STRESS TESTING CONFIGURATION
-- ============================================================================
-- Use Case: Maximum load testing, system limits testing
-- Dataset Size: 10,000,000 saga transactions
-- Expected Time: 45-90 minutes
-- Memory Usage: ~800MB
-- ============================================================================

-- Stress Testing Configuration
SET @total_sagas = 10000000;
SET @batch_size = 100000;
SET @business_types = 5;
SET @enable_progress_output = FALSE;  -- Minimize I/O overhead
SET @enable_validation = FALSE;       -- Skip validation for maximum speed
SET @enable_performance_mode = TRUE;

-- Minimal logging for maximum performance
SET @log_batch_progress = FALSE;
SET @log_performance_metrics = FALSE;
SET @log_memory_usage = FALSE;
SET @log_error_details = TRUE;  -- Keep error logging

-- Maximum performance settings
SET @bulk_insert_buffer_size = '512M';
SET @read_buffer_size = '16M';
SET @sort_buffer_size = '32M';
SET @tmp_table_size = '1G';
SET @max_heap_table_size = '1G';

-- Standard distribution
SET @status_running_pct = 50;
SET @status_completed_pct = 25;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 10;
SET @status_failed_pct = 5;

-- Standard step distribution
SET @steps_3_pct = 20;
SET @steps_4_pct = 40;
SET @steps_5_pct = 40;

-- Standard mode distribution
SET @auto_mode_pct = 75;
SET @manual_mode_pct = 25;

-- Disable all validation for maximum speed
SET @validate_referential_integrity = FALSE;
SET @validate_status_distribution = FALSE;
SET @validate_step_counts = FALSE;
SET @validate_json_data = FALSE;

-- ============================================================================
-- HIGH-LOAD SCENARIO CONFIGURATION
-- ============================================================================
-- Use Case: Testing system behavior under high transaction load
-- Focus: More running and pending transactions
-- ============================================================================

-- High-Load Scenario
SET @total_sagas = 500000;
SET @batch_size = 25000;

-- High-load status distribution
SET @status_running_pct = 70;      -- Much higher active load
SET @status_completed_pct = 15;    -- Fewer completions
SET @status_pending_pct = 10;      -- More waiting transactions
SET @status_compensating_pct = 3;  -- Fewer compensations
SET @status_failed_pct = 2;        -- Fewer failures

-- More complex workflows under load
SET @steps_3_pct = 10;   -- Fewer simple workflows
SET @steps_4_pct = 30;   -- Standard workflows
SET @steps_5_pct = 60;   -- More complex workflows

-- More auto mode for system-managed load
SET @auto_mode_pct = 85;
SET @manual_mode_pct = 15;

-- ============================================================================
-- ERROR SCENARIO TESTING CONFIGURATION
-- ============================================================================
-- Use Case: Testing compensation mechanisms and error handling
-- Focus: More failed and compensating transactions
-- ============================================================================

-- Error Scenario Configuration
SET @total_sagas = 200000;
SET @batch_size = 20000;

-- Error-heavy status distribution
SET @status_running_pct = 30;      -- Fewer running transactions
SET @status_completed_pct = 20;    -- Fewer successful completions
SET @status_pending_pct = 15;      -- More pending (stuck) transactions
SET @status_compensating_pct = 25; -- Much higher compensation rate
SET @status_failed_pct = 10;       -- Higher failure rate

-- More complex workflows (more failure points)
SET @steps_3_pct = 15;
SET @steps_4_pct = 35;
SET @steps_5_pct = 50;

-- More manual mode (more error-prone)
SET @auto_mode_pct = 60;
SET @manual_mode_pct = 40;

-- Enable detailed validation for error scenarios
SET @enable_validation = TRUE;
SET @validate_referential_integrity = TRUE;
SET @validate_status_distribution = TRUE;
SET @validate_step_counts = TRUE;
SET @validate_json_data = TRUE;

-- ============================================================================
-- MEMORY-CONSTRAINED ENVIRONMENT CONFIGURATION
-- ============================================================================
-- Use Case: Running on systems with limited memory (< 4GB RAM)
-- Focus: Minimal memory usage, smaller batches
-- ============================================================================

-- Memory-Constrained Configuration
SET @total_sagas = 50000;
SET @batch_size = 5000;            -- Very small batches
SET @business_types = 5;
SET @enable_progress_output = TRUE;
SET @enable_validation = FALSE;    -- Skip validation to save memory
SET @enable_performance_mode = FALSE;

-- Minimal memory settings
SET @bulk_insert_buffer_size = '32M';
SET @read_buffer_size = '1M';
SET @sort_buffer_size = '2M';
SET @tmp_table_size = '64M';
SET @max_heap_table_size = '64M';

-- Disable memory-intensive features
SET @log_batch_progress = FALSE;
SET @log_performance_metrics = FALSE;
SET @log_memory_usage = FALSE;
SET @log_error_details = TRUE;

-- Use disk-based temporary tables
-- SET sql_big_tables = 1;  -- Uncomment if needed

-- Standard distributions
SET @status_running_pct = 50;
SET @status_completed_pct = 25;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 10;
SET @status_failed_pct = 5;

-- ============================================================================
-- CLOUD DATABASE CONFIGURATION
-- ============================================================================
-- Use Case: Running on cloud databases (AWS RDS, Google Cloud SQL, etc.)
-- Focus: Network-optimized settings, connection management
-- ============================================================================

-- Cloud Database Configuration
SET @total_sagas = 300000;
SET @batch_size = 15000;           -- Smaller batches for network efficiency
SET @business_types = 5;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = TRUE;

-- Network-optimized settings
SET @bulk_insert_buffer_size = '128M';
SET @read_buffer_size = '4M';
SET @sort_buffer_size = '8M';
SET @tmp_table_size = '256M';
SET @max_heap_table_size = '256M';

-- Conservative logging for network overhead
SET @log_batch_progress = TRUE;
SET @log_performance_metrics = FALSE;
SET @log_memory_usage = FALSE;
SET @log_error_details = TRUE;

-- Longer timeouts for cloud databases
SET @time_range_hours = 2;
SET @max_step_delay_minutes = 30;

-- Standard distributions
SET @status_running_pct = 50;
SET @status_completed_pct = 25;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 10;
SET @status_failed_pct = 5;

-- ============================================================================
-- CUSTOM BUSINESS SCENARIO CONFIGURATION
-- ============================================================================
-- Use Case: Testing specific business scenarios or custom workflows
-- Focus: Customized business type distributions
-- ============================================================================

-- Custom Business Scenario Configuration
SET @total_sagas = 100000;
SET @batch_size = 10000;

-- Custom business type focus (modify business_step_templates accordingly)
-- This example focuses more on ecommerce and payment scenarios

-- You can modify the business_step_templates table to adjust scenario distribution:
/*
-- Example: Add weight-based selection for business scenarios
UPDATE business_step_templates SET weight = 2.0 WHERE business_type = 'ecommerce';
UPDATE business_step_templates SET weight = 2.0 WHERE business_type = 'payment';
UPDATE business_step_templates SET weight = 1.0 WHERE business_type = 'inventory';
UPDATE business_step_templates SET weight = 0.5 WHERE business_type = 'user';
UPDATE business_step_templates SET weight = 0.5 WHERE business_type = 'sync';
*/

-- Custom compensation windows for different business types
SET @ecommerce_compensation_window = 600;    -- 10 minutes for ecommerce
SET @payment_compensation_window = 300;      -- 5 minutes for payments
SET @inventory_compensation_window = 900;    -- 15 minutes for inventory
SET @user_compensation_window = 180;         -- 3 minutes for user operations
SET @sync_compensation_window = 1800;        -- 30 minutes for data sync

-- Standard distributions
SET @status_running_pct = 50;
SET @status_completed_pct = 25;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 10;
SET @status_failed_pct = 5;

-- ============================================================================
-- INCREMENTAL DATA GENERATION CONFIGURATION
-- ============================================================================
-- Use Case: Adding data to existing dataset without clearing
-- Focus: Append-only generation
-- ============================================================================

-- Incremental Generation Configuration
SET @total_sagas = 100000;         -- Additional sagas to generate
SET @batch_size = 10000;
SET @business_types = 5;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = TRUE;

-- IMPORTANT: Don't clear existing data
SET @clear_existing_data = FALSE;
SET @reset_auto_increment = FALSE;

-- Use different UUID seeds to avoid conflicts
SET @uuid_seed_base = 54321;       -- Different from default 12345

-- Standard settings
SET @bulk_insert_buffer_size = '256M';
SET @tmp_table_size = '512M';

-- Standard distributions
SET @status_running_pct = 50;
SET @status_completed_pct = 25;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 10;
SET @status_failed_pct = 5;

-- ============================================================================
-- DEBUGGING AND TROUBLESHOOTING CONFIGURATION
-- ============================================================================
-- Use Case: Debugging issues, detailed analysis
-- Focus: Maximum logging and validation
-- ============================================================================

-- Debugging Configuration
SET @total_sagas = 5000;           -- Small dataset for debugging
SET @batch_size = 500;             -- Small batches for detailed tracking
SET @business_types = 5;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = FALSE;

-- Maximum logging for debugging
SET @log_batch_progress = TRUE;
SET @log_performance_metrics = TRUE;
SET @log_memory_usage = TRUE;
SET @log_error_details = TRUE;

-- Conservative performance settings
SET @bulk_insert_buffer_size = '64M';
SET @read_buffer_size = '2M';
SET @sort_buffer_size = '4M';
SET @tmp_table_size = '128M';
SET @max_heap_table_size = '128M';

-- Enable all validation
SET @validate_referential_integrity = TRUE;
SET @validate_status_distribution = TRUE;
SET @validate_step_counts = TRUE;
SET @validate_json_data = TRUE;
SET @tolerance_percentage = 1.0;   -- Strict tolerance

-- Standard distributions
SET @status_running_pct = 50;
SET @status_completed_pct = 25;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 10;
SET @status_failed_pct = 5;

-- ============================================================================
-- USAGE INSTRUCTIONS
-- ============================================================================
/*
To use these configurations:

1. Copy the desired configuration section
2. Paste it at the beginning of the enhanced-initialize-test-data.sql file
   (after the existing configuration variables section)
3. Comment out or remove the original configuration variables
4. Run the script as normal

Example:
```bash
# Create a custom script with your chosen configuration
cat > my-custom-config.sql << 'EOF'
-- Paste your chosen configuration here
SET @total_sagas = 100000;
-- ... rest of configuration

-- Then source the main script
source enhanced-initialize-test-data.sql;
EOF

# Run the custom configuration
mysql -u username -p database_name < my-custom-config.sql
```

Or modify the configuration variables directly in the main script file.
*/

-- ============================================================================
-- CONFIGURATION VALIDATION
-- ============================================================================
-- Use this section to validate your configuration before running the main script

SELECT 
    'Configuration Validation' as check_type,
    @total_sagas as total_sagas,
    @batch_size as batch_size,
    CEILING(@total_sagas / @batch_size) as calculated_batches,
    (@status_running_pct + @status_completed_pct + @status_pending_pct + 
     @status_compensating_pct + @status_failed_pct) as status_total_should_be_100,
    (@steps_3_pct + @steps_4_pct + @steps_5_pct) as steps_total_should_be_100,
    (@auto_mode_pct + @manual_mode_pct) as mode_total_should_be_100,
    CASE 
        WHEN @batch_size > 0 AND @batch_size <= @total_sagas THEN 'Valid'
        ELSE 'Invalid'
    END as batch_size_validation,
    CASE 
        WHEN @total_sagas > 0 THEN 'Valid'
        ELSE 'Invalid'
    END as total_sagas_validation;