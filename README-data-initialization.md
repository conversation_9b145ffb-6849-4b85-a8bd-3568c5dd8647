# Enhanced Data Initialization Script Documentation

## Overview

This directory contains comprehensive documentation and examples for the Enhanced Data Initialization Script v2.0.0, a powerful MySQL-based solution for generating realistic test data for the Saga distributed transaction system.

## Documentation Structure

### 📚 Core Documentation

| Document | Description | Use Case |
|----------|-------------|----------|
| **[Complete User Guide](docs/data-initialization-script-guide.md)** | Comprehensive documentation covering all aspects of the script | First-time users, detailed reference |
| **[Quick Reference Guide](docs/quick-reference-guide.md)** | Fast lookup for common commands and configurations | Daily usage, quick troubleshooting |
| **[Troubleshooting Guide](docs/troubleshooting-guide.md)** | Detailed solutions for common issues and error scenarios | Problem resolution, debugging |

### 🔧 Configuration Resources

| Resource | Description | Use Case |
|----------|-------------|----------|
| **[Configuration Examples](examples/configuration-examples.sql)** | Pre-configured settings for different scenarios | Quick setup, best practices |
| **[Enhanced Script](enhanced-initialize-test-data.sql)** | Main script file with comprehensive features | Data generation execution |

## Quick Start

### 1. Choose Your Scenario

Select the appropriate configuration for your use case:

- **Development** (1K records): Fast setup for local development
- **Testing** (100K records): Integration testing and CI/CD
- **Performance** (1M records): Load testing and benchmarking  
- **Stress** (10M records): Maximum capacity testing

### 2. Basic Execution

```bash
# Standard execution with default settings
mysql -u username -p database_name < enhanced-initialize-test-data.sql

# With custom configuration
mysql -u username -p database_name < examples/configuration-examples.sql
```

### 3. Monitor Progress

The script provides real-time progress updates:
```
Batch Progress Report - SAGA
batch_number: 15/20
records_progress: 750,000/1,000,000
overall_completion: 75.0%
current_rate: 12,500 rec/sec
ETA: 14:32:15
```

## Key Features

### 🎯 Realistic Business Scenarios
- **E-commerce**: Order processing workflows
- **Payment**: Financial transaction processing
- **Inventory**: Stock management operations
- **User Registration**: Account creation flows
- **Data Sync**: ETL and synchronization processes

### 📊 Configurable Distributions
- **Status Distribution**: 50% running, 25% completed, 10% pending, 10% compensating, 5% failed
- **Step Counts**: 20% have 3 steps, 40% have 4 steps, 40% have 5 steps
- **Processing Modes**: 75% auto mode, 25% manual mode

### ⚡ Performance Optimizations
- Batch processing for memory efficiency
- Automatic MySQL settings optimization
- Deterministic UUID generation
- Progress monitoring and validation

## Documentation Guide

### For New Users
1. Start with the **[Complete User Guide](docs/data-initialization-script-guide.md)**
2. Review **[Configuration Examples](examples/configuration-examples.sql)** for your scenario
3. Use the **[Quick Reference Guide](docs/quick-reference-guide.md)** for commands

### For Experienced Users
1. Use **[Quick Reference Guide](docs/quick-reference-guide.md)** for fast lookup
2. Refer to **[Configuration Examples](examples/configuration-examples.sql)** for optimization
3. Check **[Troubleshooting Guide](docs/troubleshooting-guide.md)** when issues arise

### For System Administrators
1. Review **[Complete User Guide](docs/data-initialization-script-guide.md)** sections on:
   - Prerequisites and system requirements
   - Performance tuning recommendations
   - Monitoring and observability
2. Use **[Troubleshooting Guide](docs/troubleshooting-guide.md)** for:
   - System-specific issues
   - Recovery procedures
   - Diagnostic tools

## Common Use Cases

### Development Environment Setup
```sql
-- Quick development setup (1K records)
SET @total_sagas = 1000;
SET @batch_size = 500;
SET @enable_progress_output = TRUE;
```
**Time**: 30 seconds - 2 minutes  
**Memory**: ~5MB

### CI/CD Pipeline Integration
```sql
-- Testing environment (100K records)
SET @total_sagas = 100000;
SET @batch_size = 10000;
SET @enable_performance_mode = TRUE;
```
**Time**: 3-8 minutes  
**Memory**: ~80MB

### Performance Benchmarking
```sql
-- Large dataset (1M records)
SET @total_sagas = 1000000;
SET @batch_size = 50000;
SET @enable_validation = TRUE;
```
**Time**: 8-15 minutes  
**Memory**: ~400MB

### Stress Testing
```sql
-- Maximum load (10M records)
SET @total_sagas = 10000000;
SET @batch_size = 100000;
SET @enable_progress_output = FALSE;
```
**Time**: 45-90 minutes  
**Memory**: ~800MB

## System Requirements

### Minimum Requirements
- **MySQL**: 8.0+ (required for JSON support)
- **Memory**: 4GB RAM
- **Storage**: SSD recommended
- **CPU**: Multi-core recommended

### Recommended Configuration
```ini
# MySQL configuration (my.cnf)
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 512M
bulk_insert_buffer_size = 256M
tmp_table_size = 512M
max_heap_table_size = 512M
```

## Performance Benchmarks

| Dataset Size | Expected Time | Memory Usage | Recommended Batch Size |
|--------------|---------------|--------------|----------------------|
| 1K records | 30 seconds | 5MB | 500 |
| 10K records | 2 minutes | 20MB | 2,000 |
| 100K records | 5 minutes | 80MB | 10,000 |
| 1M records | 12 minutes | 400MB | 50,000 |
| 10M records | 60 minutes | 800MB | 100,000 |

*Performance varies based on hardware configuration*

## Troubleshooting Quick Reference

### Common Issues

| Issue | Quick Fix | Documentation |
|-------|-----------|---------------|
| Out of memory | Reduce `@batch_size` to 5000 | [Memory Issues](docs/troubleshooting-guide.md#memory-and-resource-problems) |
| Slow performance | Enable `@enable_performance_mode = TRUE` | [Performance Issues](docs/troubleshooting-guide.md#performance-issues) |
| Connection timeout | Increase timeout settings | [Connection Issues](docs/troubleshooting-guide.md#common-error-messages) |
| Disk space full | Reduce `@total_sagas` | [Disk Issues](docs/troubleshooting-guide.md#common-error-messages) |

### Emergency Cleanup
```sql
-- Stop script and clean up
DROP TEMPORARY TABLE IF EXISTS batch_progress_tracking;
DROP TEMPORARY TABLE IF EXISTS overall_progress_summary;
SET foreign_key_checks = 1;
SET autocommit = 1;
```

## Advanced Features

### Custom Business Scenarios
Modify the `business_step_templates` table to create custom workflows:
```sql
INSERT INTO business_step_templates (
    step_order, action, service_name, business_type, 
    compensate_endpoint, description
) VALUES (
    1, 'CustomAction', 'custom-service', 'custom',
    'http://custom-service:8080/saga/compensate/CustomAction',
    'Custom business workflow step'
);
```

### Incremental Data Generation
Add data without clearing existing records:
```sql
SET @clear_existing_data = FALSE;
SET @uuid_seed_base = 54321;  -- Different seed to avoid conflicts
```

### Performance Monitoring
Real-time monitoring during execution:
```sql
-- Check progress in another session
SELECT COUNT(*) FROM saga_transactions;
SELECT * FROM batch_progress_tracking ORDER BY batch_number DESC LIMIT 5;
```

## Best Practices

### Pre-Execution Checklist
- [ ] Verify database schema compatibility
- [ ] Check available disk space (2x expected data size)
- [ ] Confirm sufficient RAM (4GB minimum)
- [ ] Test with small dataset first
- [ ] Review configuration parameters

### During Execution
- [ ] Monitor system resources
- [ ] Watch for error messages
- [ ] Track progress metrics
- [ ] Be prepared to stop if issues arise

### Post-Execution
- [ ] Verify data integrity
- [ ] Check statistics match expectations
- [ ] Review performance metrics
- [ ] Document configuration for future use

## Support and Resources

### Getting Help
1. **Check Documentation**: Start with the appropriate guide above
2. **Review Examples**: Use configuration examples for your scenario
3. **Troubleshooting**: Consult the troubleshooting guide for specific issues
4. **System Logs**: Check MySQL error logs for detailed error information

### Additional Resources
- **MySQL Documentation**: For database-specific configuration
- **Performance Tuning**: System-specific optimization guides
- **Monitoring Tools**: Database and system monitoring solutions

### Contributing
When reporting issues or requesting features:
1. Include your configuration settings
2. Provide error messages and logs
3. Specify your system environment
4. Include steps to reproduce the issue

---

## File Structure

```
├── enhanced-initialize-test-data.sql          # Main script file
├── README-data-initialization.md              # This file
├── docs/
│   ├── data-initialization-script-guide.md   # Complete user guide
│   ├── quick-reference-guide.md               # Quick reference
│   └── troubleshooting-guide.md               # Troubleshooting guide
└── examples/
    └── configuration-examples.sql             # Configuration examples
```

For the most up-to-date information and detailed instructions, always refer to the individual documentation files linked above.