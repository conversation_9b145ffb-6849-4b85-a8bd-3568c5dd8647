#!/usr/bin/env bash

# Saga 分布式事务系统 - 集成性能测试脚本
# 在执行压测的同时收集Docker容器性能统计数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 默认配置
TEST_SUITE="basic"  # basic, full, custom
STATS_INTERVAL=2    # 统计收集间隔(秒)
OUTPUT_DIR="performance-test/results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 测试配置函数
get_test_command() {
    case "$1" in
        "create")
            echo "wrk -t4 -c20 -d30s --latency -s performance-test/create-saga-test.lua http://localhost:8080/saga/transactions"
            ;;
        "compensation")
            echo "wrk -t4 -c30 -d30s --latency -s performance-test/compensation-unique-test.lua http://localhost:8080/saga/transactions/compensation"
            ;;
        "query")
            echo "wrk -t8 -c50 -d30s --latency -s performance-test/query-real-test.lua http://localhost:8080"
            ;;
        "commit")
            echo "wrk -t4 -c20 -d30s --latency -s performance-test/commit-real-test.lua http://localhost:8080"
            ;;
        "rollback")
            echo "wrk -t4 -c20 -d30s --latency -s performance-test/rollback-real-test.lua http://localhost:8080"
            ;;
        *)
            echo ""
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "集成性能测试工具 - 压测 + Docker统计收集"
    echo ""
    echo "用法:"
    echo "  $0 [选项] [测试套件]"
    echo ""
    echo "测试套件:"
    echo "  basic    - 基础测试 (创建 + 查询)"
    echo "  full     - 完整测试 (所有5个接口)"
    echo "  create   - 仅事务创建测试"
    echo "  query    - 仅状态查询测试"
    echo "  custom   - 自定义测试 (通过-c指定)"
    echo ""
    echo "选项:"
    echo "  -i, --interval SECONDS  统计收集间隔，默认2秒"
    echo "  -c, --command COMMAND   自定义wrk命令"
    echo "  -o, --output DIR        输出目录，默认performance-test/results"
    echo "  -h, --help             显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 basic                # 基础测试套件"
    echo "  $0 full                 # 完整测试套件"
    echo "  $0 create               # 仅创建接口测试"
    echo "  $0 -i 1 query           # 1秒间隔收集统计，查询测试"
    echo "  $0 -c 'wrk -t8 -c100 -d60s --latency http://localhost:8080/hello' custom"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -i|--interval)
                STATS_INTERVAL="$2"
                shift 2
                ;;
            -c|--command)
                CUSTOM_COMMAND="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            basic|full|create|compensation|query|commit|rollback|custom)
                TEST_SUITE="$1"
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查环境
check_environment() {
    print_info "检查测试环境..."
    
    # 检查wrk
    if ! command -v wrk &> /dev/null; then
        print_error "wrk未安装，请先安装: brew install wrk"
        exit 1
    fi
    
    # 检查Docker容器
    if ! docker ps | grep -q "saga-mysql\|saga-app"; then
        print_error "Saga容器未运行，请先启动: make up"
        exit 1
    fi
    
    # 检查测试脚本
    local required_scripts=("create-saga-test.lua" "query-real-test.lua")
    for script in "${required_scripts[@]}"; do
        if [ ! -f "performance-test/$script" ]; then
            print_error "测试脚本不存在: performance-test/$script"
            exit 1
        fi
    done
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    print_success "环境检查完成"
}

# 启动Docker统计收集
start_stats_collection() {
    local duration=$1
    local stats_file="$OUTPUT_DIR/docker-stats-$TIMESTAMP.csv"
    
    print_info "启动Docker统计收集 (间隔: ${STATS_INTERVAL}s, 时长: ${duration}s)"
    
    # 启动统计收集脚本
    STATS_INTERVAL=$STATS_INTERVAL STATS_DURATION=$duration ./performance-test/collect-docker-stats.sh &
    local stats_pid=$!
    
    echo $stats_pid > "$OUTPUT_DIR/stats_pid_$TIMESTAMP.tmp"
    print_info "统计收集进程ID: $stats_pid"
}

# 执行单个测试
run_single_test() {
    local test_name=$1
    local test_command=$2
    local test_duration=$3
    
    print_info "开始执行测试: $test_name"
    echo "命令: $test_command"
    
    # 输出文件
    local test_output="$OUTPUT_DIR/test-${test_name}-$TIMESTAMP.txt"
    
    # 启动统计收集
    start_stats_collection $test_duration
    
    # 等待统计收集启动
    sleep 2
    
    # 执行压测
    echo "开始时间: $(date '+%Y-%m-%d %H:%M:%S')" > "$test_output"
    echo "测试命令: $test_command" >> "$test_output"
    echo "===========================================" >> "$test_output"
    
    if eval "$test_command" >> "$test_output" 2>&1; then
        echo "===========================================" >> "$test_output"
        echo "结束时间: $(date '+%Y-%m-%d %H:%M:%S')" >> "$test_output"
        print_success "测试 $test_name 完成"
    else
        print_error "测试 $test_name 失败"
        return 1
    fi
    
    # 等待统计收集完成
    local stats_pid_file="$OUTPUT_DIR/stats_pid_$TIMESTAMP.tmp"
    if [ -f "$stats_pid_file" ]; then
        local stats_pid=$(cat "$stats_pid_file")
        print_info "等待统计收集完成..."
        wait $stats_pid 2>/dev/null || true
        rm -f "$stats_pid_file"
    fi
    
    print_success "测试 $test_name 及统计收集完成"
}

# 执行测试套件
run_test_suite() {
    case "$TEST_SUITE" in
        "basic")
            print_info "执行基础测试套件 (创建 + 查询)"
            run_single_test "create" "$(get_test_command create)" 35
            sleep 5  # 测试间隔
            run_single_test "query" "$(get_test_command query)" 35
            ;;
        "full")
            print_info "执行完整测试套件 (所有接口)"
            local tests=("create" "compensation" "query" "commit" "rollback")
            for test in "${tests[@]}"; do
                run_single_test "$test" "$(get_test_command $test)" 35
                if [ "$test" != "rollback" ]; then
                    sleep 5  # 测试间隔
                fi
            done
            ;;
        "create"|"compensation"|"query"|"commit"|"rollback")
            print_info "执行单个测试: $TEST_SUITE"
            run_single_test "$TEST_SUITE" "$(get_test_command $TEST_SUITE)" 35
            ;;
        "custom")
            if [ -z "$CUSTOM_COMMAND" ]; then
                print_error "自定义测试需要使用 -c 参数指定命令"
                exit 1
            fi
            print_info "执行自定义测试"
            # 从命令中提取持续时间
            local duration=$(echo "$CUSTOM_COMMAND" | grep -o '\-d[0-9]*[sm]' | sed 's/-d//' | sed 's/s$//' | sed 's/m$//' | head -1)
            if [[ "$CUSTOM_COMMAND" == *"-d"*"m"* ]]; then
                duration=$((duration * 60))
            fi
            duration=${duration:-30}  # 默认30秒
            run_single_test "custom" "$CUSTOM_COMMAND" $((duration + 5))
            ;;
        *)
            print_error "未知的测试套件: $TEST_SUITE"
            exit 1
            ;;
    esac
}

# 生成综合报告
generate_comprehensive_report() {
    local report_file="$OUTPUT_DIR/comprehensive-report-$TIMESTAMP.md"
    
    print_info "生成综合性能报告..."
    
    {
        echo "# Saga 分布式事务系统 - 综合性能测试报告"
        echo ""
        echo "**测试时间**: $(date '+%Y年%m月%d日 %H:%M:%S')"
        echo "**测试套件**: $TEST_SUITE"
        echo "**统计间隔**: ${STATS_INTERVAL}秒"
        echo ""
        
        echo "## 📊 测试概览"
        echo ""
        echo "| 文件类型 | 文件名 | 大小 |"
        echo "|----------|--------|------|"
        
        # 列出生成的文件
        for file in "$OUTPUT_DIR"/*-$TIMESTAMP.*; do
            if [ -f "$file" ]; then
                local filename=$(basename "$file")
                local filesize=$(ls -lh "$file" | awk '{print $5}')
                local filetype="其他"
                
                case "$filename" in
                    *docker-stats*) filetype="Docker统计" ;;
                    *mysql-stats*) filetype="MySQL统计" ;;
                    *test-*) filetype="压测结果" ;;
                    *performance-summary*) filetype="性能汇总" ;;
                esac
                
                echo "| $filetype | \`$filename\` | $filesize |"
            fi
        done
        
        echo ""
        echo "## 🎯 关键发现"
        echo ""
        
        # 分析测试结果
        local test_files=("$OUTPUT_DIR"/test-*-$TIMESTAMP.txt)
        if [ ${#test_files[@]} -gt 0 ] && [ -f "${test_files[0]}" ]; then
            echo "### 性能指标摘要"
            echo ""
            
            for test_file in "${test_files[@]}"; do
                if [ -f "$test_file" ]; then
                    local test_name=$(basename "$test_file" | sed "s/test-//" | sed "s/-$TIMESTAMP.txt//")
                    echo "#### $test_name 测试"
                    
                    # 提取关键指标
                    local qps=$(grep "Requests/sec:" "$test_file" | awk '{print $2}' | head -1)
                    local avg_latency=$(grep "Latency" "$test_file" | awk '{print $2}' | head -1)
                    local p99_latency=$(grep "99%" "$test_file" | awk '{print $2}' | head -1)
                    
                    if [ -n "$qps" ]; then
                        echo "- **QPS**: $qps"
                        echo "- **平均延迟**: $avg_latency"
                        echo "- **P99延迟**: $p99_latency"
                        echo ""
                    fi
                fi
            done
        fi
        
        echo "## 📈 Docker容器性能"
        echo ""
        echo "详细的容器性能数据请查看对应的统计文件。"
        echo ""
        
        echo "## 🔍 分析建议"
        echo ""
        echo "1. **性能对比**: 将本次测试结果与历史数据对比"
        echo "2. **资源瓶颈**: 检查CPU、内存使用峰值"
        echo "3. **优化方向**: 根据统计数据确定优化重点"
        echo "4. **扩容建议**: 评估是否需要增加资源配置"
        echo ""
        
        echo "## 📋 文件说明"
        echo ""
        echo "- **压测结果文件**: 包含wrk的详细输出和性能指标"
        echo "- **Docker统计文件**: CSV格式的容器资源使用数据"
        echo "- **MySQL统计文件**: 数据库性能指标和状态信息"
        echo "- **性能汇总文件**: 自动生成的性能分析报告"
        echo ""
        
        echo "---"
        echo "**报告生成**: $(date '+%Y-%m-%d %H:%M:%S')"
        
    } > "$report_file"
    
    print_success "综合报告生成完成: $report_file"
}

# 清理临时文件
cleanup() {
    # 清理临时PID文件
    rm -f "$OUTPUT_DIR"/stats_pid_*.tmp
    
    # 如果有运行中的统计收集进程，尝试终止
    local stats_pids=$(pgrep -f "collect-docker-stats.sh" || true)
    if [ -n "$stats_pids" ]; then
        print_info "清理统计收集进程..."
        echo "$stats_pids" | xargs kill -TERM 2>/dev/null || true
    fi
}

# 信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    echo "🚀 Saga 性能测试 + Docker统计收集工具"
    echo "=========================================="
    
    # 解析参数
    parse_args "$@"
    
    # 检查环境
    check_environment
    
    # 设置脚本权限
    chmod +x performance-test/collect-docker-stats.sh 2>/dev/null || true
    
    print_info "测试配置:"
    echo "  📋 测试套件: $TEST_SUITE"
    echo "  ⏱️  统计间隔: ${STATS_INTERVAL}秒"
    echo "  📁 输出目录: $OUTPUT_DIR"
    echo "  🏷️  时间戳: $TIMESTAMP"
    
    # 执行测试
    run_test_suite
    
    # 生成综合报告
    generate_comprehensive_report
    
    echo ""
    print_success "🎉 性能测试完成！"
    echo ""
    print_info "生成的文件:"
    ls -lh "$OUTPUT_DIR"/*-$TIMESTAMP.* 2>/dev/null | while read line; do
        echo "  📄 $line"
    done
    
    echo ""
    print_info "建议下一步:"
    echo "  1. 查看综合报告: cat $OUTPUT_DIR/comprehensive-report-$TIMESTAMP.md"
    echo "  2. 分析Docker统计: 使用Excel打开CSV文件"
    echo "  3. 对比历史数据: 建立性能基线"
}

# 执行主函数
main "$@"
