# Saga 分布式事务系统 - 乐观锁 vs 悲观锁性能对比报告

**测试日期**: 2025年8月1日  
**测试环境**: MacBook Pro M3 Max  
**数据量级**: 10万级 (100,000个Saga事务 + 346,666个步骤)  
**对比目标**: 乐观锁实现 vs 悲观锁实现的补偿上报性能差异  

## 📊 执行摘要

本次测试对比了Saga分布式事务系统在乐观锁和悲观锁两种实现方式下的性能表现。重点关注补偿上报接口的性能差异，同时测试了所有5个核心接口的整体性能变化。

### 🎯 关键发现

- **乐观锁补偿上报性能提升26%** - QPS从2,776提升到3,503
- **乐观锁P99延迟显著改善** - 从26.14ms降低到19.11ms  
- **所有接口保持零错误率** - 两种实现都展现出完美的稳定性
- **系统资源使用更加高效** - 乐观锁版本资源消耗更低

## 📈 详细性能对比

### 1. 补偿上报接口对比 ⭐ 核心对比

| 指标 | 悲观锁版本 | 乐观锁版本 | 性能提升 | 评价 |
|------|------------|------------|----------|------|
| **QPS** | 2,776.19 | 3,503.31 | **+26.2%** | 🚀 显著提升 |
| **平均延迟** | 10.21ms | 8.04ms | **-21.3%** | 🚀 显著改善 |
| **P50延迟** | 9.27ms | 7.20ms | **-22.3%** | 🚀 显著改善 |
| **P99延迟** | 26.14ms | 19.11ms | **-26.9%** | 🚀 显著改善 |
| **错误率** | 0% | 0% | 持平 | ✅ 完美稳定 |
| **总请求数** | 83,552 | 105,432 | **+26.2%** | 🚀 处理能力提升 |

**关键洞察**：
- 乐观锁在高并发场景下显著减少了锁等待时间
- P99延迟改善26.9%，说明长尾延迟得到有效控制
- 吞吐量提升26.2%，证明乐观锁更适合读多写少的场景

### 2. 事务创建接口对比

| 指标 | 悲观锁版本 | 乐观锁版本 | 性能变化 | 评价 |
|------|------------|------------|----------|------|
| **QPS** | 7,322.46 | 6,721.98 | -8.2% | ⚠️ 轻微下降 |
| **平均延迟** | 3.82ms | 5.22ms | +36.6% | ⚠️ 延迟增加 |
| **P99延迟** | 29.19ms | 37.68ms | +29.1% | ⚠️ 长尾延迟增加 |
| **错误率** | 0% | 0% | 持平 | ✅ 稳定性保持 |

**分析**：创建接口性能略有下降，可能是乐观锁在写入密集场景下的重试开销。

### 3. 状态查询接口对比

| 指标 | 悲观锁版本 | 乐观锁版本 | 性能变化 | 评价 |
|------|------------|------------|----------|------|
| **QPS** | 13,222.27 | 13,032.45 | -1.4% | ✅ 基本持平 |
| **平均延迟** | 3.89ms | 3.86ms | -0.8% | ✅ 基本持平 |
| **P99延迟** | 11.75ms | 12.41ms | +5.6% | ✅ 轻微变化 |
| **错误率** | 0% | 0% | 持平 | ✅ 稳定性保持 |

**分析**：查询接口性能基本持平，说明乐观锁对读操作影响很小。

### 4. 事务提交接口对比

| 指标 | 悲观锁版本 | 乐观锁版本 | 性能变化 | 评价 |
|------|------------|------------|----------|------|
| **QPS** | 4,822.98 | 5,761.37 | **+19.5%** | 🚀 显著提升 |
| **平均延迟** | 5.66ms | 4.29ms | **-24.2%** | 🚀 显著改善 |
| **P99延迟** | 41.72ms | 33.59ms | **-19.5%** | 🚀 显著改善 |
| **错误率** | 0% | 0% | 持平 | ✅ 稳定性保持 |

**分析**：提交接口性能显著提升，乐观锁减少了状态更新时的锁竞争。

### 5. 事务回滚接口对比

| 指标 | 悲观锁版本 | 乐观锁版本 | 性能变化 | 评价 |
|------|------------|------------|----------|------|
| **QPS** | 6,006.05 | 6,384.37 | **+6.3%** | ✅ 轻微提升 |
| **平均延迟** | 5.25ms | 3.83ms | **-27.0%** | 🚀 显著改善 |
| **P99延迟** | 41.40ms | 26.58ms | **-35.8%** | 🚀 显著改善 |
| **错误率** | 0% | 0% | 持平 | ✅ 稳定性保持 |

**分析**：回滚接口延迟显著改善，特别是P99延迟降低35.8%。

## 🖥️ 服务器性能状态对比

### Docker容器资源使用对比

| 容器 | 指标 | 悲观锁版本 | 乐观锁版本 | 变化 |
|------|------|------------|------------|------|
| **saga-app** | CPU使用率 | 4.50% | 0.33% | **-92.7%** |
| **saga-app** | 内存使用 | 267.9MB | 322MB | +20.2% |
| **saga-mysql** | CPU使用率 | 2.78% | 0.51% | **-81.7%** |
| **saga-mysql** | 内存使用 | 1.808GB | 1.962GB | +8.5% |

**关键发现**：
- 乐观锁版本CPU使用率大幅降低，说明锁竞争减少
- 内存使用略有增加，可能是乐观锁需要更多版本信息

### MySQL性能指标对比

| 指标 | 悲观锁版本 | 乐观锁版本 | 变化 | 分析 |
|------|------------|------------|------|------|
| **总查询数** | 9,005,201 | 14,373,254 | +59.6% | 乐观锁重试导致查询增加 |
| **慢查询数** | 923 | 936 | +1.4% | 基本持平 |
| **连接数** | 199,026 | 305,891 | +53.7% | 更多并发连接 |
| **数据传输** | 接收1GB/发送3.5GB | 接收1.6GB/发送5.6GB | +60%/+60% | 数据传输量增加 |
| **行操作** | 插入109万/更新85万 | 插入186万/更新95万 | +70%/+12% | 更多数据操作 |

## 🔍 深度分析

### 乐观锁优势场景

1. **补偿上报接口** ⭐⭐⭐⭐⭐
   - 性能提升26.2%，最大受益场景
   - 读多写少的特性完美匹配乐观锁

2. **事务提交接口** ⭐⭐⭐⭐
   - 性能提升19.5%，状态更新效率提升
   - 减少了锁等待时间

3. **事务回滚接口** ⭐⭐⭐
   - 延迟改善显著，特别是P99延迟
   - 回滚操作的并发性能提升

### 乐观锁劣势场景

1. **事务创建接口** ⚠️
   - QPS下降8.2%，写入密集场景的重试开销
   - 需要关注高并发创建时的性能

### 资源使用效率

**乐观锁版本优势**：
- CPU使用率大幅降低（应用-92.7%，MySQL-81.7%）
- 锁竞争减少，系统响应更快
- 更好的并发处理能力

**需要关注的点**：
- 查询数量增加59.6%（重试机制）
- 数据传输量增加60%
- 内存使用略有增加

## 📊 综合评分

| 维度 | 悲观锁 | 乐观锁 | 优势方 |
|------|--------|--------|--------|
| **补偿上报性能** | 7/10 | 9/10 | 🏆 乐观锁 |
| **整体吞吐量** | 8/10 | 8/10 | 🤝 持平 |
| **延迟表现** | 7/10 | 8/10 | 🏆 乐观锁 |
| **资源效率** | 7/10 | 9/10 | 🏆 乐观锁 |
| **稳定性** | 10/10 | 10/10 | 🤝 持平 |
| **并发能力** | 7/10 | 9/10 | 🏆 乐观锁 |

**总体评分**: 悲观锁 46/60，乐观锁 53/60

## 🎯 建议与结论

### 推荐使用场景

**乐观锁适用于**：
- 补偿上报为主要业务场景
- 读多写少的应用模式
- 对延迟敏感的系统
- 高并发访问场景

**悲观锁适用于**：
- 写入密集的应用场景
- 对数据一致性要求极高的场景
- 网络不稳定环境（减少重试）

### 优化建议

1. **混合策略**：根据不同接口特性选择不同的锁策略
2. **重试优化**：优化乐观锁的重试机制，减少不必要的查询
3. **缓存策略**：为查询接口添加缓存，进一步提升性能
4. **监控告警**：重点监控乐观锁的重试率和冲突率

### 最终结论

**乐观锁版本在补偿上报场景下表现卓越**，性能提升26.2%，延迟改善26.9%，同时保持了系统的稳定性。建议在当前业务场景下采用乐观锁实现，特别是补偿上报为主要业务流量的情况下。

对于写入密集的场景，可以考虑针对性地使用悲观锁，实现混合锁策略以获得最佳性能。

---

**报告生成时间**: 2025年8月1日  
**测试工具**: wrk + Lua脚本  
**对比基准**: 10万级真实业务数据测试
