加载了 82645 个sagaId用于V2补偿上报测试
加载了 82645 个sagaId用于V2补偿上报测试
加载了 82645 个sagaId用于V2补偿上报测试
Running 15s test @ http://localhost:8080/v2/saga/transactions/compensation
  2 threads and 10 connections
V2业务错误响应: HTTP 200, code=50 - {"code":50,"message":"创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存","data":null}
V2业务错误响应: HTTP 200, code=50 - {"code":50,"message":"创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存","data":null}
V2业务错误响应: HTTP 200, code=50 - {"code":50,"message":"创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存","data":null}
V2业务错误响应: HTTP 200, code=50 - {"code":50,"message":"创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存","data":null}
V2业务错误响应: HTTP 200, code=50 - {"code":50,"message":"创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存","data":null}
V2业务错误响应: HTTP 200, code=50 - {"code":50,"message":"创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存","data":null}
V2业务错误响应: HTTP 200, code=50 - {"code":50,"message":"创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存","data":null}
V2业务错误响应: HTTP 200, code=50 - {"code":50,"message":"创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存","data":null}
V2业务错误响应: HTTP 200, code=50 - {"code":50,"message":"创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存","data":null}
V2业务错误响应: HTTP 200, code=50 - {"code":50,"message":"创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存","data":null}
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency     8.96ms   35.16ms 376.47ms   97.51%
    Req/Sec     1.29k   186.45     1.62k    54.49%
  Latency Distribution
     50%    3.52ms
     75%    4.33ms
     90%    5.69ms
     99%  240.58ms
  38573 requests in 15.09s, 10.08MB read
Requests/sec:   2556.51
Transfer/sec:    683.84KB

=== V2 补偿上报接口性能测试结果 (使用创建的sagaId) ===
使用sagaId数量: 82645
总请求数: 38573
成功请求: 0
错误数: 0
成功率: 0.00%
平均QPS: 2556.51
平均延迟: 8.96ms
P99延迟: 240.58ms
✅ 业务约束: 使用create-saga-v2-test.lua创建的真实sagaId
✅ 唯一性保证: 每个请求使用不同的action+service组合
