加载了 82645 个sagaId用于V2回滚测试
加载了 82645 个sagaId用于V2回滚测试
加载了 82645 个sagaId用于V2回滚测试
Running 15s test @ http://localhost:8080/v2/saga/transactions/rollback
  2 threads and 10 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency     1.76ms  825.63us  25.31ms   91.69%
    Req/Sec     2.90k   384.11     4.14k    61.46%
  Latency Distribution
     50%    1.60ms
     75%    1.97ms
     90%    2.42ms
     99%    4.30ms
  86932 requests in 15.09s, 37.51MB read
Requests/sec:   5762.60
Transfer/sec:      2.49MB

=== V2 事务回滚接口性能测试结果 (使用创建的sagaId) ===
使用sagaId数量: 82645
总请求数: 86932
成功请求: 0
错误数: 0
成功率: 0.00%
平均QPS: 5762.60
平均延迟: 1.76ms
P99延迟: 4.30ms
✅ 业务约束: 使用create-saga-v2-test.lua创建的真实sagaId
