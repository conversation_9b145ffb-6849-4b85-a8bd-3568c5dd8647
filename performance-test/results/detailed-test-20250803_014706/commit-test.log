加载了 82645 个sagaId用于V2提交测试
加载了 82645 个sagaId用于V2提交测试
加载了 82645 个sagaId用于V2提交测试
加载了 82645 个sagaId用于V2提交测试
加载了 82645 个sagaId用于V2提交测试
Running 30s test @ http://localhost:8080/v2/saga/transactions/commit
  4 threads and 20 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency     2.82ms    1.36ms  21.47ms   76.35%
    Req/Sec     1.81k   503.54     2.83k    58.69%
  Latency Distribution
     50%    2.53ms
     75%    3.44ms
     90%    4.53ms
     99%    7.34ms
  217087 requests in 30.09s, 93.80MB read
Requests/sec:   7213.97
Transfer/sec:      3.12MB

=== V2 事务提交接口性能测试结果 (使用创建的sagaId) ===
使用sagaId数量: 82645
总请求数: 217087
成功请求: 0
错误数: 0
成功率: 0.00%
平均QPS: 7213.97
平均延迟: 2.82ms
P99延迟: 7.34ms
✅ 业务约束: 使用create-saga-v2-test.lua创建的真实sagaId
⚠️  注意: 新创建的saga默认为running状态，适合提交测试
