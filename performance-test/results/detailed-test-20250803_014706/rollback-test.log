加载了 82645 个sagaId用于V2回滚测试
加载了 82645 个sagaId用于V2回滚测试
加载了 82645 个sagaId用于V2回滚测试
加载了 82645 个sagaId用于V2回滚测试
加载了 82645 个sagaId用于V2回滚测试
Running 30s test @ http://localhost:8080/v2/saga/transactions/rollback
  4 threads and 20 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency     2.86ms    4.92ms 118.45ms   98.33%
    Req/Sec     2.08k   441.68     2.87k    66.47%
  Latency Distribution
     50%    2.14ms
     75%    2.91ms
     90%    3.96ms
     99%   16.93ms
  244529 requests in 30.10s, 106.56MB read
Requests/sec:   8123.55
Transfer/sec:      3.54MB

=== V2 事务回滚接口性能测试结果 (使用创建的sagaId) ===
使用sagaId数量: 82645
总请求数: 244529
成功请求: 0
错误数: 0
成功率: 0.00%
平均QPS: 8123.55
平均延迟: 2.86ms
P99延迟: 16.93ms
✅ 业务约束: 使用create-saga-v2-test.lua创建的真实sagaId
