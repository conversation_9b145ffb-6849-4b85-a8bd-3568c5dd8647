加载了 82645 个sagaId用于V2状态查询测试
加载了 82645 个sagaId用于V2状态查询测试
加载了 82645 个sagaId用于V2状态查询测试
加载了 82645 个sagaId用于V2状态查询测试
加载了 82645 个sagaId用于V2状态查询测试
加载了 82645 个sagaId用于V2状态查询测试
加载了 82645 个sagaId用于V2状态查询测试
加载了 82645 个sagaId用于V2状态查询测试
加载了 82645 个sagaId用于V2状态查询测试
Running 30s test @ http://localhost:8080
  8 threads and 50 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency     4.03ms    4.96ms 152.21ms   94.56%
    Req/Sec     1.72k   404.46     3.72k    61.46%
  Latency Distribution
     50%    2.96ms
     75%    4.68ms
     90%    7.15ms
     99%   16.61ms
  411989 requests in 30.10s, 167.29MB read
Requests/sec:  13687.52
Transfer/sec:      5.56MB

=== V2 状态查询接口性能测试结果 (使用创建的sagaId) ===
使用sagaId数量: 82645
总请求数: 411989
成功请求: 0
错误数: 0
成功率: 0.00%
平均QPS: 13687.52
平均延迟: 4.03ms
P99延迟: 16.61ms
✅ 业务约束: 使用create-saga-v2-test.lua创建的真实sagaId
