加载了 82645 个sagaId用于V2回滚测试
加载了 82645 个sagaId用于V2回滚测试
V2业务错误响应: HTTP 200, code=51 - {"code":51,"message":"回滚原因不能为空","data":null}
V2业务错误响应: HTTP 200, code=51 - {"code":51,"message":"回滚原因不能为空","data":null}
V2业务错误响应: HTTP 200, code=51 - {"code":51,"message":"回滚原因不能为空","data":null}
V2业务错误响应: HTTP 200, code=51 - {"code":51,"message":"回滚原因不能为空","data":null}
V2业务错误响应: HTTP 200, code=51 - {"code":51,"message":"回滚原因不能为空","data":null}
加载了 82645 个sagaId用于V2回滚测试
Running 15s test @ http://localhost:8080/v2/saga/transactions/rollback
  2 threads and 10 connections
V2业务错误响应: HTTP 200, code=51 - {"code":51,"message":"回滚原因不能为空","data":null}
V2业务错误响应: HTTP 200, code=51 - {"code":51,"message":"回滚原因不能为空","data":null}
V2业务错误响应: HTTP 200, code=51 - {"code":51,"message":"回滚原因不能为空","data":null}
V2业务错误响应: HTTP 200, code=51 - {"code":51,"message":"回滚原因不能为空","data":null}
V2业务错误响应: HTTP 200, code=51 - {"code":51,"message":"回滚原因不能为空","data":null}
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency   609.87us  344.95us  11.96ms   88.88%
    Req/Sec     8.32k     0.90k    9.93k    64.24%
  Latency Distribution
     50%  528.00us
     75%  713.00us
     90%    0.96ms
     99%    1.59ms
  250144 requests in 15.10s, 60.12MB read
Requests/sec:  16565.93
Transfer/sec:      3.98MB

=== V2 事务回滚接口性能测试结果 (使用创建的sagaId) ===
使用sagaId数量: 82645
总请求数: 250144
成功请求: 0
错误数: 0
成功率: 0.00%
平均QPS: 16565.93
平均延迟: 0.61ms
P99延迟: 1.59ms
✅ 业务约束: 使用create-saga-v2-test.lua创建的真实sagaId
