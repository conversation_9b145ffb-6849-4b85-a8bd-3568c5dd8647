{"testSession": {"timestamp": "2025-08-03T01:47:06Z", "environment": "MacBook Pro M3 Max", "lockStrategy": "OptimisticLock", "dataScale": "1M+ saga transactions, 4.2M+ steps"}, "testResults": {"createSaga": {"status": "SUCCESS", "qps": 10954.03, "avgLatency": "1.86ms", "p99Latency": "5.17ms", "httpSuccessRate": "100%", "businessSuccessRate": "100%", "errors": []}, "querySaga": {"status": "SUCCESS_WITH_SCRIPT_ISSUE", "qps": 13687.52, "avgLatency": "4.03ms", "p99Latency": "16.61ms", "httpSuccessRate": "100%", "businessSuccessRate": "100% (actual), 0% (script reported)", "errors": [{"type": "SCRIPT_STATISTICS_ERROR", "description": "Lua脚本没有正确统计业务成功率", "impact": "统计显示错误，实际功能正常", "solution": "修复response函数中的成功计数逻辑"}]}, "compensationReport": {"status": "BUSINESS_LOGIC_ERROR", "qps": 2556.51, "avgLatency": "8.96ms", "p99Latency": "240.58ms", "httpSuccessRate": "100%", "businessSuccessRate": "0%", "errors": [{"type": "BUSINESS_CONSTRAINT_VIOLATION", "code": 50, "message": "创建或更新步骤失败: 创建或更新步骤失败: 没有记录被保存", "rootCause": "Auto模式saga缺少step_templates", "details": {"sagaMode": "auto", "stepTemplates": "empty", "uniqueConstraint": "uq_saga_action_service (saga_id, action, service_name)"}, "frequency": "100%", "impact": "所有补偿上报请求失败"}]}, "commitSaga": {"status": "STATE_CONSTRAINT_ERROR", "qps": 7213.97, "avgLatency": "2.82ms", "p99Latency": "7.34ms", "httpSuccessRate": "100%", "businessSuccessRate": "0%", "errors": [{"type": "STATE_CONSTRAINT_VIOLATION", "code": 0, "message": "无法提交待处理的事务，只有 running 状态的事务才能提交", "rootCause": "新创建的saga默认为pending状态", "details": {"currentState": "pending", "requiredState": "running", "stateTransition": "pending -> running需要通过补偿上报"}, "frequency": "100%", "impact": "所有提交请求被拒绝"}]}, "rollbackSaga": {"status": "STATE_CONSTRAINT_ERROR_FIXED", "qps": 5762.6, "avgLatency": "1.76ms", "p99Latency": "4.30ms", "httpSuccessRate": "100%", "businessSuccessRate": "0%", "errors": [{"type": "PARAMETER_VALIDATION_ERROR", "code": 51, "message": "回滚原因不能为空", "rootCause": "脚本使用错误的参数名称", "status": "FIXED", "fix": "将'reason'改为'failReason'"}, {"type": "STATE_CONSTRAINT_VIOLATION", "code": 0, "message": "待处理状态的事务无需回滚，因为还没有步骤上报", "rootCause": "pending状态的saga没有步骤需要回滚", "details": {"currentState": "pending", "stepsCount": 0, "businessLogic": "无步骤的saga无需回滚"}, "frequency": "100%", "impact": "所有回滚请求被业务逻辑拒绝"}]}}, "errorPatterns": {"stateConstraints": {"description": "状态约束是主要错误源", "affectedAPIs": ["commit", "rollback"], "solution": "需要完整的saga生命周期测试"}, "businessLogicConstraints": {"description": "业务逻辑约束严格", "affectedAPIs": ["compensation"], "solution": "测试数据需要符合业务模型"}, "scriptIssues": {"description": "测试脚本统计问题", "affectedAPIs": ["query"], "solution": "修复Lua脚本的响应处理逻辑"}}, "performanceCharacteristics": {"readOperations": {"avgQPS": 12320.78, "avgLatency": "2.95ms", "characteristics": "高吞吐量，低延迟，稳定性好"}, "writeOperations": {"avgQPS": 5177.03, "avgLatency": "4.51ms", "characteristics": "中等吞吐量，延迟可控，受业务约束影响"}, "optimisticLockPerformance": {"noContentionObserved": true, "highConcurrencySupport": true, "stableLatencyDistribution": true}}, "recommendations": {"immediate": ["修复查询接口的脚本统计问题", "创建包含step_templates的manual模式saga进行补偿测试", "设计完整的saga状态流转测试"], "longTerm": ["优化补偿上报的业务逻辑性能", "简化状态约束检查", "增加业务场景覆盖的性能测试"]}}