开始记录V2创建的sagaId到文件...
开始记录V2创建的sagaId到文件...
开始记录V2创建的sagaId到文件...
开始记录V2创建的sagaId到文件...
Running 30s test @ http://localhost:8080/v2/saga/transactions
  4 threads and 20 connections
  Thread Stats   Avg      Stdev     Max   +/- Stdev
    Latency     1.86ms    1.07ms  34.67ms   87.81%
    Req/Sec     2.76k   383.46     3.88k    60.82%
  Latency Distribution
     50%    1.65ms
     75%    2.19ms
     90%    2.90ms
     99%    5.17ms
  329705 requests in 30.10s, 112.84MB read
Requests/sec:  10954.03
Transfer/sec:      3.75MB

=== V2 Saga事务创建测试结果 ===
总请求数: 329705
成功请求: 329705
错误数: 0
成功率: 100.00%
平均QPS: 10954.03
平均延迟: 1.86ms
P99延迟: 5.17ms
创建的sagaId数量: 0
V2 sagaId已保存到: performance-test/results/created_saga_ids_v2.txt
注意: 成功率基于HTTP状态码，不包括业务逻辑错误(code!=0)的检查
