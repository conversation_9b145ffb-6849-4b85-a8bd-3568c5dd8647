-- 测试compensate_endpoint修改
USE saga;

-- 清空并重新插入业务模板数据
TRUNCATE TABLE business_step_templates;

-- 插入更新后的业务模板数据（包含200, 400, 500状态码）
INSERT INTO business_step_templates (step_order, action, service_name, business_type, compensate_endpoint, description, weight) VALUES
-- 电商订单流程
(1, 'CreateOrder', 'order-service', 'ecommerce', 'https://httpbin.org/status/200', '创建订单记录', 1.0),
(2, 'ProcessPayment', 'payment-service', 'ecommerce', 'https://httpbin.org/status/400', '处理订单支付', 1.5),
(3, 'ReserveInventory', 'inventory-service', 'ecommerce', 'https://httpbin.org/status/200', '预留商品库存', 1.2),
(4, 'SendNotification', 'notification-service', 'ecommerce', 'https://httpbin.org/status/500', '发送订单通知', 0.8),
(5, 'UpdateUserPoints', 'user-service', 'ecommerce', 'https://httpbin.org/status/200', '更新用户积分', 0.9),

-- 支付处理流程
(1, 'ValidateAccount', 'account-service', 'payment', 'https://httpbin.org/status/200', '验证账户状态', 1.1),
(2, 'ProcessPayment', 'payment-service', 'payment', 'https://httpbin.org/status/500', '执行支付操作', 1.8),
(3, 'UpdateBalance', 'account-service', 'payment', 'https://httpbin.org/status/200', '更新账户余额', 1.3),
(4, 'LogTransaction', 'audit-service', 'payment', 'https://httpbin.org/status/400', '记录交易日志', 0.7),
(5, 'SendReceipt', 'notification-service', 'payment', 'https://httpbin.org/status/200', '发送支付收据', 0.6),

-- 库存管理流程
(1, 'CheckInventory', 'inventory-service', 'inventory', 'https://httpbin.org/status/200', '检查库存状态', 1.0),
(2, 'ReserveStock', 'inventory-service', 'inventory', 'https://httpbin.org/status/400', '预留库存数量', 1.4),
(3, 'UpdateWarehouse', 'warehouse-service', 'inventory', 'https://httpbin.org/status/200', '更新仓库系统', 1.1),
(4, 'UpdateCatalog', 'catalog-service', 'inventory', 'https://httpbin.org/status/500', '更新商品目录', 0.9),
(5, 'NotifySupplier', 'supplier-service', 'inventory', 'https://httpbin.org/status/200', '通知供应商', 0.8),

-- 用户注册流程
(1, 'CreateUser', 'user-service', 'user', 'https://httpbin.org/status/200', '创建用户账户', 1.2),
(2, 'SendWelcomeEmail', 'notification-service', 'user', 'https://httpbin.org/status/400', '发送欢迎邮件', 0.5),
(3, 'InitializeProfile', 'profile-service', 'user', 'https://httpbin.org/status/200', '初始化用户档案', 1.0),
(4, 'GrantPermissions', 'auth-service', 'user', 'https://httpbin.org/status/500', '授予基础权限', 0.8),
(5, 'CreateWallet', 'wallet-service', 'user', 'https://httpbin.org/status/200', '创建电子钱包', 1.1),

-- 数据同步流程
(1, 'ExtractData', 'etl-service', 'sync', 'https://httpbin.org/status/200', '提取源数据', 2.0),
(2, 'TransformData', 'etl-service', 'sync', 'https://httpbin.org/status/500', '转换数据格式', 2.5),
(3, 'LoadData', 'data-service', 'sync', 'https://httpbin.org/status/200', '加载目标数据', 1.8),
(4, 'ValidateData', 'validation-service', 'sync', 'https://httpbin.org/status/400', '验证数据完整性', 1.3),
(5, 'NotifyCompletion', 'notification-service', 'sync', 'https://httpbin.org/status/200', '发送完成通知', 0.4);

-- 验证更新结果
SELECT '=== compensate_endpoint更新验证 ===' as verification;

-- 检查总数
SELECT COUNT(*) as total_templates FROM business_step_templates;

-- 检查所有compensate_endpoint是否都已更新
SELECT 
    DISTINCT compensate_endpoint,
    COUNT(*) as count
FROM business_step_templates 
GROUP BY compensate_endpoint;

-- 验证每个业务类型的模板数量
SELECT 
    business_type,
    COUNT(*) as template_count
FROM business_step_templates 
GROUP BY business_type 
ORDER BY business_type;

-- 显示部分数据样例
SELECT 
    business_type,
    step_order,
    action,
    service_name,
    compensate_endpoint,
    description
FROM business_step_templates 
WHERE business_type = 'ecommerce'
ORDER BY step_order;

-- 验证状态码分布
SELECT '=== 状态码分布验证 ===' as status_code_distribution;
SELECT
    CASE
        WHEN compensate_endpoint LIKE '%/200' THEN '200 (成功)'
        WHEN compensate_endpoint LIKE '%/400' THEN '400 (客户端错误)'
        WHEN compensate_endpoint LIKE '%/500' THEN '500 (服务器错误)'
        ELSE '其他'
    END as status_code,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM business_step_templates), 2) as percentage
FROM business_step_templates
GROUP BY
    CASE
        WHEN compensate_endpoint LIKE '%/200' THEN '200 (成功)'
        WHEN compensate_endpoint LIKE '%/400' THEN '400 (客户端错误)'
        WHEN compensate_endpoint LIKE '%/500' THEN '500 (服务器错误)'
        ELSE '其他'
    END
ORDER BY count DESC;

SELECT '✅ compensate_endpoint更新完成！包含200, 400, 500三种状态码' as completion_message;
