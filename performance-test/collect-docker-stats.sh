#!/bin/bash

# Saga 分布式事务系统 - Docker容器性能统计收集脚本
# 用于压测期间收集MySQL和应用容器的资源使用情况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 默认配置
INTERVAL=${STATS_INTERVAL:-5}  # 采集间隔(秒)
DURATION=${STATS_DURATION:-300}  # 采集时长(秒)
OUTPUT_DIR="performance-test/results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 容器名称
MYSQL_CONTAINER="saga-mysql"
APP_CONTAINER="saga-app"

# 输出文件
STATS_FILE="$OUTPUT_DIR/docker-stats-$TIMESTAMP.csv"
MYSQL_STATS_FILE="$OUTPUT_DIR/mysql-stats-$TIMESTAMP.txt"
SUMMARY_FILE="$OUTPUT_DIR/performance-summary-$TIMESTAMP.md"

# 显示帮助信息
show_help() {
    echo "Docker容器性能统计收集工具"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -i, --interval SECONDS   采集间隔，默认5秒"
    echo "  -d, --duration SECONDS   采集时长，默认300秒(5分钟)"
    echo "  -h, --help              显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  STATS_INTERVAL          采集间隔(秒)"
    echo "  STATS_DURATION          采集时长(秒)"
    echo ""
    echo "示例:"
    echo "  $0                      # 使用默认配置(5秒间隔，5分钟)"
    echo "  $0 -i 2 -d 600          # 2秒间隔，10分钟"
    echo "  STATS_INTERVAL=1 $0     # 1秒间隔，默认时长"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -i|--interval)
                INTERVAL="$2"
                shift 2
                ;;
            -d|--duration)
                DURATION="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查Docker容器状态
check_containers() {
    print_info "检查Docker容器状态..."
    
    if ! docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(saga-mysql|saga-app)"; then
        print_error "未找到运行中的Saga容器"
        echo "请先启动服务: make up 或 make up-perf"
        exit 1
    fi
    
    # 检查具体容器
    local missing_containers=()
    
    if ! docker ps | grep -q "$MYSQL_CONTAINER"; then
        missing_containers+=("$MYSQL_CONTAINER")
    fi
    
    if ! docker ps | grep -q "$APP_CONTAINER"; then
        missing_containers+=("$APP_CONTAINER")
    fi
    
    if [ ${#missing_containers[@]} -gt 0 ]; then
        print_warning "以下容器未运行: ${missing_containers[*]}"
        print_warning "将只收集运行中容器的统计数据"
    fi
    
    print_success "容器状态检查完成"
}

# 创建输出目录
setup_output() {
    mkdir -p "$OUTPUT_DIR"
    
    print_info "输出文件配置:"
    echo "  📊 Docker统计: $STATS_FILE"
    echo "  🗄️  MySQL统计: $MYSQL_STATS_FILE"
    echo "  📋 汇总报告: $SUMMARY_FILE"
    echo "  ⏱️  采集间隔: ${INTERVAL}秒"
    echo "  ⏱️  采集时长: ${DURATION}秒"
}

# 收集Docker stats
collect_docker_stats() {
    print_info "开始收集Docker容器统计数据..."
    
    # CSV头部
    echo "timestamp,container,cpu_percent,memory_usage,memory_limit,memory_percent,network_io_rx,network_io_tx,block_io_read,block_io_write,pids" > "$STATS_FILE"
    
    local end_time=$(($(date +%s) + DURATION))
    local count=0
    
    while [ $(date +%s) -lt $end_time ]; do
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        
        # 收集运行中容器的统计数据
        for container in $MYSQL_CONTAINER $APP_CONTAINER; do
            if docker ps | grep -q "$container"; then
                # 获取容器统计数据
                local stats=$(docker stats --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}\t{{.PIDs}}" "$container" 2>/dev/null | tail -n 1)
                
                if [ -n "$stats" ]; then
                    # 解析统计数据
                    local cpu_percent=$(echo "$stats" | awk '{print $1}' | sed 's/%//')
                    local mem_usage=$(echo "$stats" | awk '{print $2}' | sed 's/MiB.*//')
                    local mem_limit=$(echo "$stats" | awk '{print $4}' | sed 's/GiB//')
                    local mem_percent=$(echo "$stats" | awk '{print $5}' | sed 's/%//')
                    local net_io=$(echo "$stats" | awk '{print $6}')
                    local net_rx=$(echo "$net_io" | cut -d'/' -f1 | sed 's/[^0-9.]//g')
                    local net_tx=$(echo "$net_io" | cut -d'/' -f2 | sed 's/[^0-9.]//g')
                    local block_io=$(echo "$stats" | awk '{print $7}')
                    local block_read=$(echo "$block_io" | cut -d'/' -f1 | sed 's/[^0-9.]//g')
                    local block_write=$(echo "$block_io" | cut -d'/' -f2 | sed 's/[^0-9.]//g')
                    local pids=$(echo "$stats" | awk '{print $8}')
                    
                    # 写入CSV
                    echo "$timestamp,$container,$cpu_percent,$mem_usage,$mem_limit,$mem_percent,$net_rx,$net_tx,$block_read,$block_write,$pids" >> "$STATS_FILE"
                fi
            fi
        done
        
        count=$((count + 1))
        if [ $((count % 12)) -eq 0 ]; then  # 每分钟显示一次进度
            local elapsed=$((count * INTERVAL))
            print_info "已采集 ${elapsed}秒 / ${DURATION}秒"
        fi
        
        sleep "$INTERVAL"
    done
    
    print_success "Docker统计数据收集完成"
}

# 收集MySQL性能数据
collect_mysql_stats() {
    if ! docker ps | grep -q "$MYSQL_CONTAINER"; then
        print_warning "MySQL容器未运行，跳过MySQL统计收集"
        return
    fi
    
    print_info "收集MySQL性能统计数据..."
    
    {
        echo "=== MySQL性能统计报告 ==="
        echo "收集时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo ""
        
        echo "=== 数据库状态 ==="
        docker exec "$MYSQL_CONTAINER" mysql -u root -p12345678a -e "
        SELECT 
            VARIABLE_NAME as '指标',
            VARIABLE_VALUE as '值'
        FROM performance_schema.global_status 
        WHERE VARIABLE_NAME IN (
            'Threads_connected',
            'Threads_running', 
            'Queries',
            'Questions',
            'Slow_queries',
            'Connections',
            'Aborted_connects',
            'Bytes_received',
            'Bytes_sent',
            'Innodb_buffer_pool_reads',
            'Innodb_buffer_pool_read_requests',
            'Innodb_buffer_pool_pages_dirty',
            'Innodb_buffer_pool_pages_free',
            'Innodb_rows_read',
            'Innodb_rows_inserted',
            'Innodb_rows_updated',
            'Innodb_rows_deleted'
        )
        ORDER BY VARIABLE_NAME;
        " 2>/dev/null || echo "MySQL状态查询失败"
        
        echo ""
        echo "=== 连接信息 ==="
        docker exec "$MYSQL_CONTAINER" mysql -u root -p12345678a -e "SHOW PROCESSLIST;" 2>/dev/null | head -20 || echo "连接信息查询失败"
        
        echo ""
        echo "=== InnoDB状态 ==="
        docker exec "$MYSQL_CONTAINER" mysql -u root -p12345678a -e "SHOW ENGINE INNODB STATUS\G" 2>/dev/null | head -50 || echo "InnoDB状态查询失败"
        
        echo ""
        echo "=== 表统计信息 ==="
        docker exec "$MYSQL_CONTAINER" mysql -u root -p12345678a -e "
        USE saga;
        SELECT 
            table_name as '表名',
            table_rows as '行数',
            ROUND(data_length/1024/1024, 2) as '数据大小(MB)',
            ROUND(index_length/1024/1024, 2) as '索引大小(MB)',
            ROUND((data_length + index_length)/1024/1024, 2) as '总大小(MB)'
        FROM information_schema.tables 
        WHERE table_schema = 'saga'
        ORDER BY (data_length + index_length) DESC;
        " 2>/dev/null || echo "表统计信息查询失败"
        
    } > "$MYSQL_STATS_FILE"
    
    print_success "MySQL统计数据收集完成"
}

# 生成性能汇总报告
generate_summary() {
    print_info "生成性能汇总报告..."
    
    {
        echo "# Docker容器性能统计汇总报告"
        echo ""
        echo "**收集时间**: $(date '+%Y年%m月%d日 %H:%M:%S')"
        echo "**采集间隔**: ${INTERVAL}秒"
        echo "**采集时长**: ${DURATION}秒"
        echo "**数据文件**: "
        echo "- Docker统计: \`$(basename "$STATS_FILE")\`"
        echo "- MySQL统计: \`$(basename "$MYSQL_STATS_FILE")\`"
        echo ""
        
        echo "## 📊 容器资源使用概览"
        echo ""
        
        # 分析Docker统计数据
        if [ -f "$STATS_FILE" ] && [ -s "$STATS_FILE" ]; then
            echo "### MySQL容器 ($MYSQL_CONTAINER)"
            echo ""
            
            # MySQL统计
            local mysql_stats=$(grep "$MYSQL_CONTAINER" "$STATS_FILE" 2>/dev/null || echo "")
            if [ -n "$mysql_stats" ]; then
                local avg_cpu=$(echo "$mysql_stats" | awk -F',' '{sum+=$3; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
                local max_cpu=$(echo "$mysql_stats" | awk -F',' '{if($3>max) max=$3} END {printf "%.2f", max}')
                local avg_mem=$(echo "$mysql_stats" | awk -F',' '{sum+=$4; count++} END {if(count>0) printf "%.0f", sum/count; else print "N/A"}')
                local max_mem=$(echo "$mysql_stats" | awk -F',' '{if($4>max) max=$4} END {printf "%.0f", max}')
                
                echo "| 指标 | 平均值 | 峰值 |"
                echo "|------|--------|------|"
                echo "| CPU使用率 | ${avg_cpu}% | ${max_cpu}% |"
                echo "| 内存使用 | ${avg_mem}MB | ${max_mem}MB |"
            else
                echo "❌ 未收集到MySQL容器数据"
            fi
            
            echo ""
            echo "### 应用容器 ($APP_CONTAINER)"
            echo ""
            
            # 应用统计
            local app_stats=$(grep "$APP_CONTAINER" "$STATS_FILE" 2>/dev/null || echo "")
            if [ -n "$app_stats" ]; then
                local avg_cpu=$(echo "$app_stats" | awk -F',' '{sum+=$3; count++} END {if(count>0) printf "%.2f", sum/count; else print "N/A"}')
                local max_cpu=$(echo "$app_stats" | awk -F',' '{if($3>max) max=$3} END {printf "%.2f", max}')
                local avg_mem=$(echo "$app_stats" | awk -F',' '{sum+=$4; count++} END {if(count>0) printf "%.0f", sum/count; else print "N/A"}')
                local max_mem=$(echo "$app_stats" | awk -F',' '{if($4>max) max=$4} END {printf "%.0f", max}')
                
                echo "| 指标 | 平均值 | 峰值 |"
                echo "|------|--------|------|"
                echo "| CPU使用率 | ${avg_cpu}% | ${max_cpu}% |"
                echo "| 内存使用 | ${avg_mem}MB | ${max_mem}MB |"
            else
                echo "❌ 未收集到应用容器数据"
            fi
        else
            echo "❌ Docker统计数据文件为空或不存在"
        fi
        
        echo ""
        echo "## 🗄️ MySQL性能指标"
        echo ""
        echo "详细的MySQL性能数据请查看: \`$(basename "$MYSQL_STATS_FILE")\`"
        echo ""
        
        echo "## 📈 数据分析建议"
        echo ""
        echo "### 性能优化方向"
        echo ""
        echo "1. **CPU使用率分析**"
        echo "   - 如果MySQL CPU使用率持续>80%，考虑优化查询或增加索引"
        echo "   - 如果应用CPU使用率过高，检查业务逻辑和并发处理"
        echo ""
        echo "2. **内存使用分析**"
        echo "   - MySQL内存使用应保持在合理范围内"
        echo "   - 应用内存如果持续增长，检查是否有内存泄漏"
        echo ""
        echo "3. **网络I/O分析**"
        echo "   - 监控网络吞吐量，评估是否成为瓶颈"
        echo "   - 大量网络I/O可能表明需要优化数据传输"
        echo ""
        echo "## 📋 使用说明"
        echo ""
        echo "### 数据文件说明"
        echo ""
        echo "- **$(basename "$STATS_FILE")**: CSV格式的容器统计数据，可用Excel或其他工具分析"
        echo "- **$(basename "$MYSQL_STATS_FILE")**: MySQL详细性能数据，包含连接、查询、InnoDB状态等"
        echo ""
        echo "### 后续分析建议"
        echo ""
        echo "1. 使用图表工具(如Excel、Python matplotlib)可视化CPU和内存使用趋势"
        echo "2. 对比不同压测场景下的资源使用情况"
        echo "3. 结合应用性能数据(QPS、延迟)进行综合分析"
        echo "4. 建立性能基线，用于后续优化效果对比"
        echo ""
        echo "---"
        echo "**报告生成时间**: $(date '+%Y-%m-%d %H:%M:%S')"
        
    } > "$SUMMARY_FILE"
    
    print_success "性能汇总报告生成完成"
}

# 主函数
main() {
    echo "🚀 Docker容器性能统计收集工具"
    echo "=================================="
    
    # 解析参数
    parse_args "$@"
    
    # 检查环境
    check_containers
    
    # 设置输出
    setup_output
    
    print_info "开始性能数据收集..."
    echo "按 Ctrl+C 可提前结束收集"
    
    # 设置信号处理
    trap 'print_warning "收到中断信号，正在生成报告..."; generate_summary; exit 0' INT
    
    # 并行收集数据
    collect_docker_stats &
    local docker_pid=$!
    
    # 等待Docker统计收集完成
    wait $docker_pid
    
    # 收集MySQL数据
    collect_mysql_stats
    
    # 生成汇总报告
    generate_summary
    
    echo ""
    print_success "🎉 性能数据收集完成！"
    echo ""
    print_info "生成的文件:"
    echo "  📊 $(ls -lh "$STATS_FILE" | awk '{print $5}') - $STATS_FILE"
    echo "  🗄️  $(ls -lh "$MYSQL_STATS_FILE" | awk '{print $5}') - $MYSQL_STATS_FILE"
    echo "  📋 $(ls -lh "$SUMMARY_FILE" | awk '{print $5}') - $SUMMARY_FILE"
    echo ""
    print_info "建议:"
    echo "  1. 查看汇总报告: cat $SUMMARY_FILE"
    echo "  2. 分析CSV数据: 使用Excel或Python pandas"
    echo "  3. 对比不同测试场景的性能数据"
}

# 执行主函数
main "$@"
