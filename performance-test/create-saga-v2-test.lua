-- V2 Saga事务创建性能测试脚本
-- 支持锁策略切换和性能对比

wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 请求计数器和统计
counter = 0
created_saga_ids = {}
saga_file = nil
success_count = 0
error_count = 0

-- 初始化函数
function init(args)
    -- 确保结果目录存在
    os.execute("mkdir -p performance-test/results")
    
    -- 打开文件用于保存sagaId
    saga_file = io.open("performance-test/results/created_saga_ids_v2.txt", "w")
    if saga_file then
        print("开始记录V2创建的sagaId到文件...")
    else
        print("警告: 无法打开sagaId文件进行写入")
    end
end

-- 请求生成函数
function request()
    counter = counter + 1
    local name = "V2性能测试事务-" .. counter
    local body = string.format([[{
        "name": "%s",
        "stepIndexMode": "auto",
        "stepTemplates": []
    }]], name)
    
    return wrk.format("POST", nil, nil, body)
end

-- 响应处理
function response(status, headers, body)
    if status == 200 then
        -- 解析响应体检查code字段
        local response_code = body:match('"code":(%d+)')
        if response_code and tonumber(response_code) == 0 then
            success_count = success_count + 1
            -- 解析响应获取sagaId
            local saga_id = body:match('"sagaId":"([^"]+)"')
            if saga_id and saga_file then
                saga_file:write(saga_id .. "\n")
                saga_file:flush() -- 立即写入文件
                table.insert(created_saga_ids, saga_id)
            end
        else
            error_count = error_count + 1
            if error_count <= 3 then
                print("V2业务错误响应: HTTP " .. status .. ", code=" .. (response_code or "unknown"))
                print("响应体前200字符: " .. body:sub(1, 200))
            end
        end
    else
        error_count = error_count + 1
        if error_count <= 3 then
            print("V2 HTTP错误响应: " .. status .. " - " .. body:sub(1, 200))
        end
    end
end

-- 测试完成处理
function done(summary, latency, requests)
    -- 关闭文件
    if saga_file then
        saga_file:close()
    end
    
    print("\n=== V2 Saga事务创建测试结果 ===")
    print(string.format("总请求数: %d", summary.requests))
    
    -- 计算成功请求数（总请求数减去各种错误）
    local total_errors = summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout
    local success_requests = summary.requests - total_errors
    
    print(string.format("成功请求: %d", success_requests))
    print(string.format("错误数: %d", total_errors))
    if summary.requests > 0 then
        print(string.format("成功率: %.2f%%", (success_requests / summary.requests) * 100))
    end
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
    print(string.format("创建的sagaId数量: %d", #created_saga_ids))
    print("V2 sagaId已保存到: performance-test/results/created_saga_ids_v2.txt")
    print("注意: 成功率基于HTTP状态码，不包括业务逻辑错误(code!=0)的检查")
end
