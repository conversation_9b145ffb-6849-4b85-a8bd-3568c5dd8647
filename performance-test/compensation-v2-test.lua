-- V2 补偿上报性能测试脚本 (优化版 - 保证唯一性)
-- 支持锁策略切换和性能对比
-- 唯一性策略: 使用counter生成唯一的action+service组合，避免数据库约束冲突

wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 加载真实的sagaId
local saga_ids = {}
local file = io.open("performance-test/results/created_saga_ids_v2.txt", "r")
if file then
    for line in file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    file:close()
else
    -- 如果V2文件不存在，尝试使用V1文件
    file = io.open("performance-test/results/created_saga_ids.txt", "r")
    if file then
        for line in file:lines() do
            if line and line ~= "" then
                table.insert(saga_ids, line)
            end
        end
        file:close()
    end
end

print("加载了 " .. #saga_ids .. " 个sagaId用于V2补偿上报测试 (唯一性优化版)")

-- 唯一性策略: 不使用固定模板，而是基于counter生成唯一组合

counter = 0
success_count = 0
error_count = 0

function request()
    if #saga_ids == 0 then
        return wrk.format("POST", nil, nil, '{"error": "no saga ids available"}')
    end

    counter = counter + 1

    -- 使用counter循环选择sagaId，确保分布均匀
    local saga_id = saga_ids[(counter % #saga_ids) + 1]

    -- 生成绝对唯一的action和service组合 (使用counter确保全局唯一性)
    -- 这样可以避免数据库唯一约束 uq_saga_action_service 冲突
    local action = "V2PerfTestAction" .. counter
    local service = "v2-perf-test-service-" .. (counter % 1000)  -- 增加service的多样性

    -- 生成对应的补偿端点
    local endpoint = string.format("http://%s:8080/saga/compensate/%s", service, action)

    -- 生成符合业务逻辑的补偿上报数据
    local body = string.format([[{
        "sagaId": "%s",
        "action": "%s",
        "serviceName": "%s",
        "contextData": "{\"orderId\":\"ORDER-%d\",\"userId\":\"user-%d\",\"amount\":%.2f,\"timestamp\":%d}",
        "compensationContext": "{\"sagaId\":\"%s\",\"action\":\"%s\",\"reason\":\"v2_performance_test\",\"counter\":%d}",
        "compensateEndpoint": "%s"
    }]],
        saga_id,
        action,
        service,
        counter,
        counter % 10000,
        99.99 + (counter % 900),
        os.time(),
        saga_id,
        action,
        counter,
        endpoint
    )

    return wrk.format("POST", nil, nil, body)
end

-- 响应处理函数 (最终修复 - 只有HTTP 200 + code 0 = 成功)
function response(status, headers, body)
    if status == 200 then
        -- 解析响应体检查code字段
        local response_code = body:match('"code":(%d+)')
        if response_code and tonumber(response_code) == 0 then
            -- 只有HTTP 200 + code 0 = 成功
            success_count = success_count + 1
        else
            error_count = error_count + 1
            if error_count <= 5 then
                print("V2业务错误响应: HTTP " .. status .. ", code=" .. (response_code or "unknown") .. " - " .. body:sub(1, 200))
            end
        end
    else
        error_count = error_count + 1
        if error_count <= 5 then
            print("V2 HTTP错误响应: " .. status .. " - " .. body:sub(1, 200))
        end
    end
end

function done(summary, latency, requests)
    print("\n=== V2 补偿上报接口性能测试结果 (唯一性优化版) ===")
    print(string.format("使用sagaId数量: %d", #saga_ids))
    print(string.format("总请求数: %d", summary.requests))

    -- 使用wrk内置统计，参考create-saga-v2-test.lua的方法
    local total_errors = summary.errors.connect + summary.errors.read + summary.errors.write + summary.errors.status + summary.errors.timeout
    local success_requests = summary.requests - total_errors

    print(string.format("成功请求: %d", success_requests))
    print(string.format("错误数: %d", total_errors))
    print(string.format("业务成功: %d (code=0)", success_count))
    print(string.format("业务错误: %d (code!=0)", error_count))
    if summary.requests > 0 then
        print(string.format("HTTP成功率: %.2f%%", (success_requests / summary.requests) * 100))
    end
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
    print("✅ 业务约束: 使用create-saga-v2-test.lua创建的真实sagaId")
    print("✅ 唯一性保证: 使用counter生成绝对唯一的action+service组合")
    print("✅ 数据库约束: 避免uq_saga_action_service唯一约束冲突")
    print("✅ 统计修复: 参考create-saga-v2-test.lua的统计方法")
end
