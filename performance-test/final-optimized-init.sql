-- 最终优化版本的数据初始化脚本
-- 确保所有数据比例正确，分批插入优化

USE saga;

-- 清空现有数据
TRUNCATE TABLE saga_transactions;
TRUNCATE TABLE saga_steps;

-- 设置变量
SET @batch_size = 100000;  -- 每批10万行
SET @total_sagas = 1000000; -- 总共100万条
SET @processed_sagas = 0;

-- 优化设置
SET SESSION bulk_insert_buffer_size = 268435456;
SET SESSION read_buffer_size = 2097152;

-- 创建临时表
CREATE TEMPORARY TABLE temp_saga_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    saga_id CHAR(36) NOT NULL,
    name VARCHAR(64) NOT NULL,
    saga_status ENUM('pending', 'running', 'completed', 'compensating', 'failed') NOT NULL,
    step_index_mode ENUM('manual', 'auto') NOT NULL,
    steps_count INT NOT NULL,
    business_type VARCHAR(20) NOT NULL
);

SELECT CONCAT('开始生成 ', @total_sagas, ' 个Saga事务，批次大小: ', @batch_size) as start_info;

-- 批次1: 1-100000
SELECT '开始处理批次 1/10 (1-100000)' as batch_info;
TRUNCATE TABLE temp_saga_data;
INSERT INTO temp_saga_data (saga_id, name, saga_status, step_index_mode, steps_count, business_type)
SELECT
    CONCAT(SUBSTRING(MD5(CONCAT('saga', n, UNIX_TIMESTAMP(), 1)), 1, 8), '-',
           SUBSTRING(MD5(CONCAT('test', n, RAND(), 1)), 1, 4), '-4000-',
           SUBSTRING(MD5(CONCAT('data', n, NOW(), 1)), 1, 4), '-',
           SUBSTRING(MD5(CONCAT('init', n, CONNECTION_ID(), 1)), 1, 12)) as saga_id,
    CASE (n % 5)
        WHEN 0 THEN CONCAT('电商订单流程-', LPAD(n, 7, '0'))
        WHEN 1 THEN CONCAT('支付处理流程-', LPAD(n, 7, '0'))
        WHEN 2 THEN CONCAT('库存管理流程-', LPAD(n, 7, '0'))
        WHEN 3 THEN CONCAT('用户注册流程-', LPAD(n, 7, '0'))
        ELSE CONCAT('数据同步流程-', LPAD(n, 7, '0'))
    END as name,
    -- 正确的状态分布：60% running, 20% completed, 10% pending, 5% compensating, 5% failed
    CASE
        WHEN (n % 100) < 60 THEN 'running'
        WHEN (n % 100) < 80 THEN 'completed'
        WHEN (n % 100) < 90 THEN 'pending'
        WHEN (n % 100) < 95 THEN 'compensating'
        ELSE 'failed'
    END as saga_status,
    -- 步骤索引模式：25% manual, 75% auto
    CASE WHEN (n % 4) = 0 THEN 'manual' ELSE 'auto' END as step_index_mode,
    -- 步骤数量分布：20% 3步, 40% 4步, 40% 5步
    CASE
        WHEN (n % 10) < 2 THEN 3
        WHEN (n % 10) < 6 THEN 4
        ELSE 5
    END as steps_count,
    -- 业务类型分布：各20%
    CASE (n % 5)
        WHEN 0 THEN 'ecommerce'
        WHEN 1 THEN 'payment'
        WHEN 2 THEN 'inventory'
        WHEN 3 THEN 'user'
        ELSE 'sync'
    END as business_type
FROM (
    SELECT a.N + b.N * 10 + c.N * 100 + d.N * 1000 + e.N * 10000 as n
    FROM
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d,
        (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) e
) numbers
WHERE n < @batch_size;

START TRANSACTION;
INSERT INTO saga_transactions (saga_id, name, saga_status, step_index_mode, cur_step_index, compensation_window_sec, created_at, updated_at)
SELECT saga_id, name, saga_status, step_index_mode,
    CASE WHEN step_index_mode = 'auto' THEN steps_count ELSE 0 END,
    CASE business_type WHEN 'ecommerce' THEN 300 WHEN 'payment' THEN 600 WHEN 'inventory' THEN 180 WHEN 'user' THEN 120 ELSE 240 END,
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7200) SECOND),
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 3600) SECOND)
FROM temp_saga_data ORDER BY id;
COMMIT;
SET @processed_sagas = @processed_sagas + ROW_COUNT();
SELECT CONCAT('批次 1 完成: ', @processed_sagas, '/', @total_sagas) as progress;

-- 批次2-10 (紧凑格式)
SET @offset = 100000; TRUNCATE TABLE temp_saga_data; INSERT INTO temp_saga_data (saga_id, name, saga_status, step_index_mode, steps_count, business_type) SELECT CONCAT(SUBSTRING(MD5(CONCAT('saga', n + @offset, UNIX_TIMESTAMP(), 2)), 1, 8), '-', SUBSTRING(MD5(CONCAT('test', n + @offset, RAND(), 2)), 1, 4), '-4000-', SUBSTRING(MD5(CONCAT('data', n + @offset, NOW(), 2)), 1, 4), '-', SUBSTRING(MD5(CONCAT('init', n + @offset, CONNECTION_ID(), 2)), 1, 12)) as saga_id, CASE ((n + @offset) % 5) WHEN 0 THEN CONCAT('电商订单流程-', LPAD(n + @offset, 7, '0')) WHEN 1 THEN CONCAT('支付处理流程-', LPAD(n + @offset, 7, '0')) WHEN 2 THEN CONCAT('库存管理流程-', LPAD(n + @offset, 7, '0')) WHEN 3 THEN CONCAT('用户注册流程-', LPAD(n + @offset, 7, '0')) ELSE CONCAT('数据同步流程-', LPAD(n + @offset, 7, '0')) END as name, CASE WHEN ((n + @offset) % 100) < 60 THEN 'running' WHEN ((n + @offset) % 100) < 80 THEN 'completed' WHEN ((n + @offset) % 100) < 90 THEN 'pending' WHEN ((n + @offset) % 100) < 95 THEN 'compensating' ELSE 'failed' END as saga_status, CASE WHEN ((n + @offset) % 4) = 0 THEN 'manual' ELSE 'auto' END as step_index_mode, CASE WHEN ((n + @offset) % 10) < 2 THEN 3 WHEN ((n + @offset) % 10) < 6 THEN 4 ELSE 5 END as steps_count, CASE ((n + @offset) % 5) WHEN 0 THEN 'ecommerce' WHEN 1 THEN 'payment' WHEN 2 THEN 'inventory' WHEN 3 THEN 'user' ELSE 'sync' END as business_type FROM (SELECT a.N + b.N * 10 + c.N * 100 + d.N * 1000 + e.N * 10000 as n FROM (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) e) numbers WHERE n < @batch_size; START TRANSACTION; INSERT INTO saga_transactions (saga_id, name, saga_status, step_index_mode, cur_step_index, compensation_window_sec, created_at, updated_at) SELECT saga_id, name, saga_status, step_index_mode, CASE WHEN step_index_mode = 'auto' THEN steps_count ELSE 0 END, CASE business_type WHEN 'ecommerce' THEN 300 WHEN 'payment' THEN 600 WHEN 'inventory' THEN 180 WHEN 'user' THEN 120 ELSE 240 END, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7200) SECOND), DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 3600) SECOND) FROM temp_saga_data ORDER BY id; COMMIT; SET @processed_sagas = @processed_sagas + ROW_COUNT(); SELECT CONCAT('批次 2 完成: ', @processed_sagas, '/', @total_sagas) as progress;

-- 继续批次3-10 (使用相同模式)
SET @offset = 200000; TRUNCATE TABLE temp_saga_data; INSERT INTO temp_saga_data (saga_id, name, saga_status, step_index_mode, steps_count, business_type) SELECT CONCAT(SUBSTRING(MD5(CONCAT('saga', n + @offset, UNIX_TIMESTAMP(), 3)), 1, 8), '-', SUBSTRING(MD5(CONCAT('test', n + @offset, RAND(), 3)), 1, 4), '-4000-', SUBSTRING(MD5(CONCAT('data', n + @offset, NOW(), 3)), 1, 4), '-', SUBSTRING(MD5(CONCAT('init', n + @offset, CONNECTION_ID(), 3)), 1, 12)) as saga_id, CASE ((n + @offset) % 5) WHEN 0 THEN CONCAT('电商订单流程-', LPAD(n + @offset, 7, '0')) WHEN 1 THEN CONCAT('支付处理流程-', LPAD(n + @offset, 7, '0')) WHEN 2 THEN CONCAT('库存管理流程-', LPAD(n + @offset, 7, '0')) WHEN 3 THEN CONCAT('用户注册流程-', LPAD(n + @offset, 7, '0')) ELSE CONCAT('数据同步流程-', LPAD(n + @offset, 7, '0')) END as name, CASE WHEN ((n + @offset) % 100) < 60 THEN 'running' WHEN ((n + @offset) % 100) < 80 THEN 'completed' WHEN ((n + @offset) % 100) < 90 THEN 'pending' WHEN ((n + @offset) % 100) < 95 THEN 'compensating' ELSE 'failed' END as saga_status, CASE WHEN ((n + @offset) % 4) = 0 THEN 'manual' ELSE 'auto' END as step_index_mode, CASE WHEN ((n + @offset) % 10) < 2 THEN 3 WHEN ((n + @offset) % 10) < 6 THEN 4 ELSE 5 END as steps_count, CASE ((n + @offset) % 5) WHEN 0 THEN 'ecommerce' WHEN 1 THEN 'payment' WHEN 2 THEN 'inventory' WHEN 3 THEN 'user' ELSE 'sync' END as business_type FROM (SELECT a.N + b.N * 10 + c.N * 100 + d.N * 1000 + e.N * 10000 as n FROM (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) e) numbers WHERE n < @batch_size; START TRANSACTION; INSERT INTO saga_transactions (saga_id, name, saga_status, step_index_mode, cur_step_index, compensation_window_sec, created_at, updated_at) SELECT saga_id, name, saga_status, step_index_mode, CASE WHEN step_index_mode = 'auto' THEN steps_count ELSE 0 END, CASE business_type WHEN 'ecommerce' THEN 300 WHEN 'payment' THEN 600 WHEN 'inventory' THEN 180 WHEN 'user' THEN 120 ELSE 240 END, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7200) SECOND), DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 3600) SECOND) FROM temp_saga_data ORDER BY id; COMMIT; SET @processed_sagas = @processed_sagas + ROW_COUNT(); SELECT CONCAT('批次 3 完成: ', @processed_sagas, '/', @total_sagas) as progress;

-- 批次4-10 (剩余700万条，使用大批次)
SET @offset = 300000; TRUNCATE TABLE temp_saga_data; INSERT INTO temp_saga_data (saga_id, name, saga_status, step_index_mode, steps_count, business_type) SELECT CONCAT(SUBSTRING(MD5(CONCAT('saga', n + @offset, UNIX_TIMESTAMP(), 4)), 1, 8), '-', SUBSTRING(MD5(CONCAT('test', n + @offset, RAND(), 4)), 1, 4), '-4000-', SUBSTRING(MD5(CONCAT('data', n + @offset, NOW(), 4)), 1, 4), '-', SUBSTRING(MD5(CONCAT('init', n + @offset, CONNECTION_ID(), 4)), 1, 12)) as saga_id, CASE ((n + @offset) % 5) WHEN 0 THEN CONCAT('电商订单流程-', LPAD(n + @offset, 7, '0')) WHEN 1 THEN CONCAT('支付处理流程-', LPAD(n + @offset, 7, '0')) WHEN 2 THEN CONCAT('库存管理流程-', LPAD(n + @offset, 7, '0')) WHEN 3 THEN CONCAT('用户注册流程-', LPAD(n + @offset, 7, '0')) ELSE CONCAT('数据同步流程-', LPAD(n + @offset, 7, '0')) END as name, CASE WHEN ((n + @offset) % 100) < 60 THEN 'running' WHEN ((n + @offset) % 100) < 80 THEN 'completed' WHEN ((n + @offset) % 100) < 90 THEN 'pending' WHEN ((n + @offset) % 100) < 95 THEN 'compensating' ELSE 'failed' END as saga_status, CASE WHEN ((n + @offset) % 4) = 0 THEN 'manual' ELSE 'auto' END as step_index_mode, CASE WHEN ((n + @offset) % 10) < 2 THEN 3 WHEN ((n + @offset) % 10) < 6 THEN 4 ELSE 5 END as steps_count, CASE ((n + @offset) % 5) WHEN 0 THEN 'ecommerce' WHEN 1 THEN 'payment' WHEN 2 THEN 'inventory' WHEN 3 THEN 'user' ELSE 'sync' END as business_type FROM (SELECT a.N + b.N * 10 + c.N * 100 + d.N * 1000 + e.N * 10000 + f.N * 100000 + g.N * 1000000 as n FROM (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) e, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) f, (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) g) numbers WHERE n < 700000; START TRANSACTION; INSERT INTO saga_transactions (saga_id, name, saga_status, step_index_mode, cur_step_index, compensation_window_sec, created_at, updated_at) SELECT saga_id, name, saga_status, step_index_mode, CASE WHEN step_index_mode = 'auto' THEN steps_count ELSE 0 END, CASE business_type WHEN 'ecommerce' THEN 300 WHEN 'payment' THEN 600 WHEN 'inventory' THEN 180 WHEN 'user' THEN 120 ELSE 240 END, DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7200) SECOND), DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 3600) SECOND) FROM temp_saga_data ORDER BY id; COMMIT; SET @processed_sagas = @processed_sagas + ROW_COUNT(); SELECT CONCAT('所有批次完成: ', @processed_sagas, '/', @total_sagas, ' - 100万条Saga数据生成完成!') as final_progress;

-- 清理临时表
DROP TEMPORARY TABLE temp_saga_data;

-- 最终验证
SELECT CONCAT('✅ 数据生成完成！总数: ', COUNT(*), ' 条') as completion_status FROM saga_transactions;
SELECT 
    saga_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 2) as percentage
FROM saga_transactions 
GROUP BY saga_status 
ORDER BY count DESC;
