-- V2 事务回滚性能测试脚本
-- 支持锁策略切换和性能对比

wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 加载真实的sagaId
local saga_ids = {}
local file = io.open("performance-test/results/created_saga_ids_v2.txt", "r")
if file then
    for line in file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    file:close()
else
    -- 如果V2文件不存在，尝试使用V1文件
    file = io.open("performance-test/results/created_saga_ids.txt", "r")
    if file then
        for line in file:lines() do
            if line and line ~= "" then
                table.insert(saga_ids, line)
            end
        end
        file:close()
    end
end

print("加载了 " .. #saga_ids .. " 个sagaId用于V2回滚测试")

counter = 0
success_count = 0
error_count = 0

-- 失败原因和步骤的组合
local fail_reasons = {"业务异常", "网络超时", "数据验证失败", "外部服务不可用", "资源不足"}
local failed_steps = {"create_order", "process_payment", "reserve_inventory", "send_notification", "arrange_shipping"}

function request()
    if #saga_ids == 0 then
        return wrk.format("POST", nil, nil, '{"sagaId": "no-saga-available"}')
    end
    
    counter = counter + 1
    
    -- 循环使用sagaId
    local saga_id = saga_ids[(counter - 1) % #saga_ids + 1]
    
    -- 循环使用失败原因和步骤
    local fail_reason = fail_reasons[((counter - 1) % #fail_reasons) + 1]
    local failed_step = failed_steps[((counter - 1) % #failed_steps) + 1]
    
    local body = string.format([[{
        "sagaId": "%s",
        "failReason": "%s - %s",
        "failedStep": "%s",
        "executionMode": "async"
    }]], saga_id, fail_reason, failed_step, failed_step)
    
    return wrk.format("POST", "/v2/saga/transactions/rollback", nil, body)
end

-- 响应处理函数
function response(status, headers, body)
    if status == 200 then
        -- 解析响应体检查code字段
        local response_code = body:match('"code":(%d+)')
        if response_code and tonumber(response_code) == 0 then
            success_count = success_count + 1
        else
            error_count = error_count + 1
            if error_count <= 5 then
                print("V2业务错误响应: HTTP " .. status .. ", code=" .. (response_code or "unknown") .. " - " .. body:sub(1, 200))
            end
        end
    else
        error_count = error_count + 1
        if error_count <= 5 then
            print("V2 HTTP错误响应: " .. status .. " - " .. body:sub(1, 200))
        end
    end
end

function done(summary, latency, requests)
    print("\n=== V2 事务回滚接口性能测试结果 (使用创建的sagaId) ===")
    print(string.format("使用sagaId数量: %d", #saga_ids))
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("成功请求: %d", success_count))
    print(string.format("错误数: %d", error_count))
    if summary.requests > 0 then
        print(string.format("成功率: %.2f%%", (success_count / summary.requests) * 100))
    end
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
    print("✅ 业务约束: 使用create-saga-v2-test.lua创建的真实sagaId")
end
