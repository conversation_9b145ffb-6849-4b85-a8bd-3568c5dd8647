# Docker容器性能统计收集工具使用指南

## 📋 概述

本工具集提供了在Saga分布式事务系统性能测试期间收集Docker容器性能统计数据的完整解决方案，便于后期优化对比分析。

## 🛠️ 工具组件

### 1. 核心脚本

| 脚本名称 | 功能描述 | 使用场景 |
|----------|----------|----------|
| `collect-docker-stats.sh` | 独立的Docker统计收集工具 | 单独收集容器性能数据 |
| `run-perf-test-with-stats.sh` | 集成性能测试工具 | 压测+统计收集一体化 |

### 2. 输出文件类型

| 文件类型 | 格式 | 内容描述 |
|----------|------|----------|
| `docker-stats-*.csv` | CSV | 容器CPU、内存、网络、磁盘I/O统计 |
| `mysql-stats-*.txt` | 文本 | MySQL性能指标、连接信息、InnoDB状态 |
| `performance-summary-*.md` | Markdown | 性能数据汇总分析报告 |
| `test-*-*.txt` | 文本 | wrk压测详细输出结果 |
| `comprehensive-report-*.md` | Markdown | 综合性能测试报告 |

## 🚀 快速开始

### 方式1：集成测试（推荐）

```bash
# 设置脚本权限
chmod +x performance-test/*.sh

# 基础测试套件（创建+查询）
./performance-test/run-perf-test-with-stats.sh basic

# 完整测试套件（所有5个接口）
./performance-test/run-perf-test-with-stats.sh full

# 单个接口测试
./performance-test/run-perf-test-with-stats.sh create
./performance-test/run-perf-test-with-stats.sh query

# 自定义测试
./performance-test/run-perf-test-with-stats.sh -c "wrk -t8 -c100 -d60s --latency http://localhost:8080/hello" custom
```

### 方式2：独立统计收集

```bash
# 默认配置（5秒间隔，5分钟）
./performance-test/collect-docker-stats.sh

# 自定义配置
./performance-test/collect-docker-stats.sh -i 2 -d 600  # 2秒间隔，10分钟

# 环境变量配置
STATS_INTERVAL=1 STATS_DURATION=300 ./performance-test/collect-docker-stats.sh
```

## 📊 数据分析

### CSV数据字段说明

`docker-stats-*.csv` 文件包含以下字段：

| 字段名 | 描述 | 单位 |
|--------|------|------|
| timestamp | 时间戳 | YYYY-MM-DD HH:MM:SS |
| container | 容器名称 | saga-mysql / saga-app |
| cpu_percent | CPU使用率 | 百分比 |
| memory_usage | 内存使用量 | MB |
| memory_limit | 内存限制 | GB |
| memory_percent | 内存使用率 | 百分比 |
| network_io_rx | 网络接收 | MB |
| network_io_tx | 网络发送 | MB |
| block_io_read | 磁盘读取 | MB |
| block_io_write | 磁盘写入 | MB |
| pids | 进程数 | 个数 |

### 使用Excel分析

1. **导入CSV文件**
   ```
   文件 → 打开 → 选择CSV文件 → 数据分隔符选择"逗号"
   ```

2. **创建图表**
   - 选择时间戳列作为X轴
   - 选择CPU使用率、内存使用量作为Y轴
   - 插入折线图查看趋势

3. **数据透视表**
   - 按容器名称分组
   - 计算平均值、最大值、最小值

### 使用Python分析

```python
import pandas as pd
import matplotlib.pyplot as plt

# 读取数据
df = pd.read_csv('performance-test/results/docker-stats-20250801_143000.csv')

# 转换时间戳
df['timestamp'] = pd.to_datetime(df['timestamp'])

# 按容器分组绘图
for container in df['container'].unique():
    container_data = df[df['container'] == container]
    
    plt.figure(figsize=(12, 8))
    
    # CPU使用率
    plt.subplot(2, 2, 1)
    plt.plot(container_data['timestamp'], container_data['cpu_percent'])
    plt.title(f'{container} - CPU使用率')
    plt.ylabel('CPU %')
    
    # 内存使用量
    plt.subplot(2, 2, 2)
    plt.plot(container_data['timestamp'], container_data['memory_usage'])
    plt.title(f'{container} - 内存使用量')
    plt.ylabel('Memory MB')
    
    plt.tight_layout()
    plt.show()
```

## 🎯 性能优化指导

### 1. MySQL容器优化

**CPU使用率分析**：
- `< 50%`: 正常，资源充足
- `50-80%`: 良好，可考虑优化查询
- `> 80%`: 需要优化，检查慢查询、索引

**内存使用分析**：
- 监控InnoDB缓冲池命中率
- 检查是否有内存泄漏
- 评估是否需要增加内存配置

### 2. 应用容器优化

**CPU使用率分析**：
- 检查业务逻辑复杂度
- 评估并发处理效率
- 考虑增加实例数量

**内存使用分析**：
- 监控Go GC频率和耗时
- 检查是否有goroutine泄漏
- 评估内存配置是否合理

### 3. 网络I/O分析

**高网络I/O可能原因**：
- 大量数据传输
- 频繁的数据库查询
- 需要优化数据传输格式

## 📈 基线建立和对比

### 1. 建立性能基线

```bash
# 第一次测试 - 建立基线
./performance-test/run-perf-test-with-stats.sh full
mv performance-test/results performance-test/baseline-$(date +%Y%m%d)

# 后续测试 - 对比分析
./performance-test/run-perf-test-with-stats.sh full
```

### 2. 对比分析脚本

```bash
# 创建对比分析脚本
cat > performance-test/compare-results.sh << 'EOF'
#!/bin/bash
baseline_dir=$1
current_dir=$2

echo "性能对比分析"
echo "基线数据: $baseline_dir"
echo "当前数据: $current_dir"

# 对比QPS
baseline_qps=$(grep "Requests/sec:" $baseline_dir/test-*create*.txt | awk '{print $2}')
current_qps=$(grep "Requests/sec:" $current_dir/test-*create*.txt | awk '{print $2}')

echo "创建接口QPS对比:"
echo "  基线: $baseline_qps"
echo "  当前: $current_qps"
echo "  变化: $(echo "scale=2; ($current_qps - $baseline_qps) / $baseline_qps * 100" | bc)%"
EOF

chmod +x performance-test/compare-results.sh
```

## 🔧 高级配置

### 环境变量配置

```bash
# 统计收集配置
export STATS_INTERVAL=1        # 1秒间隔
export STATS_DURATION=600      # 10分钟

# 自定义输出目录
export OUTPUT_DIR="custom-results"

# 执行测试
./performance-test/run-perf-test-with-stats.sh full
```

### Docker资源限制调整

```yaml
# docker-compose.yml 中调整资源限制
services:
  saga-app:
    deploy:
      resources:
        limits:
          cpus: '8.0'
          memory: 16G
        reservations:
          cpus: '4.0'
          memory: 8G
```

## 📋 故障排查

### 常见问题

1. **容器未运行**
   ```bash
   # 检查容器状态
   docker ps
   
   # 启动服务
   make up
   ```

2. **权限问题**
   ```bash
   # 设置脚本权限
   chmod +x performance-test/*.sh
   ```

3. **磁盘空间不足**
   ```bash
   # 清理旧的测试结果
   rm -rf performance-test/results/old-*
   
   # 检查磁盘空间
   df -h
   ```

4. **MySQL连接失败**
   ```bash
   # 检查MySQL容器日志
   docker logs saga-mysql
   
   # 测试连接
   docker exec saga-mysql mysql -u root -p12345678a -e "SELECT 1"
   ```

## 🎉 最佳实践

1. **定期收集基线数据**：每次重大更新后建立新的性能基线
2. **多场景测试**：不同数据量级和并发级别的测试
3. **长期趋势分析**：保存历史数据，分析性能趋势
4. **自动化集成**：将性能测试集成到CI/CD流程
5. **告警阈值设置**：根据基线数据设置性能告警阈值

---

**文档更新时间**: 2025年8月1日  
**工具版本**: v1.0  
**适用环境**: macOS, Linux
