-- Saga 分布式事务系统 - 修复版数据初始化脚本
-- 生成时间: 2025年8月4日
-- 修复内容:
--   1. 移除不支持的 CREATE OR REPLACE 语法
--   2. 修复 MySQL 8.0 兼容性问题
--   3. 删除统计和验证部分代码
--   4. 优化存储过程创建逻辑

USE saga;

-- 清理现有数据
TRUNCATE TABLE saga_steps;
TRUNCATE TABLE saga_transactions;

-- 重置自增ID
ALTER TABLE saga_transactions AUTO_INCREMENT = 1;
ALTER TABLE saga_steps AUTO_INCREMENT = 1;

-- 配置参数
SET @total_sagas = 1000000;
SET @batch_size = 50000;
SET @batches = CEIL(@total_sagas / @batch_size);

-- 内存优化设置
SET SESSION autocommit = 0;
SET SESSION unique_checks = 0;
SET SESSION foreign_key_checks = 0;
SET SESSION bulk_insert_buffer_size = 134217728;  -- 128MB
SET SESSION read_buffer_size = 1048576;  -- 1MB

-- ============================================================================
-- 创建业务步骤模板
-- ============================================================================
DROP TABLE IF EXISTS business_step_templates;
CREATE TABLE business_step_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    step_order INT NOT NULL,
    action VARCHAR(64) NOT NULL,
    service_name VARCHAR(100) NOT NULL,
    business_type VARCHAR(20) NOT NULL,
    compensate_endpoint VARCHAR(255) NOT NULL,
    description VARCHAR(255) DEFAULT NULL,
    weight DECIMAL(3,2) DEFAULT 1.0 COMMENT '步骤权重，用于性能测试',
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uq_business_step (business_type, step_order),
    KEY idx_business_type (business_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入业务模板数据
INSERT INTO business_step_templates (step_order, action, service_name, business_type, compensate_endpoint, description, weight) VALUES
-- 电商订单流程
(1, 'CreateOrder', 'order-service', 'ecommerce', '/saga/compensate/CancelOrder', '创建订单记录', 1.0),
(2, 'ProcessPayment', 'payment-service', 'ecommerce', '/saga/compensate/RefundPayment', '处理订单支付', 1.5),
(3, 'ReserveInventory', 'inventory-service', 'ecommerce', '/saga/compensate/ReleaseInventory', '预留商品库存', 1.2),
(4, 'SendNotification', 'notification-service', 'ecommerce', '/saga/compensate/CancelNotification', '发送订单通知', 0.8),
(5, 'UpdateUserPoints', 'user-service', 'ecommerce', '/saga/compensate/RevertPoints', '更新用户积分', 0.9),

-- 支付处理流程
(1, 'ValidateAccount', 'account-service', 'payment', '/saga/compensate/UnlockAccount', '验证账户状态', 1.1),
(2, 'ProcessPayment', 'payment-service', 'payment', '/saga/compensate/RefundPayment', '执行支付操作', 1.8),
(3, 'UpdateBalance', 'account-service', 'payment', '/saga/compensate/RevertBalance', '更新账户余额', 1.3),
(4, 'LogTransaction', 'audit-service', 'payment', '/saga/compensate/RemoveLog', '记录交易日志', 0.7),
(5, 'SendReceipt', 'notification-service', 'payment', '/saga/compensate/CancelReceipt', '发送支付收据', 0.6),

-- 库存管理流程
(1, 'CheckInventory', 'inventory-service', 'inventory', '/saga/compensate/UnlockInventory', '检查库存状态', 1.0),
(2, 'ReserveStock', 'inventory-service', 'inventory', '/saga/compensate/ReleaseStock', '预留库存数量', 1.4),
(3, 'UpdateWarehouse', 'warehouse-service', 'inventory', '/saga/compensate/RevertWarehouse', '更新仓库系统', 1.1),
(4, 'UpdateCatalog', 'catalog-service', 'inventory', '/saga/compensate/RevertCatalog', '更新商品目录', 0.9),
(5, 'NotifySupplier', 'supplier-service', 'inventory', '/saga/compensate/CancelSupplierNotify', '通知供应商', 0.8),

-- 用户注册流程
(1, 'CreateUser', 'user-service', 'user', '/saga/compensate/DeleteUser', '创建用户账户', 1.2),
(2, 'SendWelcomeEmail', 'notification-service', 'user', '/saga/compensate/CancelEmail', '发送欢迎邮件', 0.5),
(3, 'InitializeProfile', 'profile-service', 'user', '/saga/compensate/DeleteProfile', '初始化用户档案', 1.0),
(4, 'GrantPermissions', 'auth-service', 'user', '/saga/compensate/RevokePermissions', '授予基础权限', 0.8),
(5, 'CreateWallet', 'wallet-service', 'user', '/saga/compensate/DeleteWallet', '创建电子钱包', 1.1),

-- 数据同步流程
(1, 'ExtractData', 'etl-service', 'sync', '/saga/compensate/CleanupExtract', '提取源数据', 2.0),
(2, 'TransformData', 'etl-service', 'sync', '/saga/compensate/RevertTransform', '转换数据格式', 2.5),
(3, 'LoadData', 'data-service', 'sync', '/saga/compensate/RemoveData', '加载目标数据', 1.8),
(4, 'ValidateData', 'validation-service', 'sync', '/saga/compensate/RemoveValidation', '验证数据完整性', 1.3),
(5, 'NotifyCompletion', 'notification-service', 'sync', '/saga/compensate/CancelCompletion', '发送完成通知', 0.4);

-- ============================================================================
-- 创建数据生成存储过程
-- ============================================================================

-- 删除可能存在的过程和函数
DROP PROCEDURE IF EXISTS HandleError;
DROP FUNCTION IF EXISTS GenerateUUID;
DROP FUNCTION IF EXISTS GetBusinessConfig;
DROP PROCEDURE IF EXISTS GenerateSagaBatch;
DROP PROCEDURE IF EXISTS GenerateStepsBatch;
DROP PROCEDURE IF EXISTS ProcessAllBatches;

DELIMITER //

-- 错误处理过程
CREATE PROCEDURE HandleError(IN error_msg TEXT)
BEGIN
    ROLLBACK;
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = error_msg;
END //

-- 生成UUID函数
CREATE FUNCTION GenerateUUID(seed1 INT, seed2 INT, batch_num INT) 
RETURNS CHAR(36)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE uuid_str CHAR(36);
    SET uuid_str = CONCAT(
        LPAD(HEX(UNIX_TIMESTAMP() + seed1), 8, '0'), '-',
        LPAD(HEX((seed2 * 31 + batch_num) % 65536), 4, '0'), '-',
        '4', LPAD(HEX((seed1 * 17 + seed2) % 4096), 3, '0'), '-',
        LPAD(HEX((8192 + (seed1 + seed2) % 2048)), 4, '0'), '-',
        LPAD(HEX(CONNECTION_ID() * 65536 + seed1), 12, '0')
    );
    RETURN uuid_str;
END //

-- 获取业务类型配置函数
CREATE FUNCTION GetBusinessConfig(business_type VARCHAR(20), config_key VARCHAR(20))
RETURNS VARCHAR(100)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result VARCHAR(100);
    CASE business_type
        WHEN 'ecommerce' THEN
            CASE config_key
                WHEN 'name_prefix' THEN SET result = '电商订单流程';
                WHEN 'window_sec' THEN SET result = '300';
                ELSE SET result = 'ecommerce';
            END CASE;
        WHEN 'payment' THEN
            CASE config_key
                WHEN 'name_prefix' THEN SET result = '支付处理流程';
                WHEN 'window_sec' THEN SET result = '600';
                ELSE SET result = 'payment';
            END CASE;
        WHEN 'inventory' THEN
            CASE config_key
                WHEN 'name_prefix' THEN SET result = '库存管理流程';
                WHEN 'window_sec' THEN SET result = '180';
                ELSE SET result = 'inventory';
            END CASE;
        WHEN 'user' THEN
            CASE config_key
                WHEN 'name_prefix' THEN SET result = '用户注册流程';
                WHEN 'window_sec' THEN SET result = '120';
                ELSE SET result = 'user';
            END CASE;
        ELSE
            CASE config_key
                WHEN 'name_prefix' THEN SET result = '数据同步流程';
                WHEN 'window_sec' THEN SET result = '240';
                ELSE SET result = 'sync';
            END CASE;
    END CASE;
    RETURN result;
END //

-- 批量生成Saga事务数据
CREATE PROCEDURE GenerateSagaBatch(
    IN batch_number INT,
    IN batch_size INT,
    IN start_offset INT
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        CALL HandleError(CONCAT('批次 ', batch_number, ' 处理失败'));
    END;

    -- 创建临时表
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_batch_saga (
        row_num INT PRIMARY KEY,
        saga_id CHAR(36) NOT NULL,
        name VARCHAR(64) NOT NULL,
        saga_status ENUM('pending', 'running', 'completed', 'compensating', 'failed') NOT NULL,
        step_index_mode ENUM('manual', 'auto') NOT NULL,
        steps_count INT NOT NULL,
        business_type VARCHAR(20) NOT NULL,
        compensation_window_sec INT NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL
    );

    -- 删除可能存在的旧数据
    DELETE FROM temp_batch_saga;

    -- 生成批次数据
    INSERT INTO temp_batch_saga
    SELECT 
        n,
        GenerateUUID(n + start_offset, batch_number, FLOOR(n/1000)) as saga_id,
        CONCAT(GetBusinessConfig(
            CASE (n % 5)
                WHEN 0 THEN 'ecommerce'
                WHEN 1 THEN 'payment' 
                WHEN 2 THEN 'inventory'
                WHEN 3 THEN 'user'
                ELSE 'sync'
            END, 'name_prefix'
        ), '-', LPAD(n + start_offset, 7, '0')) as name,
        
        -- 状态分布：更真实的业务场景
        CASE 
            WHEN (n % 100) < 50 THEN 'running'      -- 50% running
            WHEN (n % 100) < 75 THEN 'completed'    -- 25% completed  
            WHEN (n % 100) < 85 THEN 'pending'      -- 10% pending
            WHEN (n % 100) < 95 THEN 'compensating' -- 10% compensating
            ELSE 'failed'                           -- 5% failed
        END as saga_status,
        
        CASE WHEN (n % 4) = 0 THEN 'manual' ELSE 'auto' END as step_index_mode,
        
        -- 步骤分布：3(20%) + 4(40%) + 5(40%)
        CASE 
            WHEN (n % 10) < 2 THEN 3
            WHEN (n % 10) < 6 THEN 4
            ELSE 5
        END as steps_count,
        
        CASE (n % 5)
            WHEN 0 THEN 'ecommerce'
            WHEN 1 THEN 'payment'
            WHEN 2 THEN 'inventory' 
            WHEN 3 THEN 'user'
            ELSE 'sync'
        END as business_type,
        
        CAST(GetBusinessConfig(
            CASE (n % 5)
                WHEN 0 THEN 'ecommerce'
                WHEN 1 THEN 'payment'
                WHEN 2 THEN 'inventory'
                WHEN 3 THEN 'user'
                ELSE 'sync'
            END, 'window_sec'
        ) AS UNSIGNED) as compensation_window_sec,
        
        DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7200) SECOND) as created_at,
        DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 3600) SECOND) as updated_at
        
    FROM (
        SELECT a.N + b.N*10 + c.N*100 + d.N*1000 + e.N*10000 as n
        FROM 
            (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a,
            (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b,
            (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c,
            (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) d,
            (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) e
    ) numbers 
    WHERE n < batch_size;

    -- 插入到主表
    START TRANSACTION;
    INSERT INTO saga_transactions (
        saga_id, name, saga_status, step_index_mode, cur_step_index, 
        compensation_window_sec, created_at, updated_at
    )
    SELECT 
        saga_id, name, saga_status, step_index_mode,
        CASE 
            WHEN step_index_mode = 'auto' THEN steps_count 
            ELSE 0 
        END as cur_step_index,
        compensation_window_sec, created_at, updated_at
    FROM temp_batch_saga
    ORDER BY row_num;
    COMMIT;

    -- 清理临时表
    DROP TEMPORARY TABLE temp_batch_saga;
END //

-- 批量生成步骤数据（修复版）
CREATE PROCEDURE GenerateStepsBatch(
    IN batch_number INT,
    IN batch_size INT,
    IN start_offset INT
)
BEGIN
    DECLARE current_row INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT CONCAT('步骤批次 ', batch_number, ' 错误: ', @@error_count) as error_info;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    -- 使用窗口函数为事务分配行号，然后筛选批次
    INSERT INTO saga_steps (
        step_id, saga_id, action, step_index, service_name,
        context_data, compensation_context, compensate_endpoint,
        compensation_status, retry_count, created_at, updated_at
    )
    SELECT 
        MD5(CONCAT(t.saga_id, st.action, st.service_name, st.step_order, UNIX_TIMESTAMP(NOW()), @CONNECTION_ID)) as step_id,
        t.saga_id,
        st.action,
        st.step_order,
        st.service_name,
        
        -- 简化的上下文数据生成，使用saga_id的哈希值作为随机种子
        CASE 
            WHEN t.name LIKE '电商订单%' THEN CONCAT('{"orderId":"ORD-', SUBSTRING(t.saga_id, 1, 8), '","amount":', (50 + (CRC32(t.saga_id) % 950)), ',"currency":"CNY"}')
            WHEN t.name LIKE '支付处理%' THEN CONCAT('{"paymentId":"PAY-', SUBSTRING(t.saga_id, 1, 8), '","amount":', (10 + (CRC32(t.saga_id) % 990)), ',"currency":"CNY"}')
            WHEN t.name LIKE '库存管理%' THEN CONCAT('{"productId":"PROD-', (1000 + (CRC32(t.saga_id) % 9000)), '","quantity":', (1 + (CRC32(t.saga_id) % 10)), '}')
            WHEN t.name LIKE '用户注册%' THEN CONCAT('{"userId":"USR-', SUBSTRING(t.saga_id, 1, 8), '","email":"user', (CRC32(t.saga_id) % 100000), '@example.com"}')
            ELSE CONCAT('{"syncId":"SYNC-', SUBSTRING(t.saga_id, 1, 8), '","batchSize":', (100 + (CRC32(t.saga_id) % 900)), '}')
        END as context_data,
        
        CONCAT('{"sagaId":"', t.saga_id, '","stepIndex":', st.step_order, ',"action":"', st.action, '"}') as compensation_context,
        
        CONCAT('http://', st.service_name, ':8080', st.compensate_endpoint) as compensate_endpoint,
        
        -- 简化的补偿状态
        CASE 
            WHEN t.saga_status = 'completed' THEN 'completed'
            WHEN t.saga_status = 'failed' THEN 
                CASE (CRC32(t.saga_id) % 3) WHEN 0 THEN 'failed' WHEN 1 THEN 'completed' ELSE 'running' END
            WHEN t.saga_status = 'compensating' THEN
                CASE (CRC32(t.saga_id) % 4) WHEN 0 THEN 'running' WHEN 1 THEN 'completed' WHEN 2 THEN 'failed' ELSE 'pending' END
            ELSE 'uninitialized'
        END as compensation_status,
        
        CASE WHEN t.saga_status IN ('failed', 'compensating') THEN (CRC32(t.saga_id) % 3) ELSE 0 END as retry_count,
        
        t.created_at,
        DATE_ADD(t.created_at, INTERVAL (st.step_order * 15 + (CRC32(t.saga_id) % 30)) SECOND) as updated_at
        
    FROM (
        SELECT *,
               ROW_NUMBER() OVER (ORDER BY saga_id) as row_num
        FROM saga_transactions
    ) t
    JOIN business_step_templates st ON (
        st.business_type = CASE 
            WHEN t.name LIKE '电商订单%' THEN 'ecommerce'
            WHEN t.name LIKE '支付处理%' THEN 'payment'
            WHEN t.name LIKE '库存管理%' THEN 'inventory'
            WHEN t.name LIKE '用户注册%' THEN 'user'
            ELSE 'sync'
        END
        AND st.step_order <= CASE 
            WHEN t.step_index_mode = 'auto' THEN t.cur_step_index
            ELSE 5
        END
        AND st.is_active = TRUE
    )
    WHERE t.row_num > start_offset AND t.row_num <= start_offset + batch_size
    ORDER BY t.row_num, st.step_order;
    
    COMMIT;
END //

-- 主批处理控制程序
CREATE PROCEDURE ProcessAllBatches()
BEGIN
    DECLARE current_batch INT DEFAULT 1;
    DECLARE start_time DATETIME DEFAULT NOW();
    DECLARE batch_start_time DATETIME;
    DECLARE processed_sagas INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SELECT CONCAT('❌ 处理失败在批次 ', current_batch) as error_message;
        RESIGNAL;
    END;

    SELECT CONCAT('🚀 开始处理 ', @batches, ' 个批次，总计 ', @total_sagas, ' 个Saga事务') as start_info;
    
    -- 生成Saga事务数据
    WHILE current_batch <= @batches DO
        SET batch_start_time = NOW();
        
        SELECT CONCAT('📦 处理Saga批次 ', current_batch, '/', @batches, 
                     ' (', processed_sagas + 1, '-', 
                     LEAST(processed_sagas + @batch_size, @total_sagas), ')') as batch_info;
        
        CALL GenerateSagaBatch(current_batch, 
                              LEAST(@batch_size, @total_sagas - processed_sagas), 
                              processed_sagas);
        
        SET processed_sagas = processed_sagas + @batch_size;
        
        SELECT CONCAT('✅ Saga批次 ', current_batch, ' 完成，耗时: ',
                     TIMESTAMPDIFF(SECOND, batch_start_time, NOW()), '秒，',
                     '已处理: ', LEAST(processed_sagas, @total_sagas), '/', @total_sagas) as progress;
        
        SET current_batch = current_batch + 1;
    END WHILE;
    
    -- 生成步骤数据
    SET current_batch = 1;
    SET processed_sagas = 0;
    
    SELECT '🔧 开始生成步骤数据...' as step_info;
    
    WHILE current_batch <= @batches DO
        SET batch_start_time = NOW();
        
        SELECT CONCAT('📦 处理步骤批次 ', current_batch, '/', @batches) as step_batch_info;
        
        CALL GenerateStepsBatch(current_batch, 
                               LEAST(@batch_size, @total_sagas - processed_sagas),
                               processed_sagas);
        
        SET processed_sagas = processed_sagas + @batch_size;
        
        SELECT CONCAT('✅ 步骤批次 ', current_batch, ' 完成，耗时: ',
                     TIMESTAMPDIFF(SECOND, batch_start_time, NOW()), '秒') as step_progress;
        
        SET current_batch = current_batch + 1;
    END WHILE;
    
    SELECT CONCAT('🎉 所有批次处理完成！总耗时: ', 
                 TIMESTAMPDIFF(SECOND, start_time, NOW()), '秒') as completion_info;
END //

DELIMITER ;

-- ============================================================================
-- 执行批量数据生成
-- ============================================================================

SELECT '🚀 开始执行修复版数据初始化...' as execution_start;

CALL ProcessAllBatches();

-- ============================================================================
-- 简单验证
-- ============================================================================

SELECT '=== 数据生成完成 ===' as summary_header;

SELECT 
    '数据统计' as type,
    (SELECT COUNT(*) FROM saga_transactions) as saga_count,
    (SELECT COUNT(*) FROM saga_steps) as step_count,
    CONCAT(ROUND((SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions), 1), ':1') as step_ratio;

-- 状态分布
SELECT '状态分布' as type, saga_status, COUNT(*) as count
FROM saga_transactions 
GROUP BY saga_status 
ORDER BY count DESC;

-- ============================================================================
-- 清理
-- ============================================================================

-- 恢复系统设置
SET SESSION autocommit = 1;
SET SESSION unique_checks = 1;
SET SESSION foreign_key_checks = 1;
SET SESSION bulk_insert_buffer_size = DEFAULT;
SET SESSION read_buffer_size = DEFAULT;

-- 删除存储过程和函数
DROP PROCEDURE IF EXISTS HandleError;
DROP FUNCTION IF EXISTS GenerateUUID;
DROP FUNCTION IF EXISTS GetBusinessConfig;
DROP PROCEDURE IF EXISTS GenerateSagaBatch;
DROP PROCEDURE IF EXISTS GenerateStepsBatch;
DROP PROCEDURE IF EXISTS ProcessAllBatches;

SELECT '✅ 修复版Saga数据初始化脚本执行完成！' as final_message;