-- 测试动态状态码生成逻辑
USE saga;

-- 清空步骤数据
TRUNCATE TABLE saga_steps;

-- 确保业务模板数据是最新的
TRUNCATE TABLE business_step_templates;
INSERT INTO business_step_templates (step_order, action, service_name, business_type, compensate_endpoint, description, weight) VALUES
-- 电商订单流程
(1, 'CreateOrder', 'order-service', 'ecommerce', '/saga/compensate/CancelOrder', '创建订单记录', 1.0),
(2, 'ProcessPayment', 'payment-service', 'ecommerce', '/saga/compensate/RefundPayment', '处理订单支付', 1.5),
(3, 'ReserveInventory', 'inventory-service', 'ecommerce', '/saga/compensate/ReleaseInventory', '预留商品库存', 1.2),
(4, 'SendNotification', 'notification-service', 'ecommerce', '/saga/compensate/CancelNotification', '发送订单通知', 0.8),
(5, 'UpdateUserPoints', 'user-service', 'ecommerce', '/saga/compensate/RevertPoints', '更新用户积分', 0.9),

-- 支付处理流程
(1, 'ValidateAccount', 'account-service', 'payment', '/saga/compensate/UnlockAccount', '验证账户状态', 1.1),
(2, 'ProcessPayment', 'payment-service', 'payment', '/saga/compensate/RefundPayment', '执行支付操作', 1.8),
(3, 'UpdateBalance', 'account-service', 'payment', '/saga/compensate/RevertBalance', '更新账户余额', 1.3),
(4, 'LogTransaction', 'audit-service', 'payment', '/saga/compensate/RemoveLog', '记录交易日志', 0.7),
(5, 'SendReceipt', 'notification-service', 'payment', '/saga/compensate/CancelReceipt', '发送支付收据', 0.6),

-- 库存管理流程
(1, 'CheckInventory', 'inventory-service', 'inventory', '/saga/compensate/UnlockInventory', '检查库存状态', 1.0),
(2, 'ReserveStock', 'inventory-service', 'inventory', '/saga/compensate/ReleaseStock', '预留库存数量', 1.4),
(3, 'UpdateWarehouse', 'warehouse-service', 'inventory', '/saga/compensate/RevertWarehouse', '更新仓库系统', 1.1),
(4, 'UpdateCatalog', 'catalog-service', 'inventory', '/saga/compensate/RevertCatalog', '更新商品目录', 0.9),
(5, 'NotifySupplier', 'supplier-service', 'inventory', '/saga/compensate/CancelSupplierNotify', '通知供应商', 0.8),

-- 用户注册流程
(1, 'CreateUser', 'user-service', 'user', '/saga/compensate/DeleteUser', '创建用户账户', 1.2),
(2, 'SendWelcomeEmail', 'notification-service', 'user', '/saga/compensate/CancelEmail', '发送欢迎邮件', 0.5),
(3, 'InitializeProfile', 'profile-service', 'user', '/saga/compensate/DeleteProfile', '初始化用户档案', 1.0),
(4, 'GrantPermissions', 'auth-service', 'user', '/saga/compensate/RevokePermissions', '授予基础权限', 0.8),
(5, 'CreateWallet', 'wallet-service', 'user', '/saga/compensate/DeleteWallet', '创建电子钱包', 1.1),

-- 数据同步流程
(1, 'ExtractData', 'etl-service', 'sync', '/saga/compensate/CleanupExtract', '提取源数据', 2.0),
(2, 'TransformData', 'etl-service', 'sync', '/saga/compensate/RevertTransform', '转换数据格式', 2.5),
(3, 'LoadData', 'data-service', 'sync', '/saga/compensate/RemoveData', '加载目标数据', 1.8),
(4, 'ValidateData', 'validation-service', 'sync', '/saga/compensate/RemoveValidation', '验证数据完整性', 1.3),
(5, 'NotifyCompletion', 'notification-service', 'sync', '/saga/compensate/CancelCompletion', '发送完成通知', 0.4);

-- 测试动态状态码生成（使用前1000个saga）
SELECT '=== 测试动态状态码生成 ===' as test_info;

INSERT INTO saga_steps (
    step_id, saga_id, action, step_index, service_name,
    context_data, compensation_context, compensate_endpoint,
    compensation_status, retry_count, created_at, updated_at
)
SELECT 
    MD5(CONCAT(t.saga_id, st.action, st.service_name, st.step_order, UNIX_TIMESTAMP(NOW()), CONNECTION_ID())) as step_id,
    t.saga_id,
    st.action,
    st.step_order,
    st.service_name,
    
    -- 简化的上下文数据
    JSON_OBJECT('sagaId', t.saga_id, 'action', st.action, 'amount', 99.99) as context_data,
    
    JSON_OBJECT('sagaId', t.saga_id, 'stepIndex', st.step_order, 'action', st.action) as compensation_context,
    
    -- 动态生成compensate_endpoint，80%成功(200)，20%失败(400/500)
    CASE 
        WHEN (CRC32(CONCAT(t.saga_id, st.step_order)) % 100) < 80 THEN 'https://httpbin.org/status/200'
        WHEN (CRC32(CONCAT(t.saga_id, st.step_order)) % 100) < 90 THEN 'https://httpbin.org/status/400'
        ELSE 'https://httpbin.org/status/500'
    END as compensate_endpoint,
    
    'completed' as compensation_status,
    0 as retry_count,
    t.created_at,
    t.updated_at
    
FROM (SELECT * FROM saga_transactions LIMIT 1000) t
JOIN business_step_templates st ON 
    st.business_type = CASE 
        WHEN t.name LIKE '电商订单流程%' THEN 'ecommerce'
        WHEN t.name LIKE '支付处理流程%' THEN 'payment'
        WHEN t.name LIKE '库存管理流程%' THEN 'inventory'
        WHEN t.name LIKE '用户注册流程%' THEN 'user'
        ELSE 'sync'
    END
    AND st.step_order <= CASE 
        WHEN t.step_index_mode = 'auto' AND t.cur_step_index > 0 THEN t.cur_step_index
        ELSE 5
    END
    AND st.is_active = TRUE;

-- 验证状态码分布
SELECT '=== 状态码分布验证 ===' as distribution_check;

SELECT 
    CASE 
        WHEN compensate_endpoint LIKE '%/200' THEN '200 (成功)'
        WHEN compensate_endpoint LIKE '%/400' THEN '400 (客户端错误)'
        WHEN compensate_endpoint LIKE '%/500' THEN '500 (服务器错误)'
        ELSE '其他'
    END as status_code,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_steps), 2) as percentage
FROM saga_steps 
GROUP BY 
    CASE 
        WHEN compensate_endpoint LIKE '%/200' THEN '200 (成功)'
        WHEN compensate_endpoint LIKE '%/400' THEN '400 (客户端错误)'
        WHEN compensate_endpoint LIKE '%/500' THEN '500 (服务器错误)'
        ELSE '其他'
    END
ORDER BY count DESC;

-- 显示部分数据样例
SELECT '=== 数据样例 ===' as sample_data;
SELECT 
    saga_id,
    action,
    step_index,
    service_name,
    compensate_endpoint
FROM saga_steps 
LIMIT 10;

SELECT CONCAT('✅ 动态状态码测试完成！总步骤数: ', COUNT(*)) as test_result FROM saga_steps;
