-- 修复数据比例脚本
-- 将现有数据的状态分布调整为正确的比例

USE saga;

SELECT '=== 开始修复数据比例 ===' as fix_info;

-- 显示修复前的状态
SELECT '修复前的状态分布:' as before_fix;
SELECT 
    saga_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 2) as percentage
FROM saga_transactions 
GROUP BY saga_status 
ORDER BY count DESC;

-- 修复saga状态分布：调整为60% running, 20% completed, 10% pending, 5% compensating, 5% failed
UPDATE saga_transactions 
SET saga_status = CASE 
    WHEN (CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(name, '-', -1), '.', 1) AS UNSIGNED) % 100) < 60 THEN 'running'
    WHEN (CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(name, '-', -1), '.', 1) AS UNSIGNED) % 100) < 80 THEN 'completed'
    WHEN (CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(name, '-', -1), '.', 1) AS UNSIGNED) % 100) < 90 THEN 'pending'
    WHEN (CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(name, '-', -1), '.', 1) AS UNSIGNED) % 100) < 95 THEN 'compensating'
    ELSE 'failed'
END;

-- 显示修复后的状态
SELECT '修复后的状态分布:' as after_fix;
SELECT 
    saga_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 2) as percentage
FROM saga_transactions 
GROUP BY saga_status 
ORDER BY count DESC;

-- 清空并重新生成步骤数据
SELECT '开始重新生成步骤数据...' as step_generation;
TRUNCATE TABLE saga_steps;

-- 分批生成步骤数据 - 批次1: 前20万个saga
SELECT '生成步骤数据 - 批次1 (前20万个saga)' as batch1;
INSERT INTO saga_steps (step_id, saga_id, action, step_index, service_name, context_data, compensation_context, compensate_endpoint, compensation_status, retry_count, created_at, updated_at)
SELECT
    MD5(CONCAT(s.saga_id, st.action, st.service_name, st.step_order, RAND())) as step_id,
    s.saga_id, st.action, st.step_order as step_index, st.service_name,
    JSON_OBJECT(
        'sagaId', s.saga_id,
        'action', st.action,
        'businessType', CASE 
            WHEN s.name LIKE '电商订单流程%' THEN 'ecommerce'
            WHEN s.name LIKE '支付处理流程%' THEN 'payment'
            WHEN s.name LIKE '库存管理流程%' THEN 'inventory'
            WHEN s.name LIKE '用户注册流程%' THEN 'user'
            ELSE 'sync'
        END,
        'amount', ROUND(99.99 + (CHAR_LENGTH(s.saga_id) % 900), 2),
        'timestamp', UNIX_TIMESTAMP(NOW())
    ) as context_data,
    JSON_OBJECT(
        'sagaId', s.saga_id,
        'stepIndex', st.step_order,
        'action', st.action,
        'service', st.service_name,
        'reason', 'saga_rollback'
    ) as compensation_context,
    st.compensate_endpoint,
    CASE
        WHEN s.saga_status = 'completed' THEN 'completed'
        WHEN s.saga_status = 'failed' THEN 'failed'
        WHEN s.saga_status = 'compensating' THEN
            CASE (CHAR_LENGTH(s.saga_id) % 4)
                WHEN 0 THEN 'running'
                WHEN 1 THEN 'completed'
                WHEN 2 THEN 'failed'
                ELSE 'pending'
            END
        ELSE
            CASE (CHAR_LENGTH(s.saga_id) % 6)
                WHEN 0 THEN 'uninitialized'
                WHEN 1 THEN 'pending'
                WHEN 2 THEN 'running'
                WHEN 3 THEN 'completed'
                WHEN 4 THEN 'failed'
                ELSE 'delay'
            END
    END as compensation_status,
    CASE
        WHEN s.saga_status = 'failed' THEN (CHAR_LENGTH(s.saga_id) % 3)
        WHEN s.saga_status = 'compensating' THEN (CHAR_LENGTH(s.saga_id) % 2)
        ELSE 0
    END as retry_count,
    s.created_at,
    DATE_ADD(s.created_at, INTERVAL (st.step_order * 30) SECOND) as updated_at
FROM (SELECT * FROM saga_transactions ORDER BY saga_id LIMIT 200000) s
JOIN business_step_templates st ON st.business_type = 
    CASE 
        WHEN s.name LIKE '电商订单流程%' THEN 'ecommerce'
        WHEN s.name LIKE '支付处理流程%' THEN 'payment'
        WHEN s.name LIKE '库存管理流程%' THEN 'inventory'
        WHEN s.name LIKE '用户注册流程%' THEN 'user'
        ELSE 'sync'
    END
    AND st.step_order <= 
    CASE 
        WHEN s.step_index_mode = 'auto' AND s.cur_step_index > 0 THEN s.cur_step_index
        ELSE 5
    END
WHERE st.is_active = TRUE;

SELECT CONCAT('批次1完成，当前步骤数: ', COUNT(*)) as progress FROM saga_steps;

-- 批次2: 第20-40万个saga
SELECT '生成步骤数据 - 批次2 (第20-40万个saga)' as batch2;
INSERT INTO saga_steps (step_id, saga_id, action, step_index, service_name, context_data, compensation_context, compensate_endpoint, compensation_status, retry_count, created_at, updated_at)
SELECT
    MD5(CONCAT(s.saga_id, st.action, st.service_name, st.step_order, RAND())) as step_id,
    s.saga_id, st.action, st.step_order as step_index, st.service_name,
    JSON_OBJECT('sagaId', s.saga_id, 'action', st.action, 'amount', 99.99) as context_data,
    JSON_OBJECT('sagaId', s.saga_id, 'stepIndex', st.step_order, 'action', st.action) as compensation_context,
    st.compensate_endpoint,
    CASE WHEN s.saga_status = 'completed' THEN 'completed' WHEN s.saga_status = 'failed' THEN 'failed' WHEN s.saga_status = 'compensating' THEN 'running' ELSE 'pending' END as compensation_status,
    CASE WHEN s.saga_status IN ('failed', 'compensating') THEN 1 ELSE 0 END as retry_count,
    s.created_at, s.updated_at
FROM (SELECT * FROM saga_transactions ORDER BY saga_id LIMIT 200000 OFFSET 200000) s
JOIN business_step_templates st ON st.business_type = CASE WHEN s.name LIKE '电商订单流程%' THEN 'ecommerce' WHEN s.name LIKE '支付处理流程%' THEN 'payment' WHEN s.name LIKE '库存管理流程%' THEN 'inventory' WHEN s.name LIKE '用户注册流程%' THEN 'user' ELSE 'sync' END AND st.step_order <= CASE WHEN s.step_index_mode = 'auto' AND s.cur_step_index > 0 THEN s.cur_step_index ELSE 5 END
WHERE st.is_active = TRUE;

SELECT CONCAT('批次2完成，当前步骤数: ', COUNT(*)) as progress FROM saga_steps;

-- 批次3: 第40-60万个saga
SELECT '生成步骤数据 - 批次3 (第40-60万个saga)' as batch3;
INSERT INTO saga_steps (step_id, saga_id, action, step_index, service_name, context_data, compensation_context, compensate_endpoint, compensation_status, retry_count, created_at, updated_at)
SELECT MD5(CONCAT(s.saga_id, st.action, st.service_name, st.step_order, RAND())) as step_id, s.saga_id, st.action, st.step_order as step_index, st.service_name, JSON_OBJECT('sagaId', s.saga_id, 'action', st.action, 'amount', 99.99) as context_data, JSON_OBJECT('sagaId', s.saga_id, 'stepIndex', st.step_order, 'action', st.action) as compensation_context, st.compensate_endpoint, CASE WHEN s.saga_status = 'completed' THEN 'completed' WHEN s.saga_status = 'failed' THEN 'failed' WHEN s.saga_status = 'compensating' THEN 'running' ELSE 'pending' END as compensation_status, CASE WHEN s.saga_status IN ('failed', 'compensating') THEN 1 ELSE 0 END as retry_count, s.created_at, s.updated_at FROM (SELECT * FROM saga_transactions ORDER BY saga_id LIMIT 200000 OFFSET 400000) s JOIN business_step_templates st ON st.business_type = CASE WHEN s.name LIKE '电商订单流程%' THEN 'ecommerce' WHEN s.name LIKE '支付处理流程%' THEN 'payment' WHEN s.name LIKE '库存管理流程%' THEN 'inventory' WHEN s.name LIKE '用户注册流程%' THEN 'user' ELSE 'sync' END AND st.step_order <= CASE WHEN s.step_index_mode = 'auto' AND s.cur_step_index > 0 THEN s.cur_step_index ELSE 5 END WHERE st.is_active = TRUE;

SELECT CONCAT('批次3完成，当前步骤数: ', COUNT(*)) as progress FROM saga_steps;

-- 批次4-5: 剩余40万个saga
SELECT '生成步骤数据 - 批次4-5 (剩余40万个saga)' as batch45;
INSERT INTO saga_steps (step_id, saga_id, action, step_index, service_name, context_data, compensation_context, compensate_endpoint, compensation_status, retry_count, created_at, updated_at)
SELECT MD5(CONCAT(s.saga_id, st.action, st.service_name, st.step_order, RAND())) as step_id, s.saga_id, st.action, st.step_order as step_index, st.service_name, JSON_OBJECT('sagaId', s.saga_id, 'action', st.action, 'amount', 99.99) as context_data, JSON_OBJECT('sagaId', s.saga_id, 'stepIndex', st.step_order, 'action', st.action) as compensation_context, st.compensate_endpoint, CASE WHEN s.saga_status = 'completed' THEN 'completed' WHEN s.saga_status = 'failed' THEN 'failed' WHEN s.saga_status = 'compensating' THEN 'running' ELSE 'pending' END as compensation_status, CASE WHEN s.saga_status IN ('failed', 'compensating') THEN 1 ELSE 0 END as retry_count, s.created_at, s.updated_at FROM (SELECT * FROM saga_transactions ORDER BY saga_id LIMIT 400000 OFFSET 600000) s JOIN business_step_templates st ON st.business_type = CASE WHEN s.name LIKE '电商订单流程%' THEN 'ecommerce' WHEN s.name LIKE '支付处理流程%' THEN 'payment' WHEN s.name LIKE '库存管理流程%' THEN 'inventory' WHEN s.name LIKE '用户注册流程%' THEN 'user' ELSE 'sync' END AND st.step_order <= CASE WHEN s.step_index_mode = 'auto' AND s.cur_step_index > 0 THEN s.cur_step_index ELSE 5 END WHERE st.is_active = TRUE;

-- 最终统计
SELECT CONCAT('✅ 数据比例修复完成！总步骤数: ', COUNT(*)) as final_result FROM saga_steps;
SELECT 
    COUNT(*) as total_sagas,
    (SELECT COUNT(*) FROM saga_steps) as total_steps,
    ROUND((SELECT COUNT(*) FROM saga_steps) / COUNT(*), 2) as avg_steps_per_saga
FROM saga_transactions;

SELECT '=== 🎉 数据比例修复完成！🎉 ===' as completion_status;
