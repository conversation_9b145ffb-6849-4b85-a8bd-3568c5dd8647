-- 事务提交性能测试脚本 (使用创建的sagaId)
-- 业务约束: 使用create-saga-test.lua创建的真实sagaId进行提交测试

wrk.method = "POST"
wrk.headers["Content-Type"] = "application/json"

-- 读取创建的sagaId
local saga_ids = {}

-- 优先使用组合测试创建的sagaId
local created_file = io.open("performance-test/results/combined_created_saga_ids.txt", "r")
if not created_file then
    -- 如果组合测试文件不存在，使用单独创建测试的文件
    created_file = io.open("performance-test/results/created_saga_ids.txt", "r")
end

if created_file then
    for line in created_file:lines() do
        if line and line ~= "" then
            table.insert(saga_ids, line)
        end
    end
    created_file:close()
    print("加载了 " .. #saga_ids .. " 个新创建的sagaId用于提交测试")
else
    print("❌ 未找到创建的sagaId文件，请先运行create-saga-test.lua")
end

counter = 0
success_count = 0
error_count = 0

function request()
    if #saga_ids == 0 then
        return wrk.format("GET", "/health", nil, nil)
    end
    
    counter = counter + 1
    local saga_id = saga_ids[(counter % #saga_ids) + 1]
    
    -- 事务提交请求体 (符合API规范)
    local body = string.format([[{
        "sagaId": "%s"
    }]], saga_id)

    return wrk.format("POST", "/saga/transactions/commit", nil, body)
end

-- 响应处理函数
function response(status, headers, body)
    if status == 200 then
        -- 解析响应体检查code字段
        local response_code = body:match('"code":(%d+)')
        if response_code and tonumber(response_code) == 0 then
            success_count = success_count + 1
        else
            error_count = error_count + 1
            if error_count <= 5 then
                print("业务错误响应: HTTP " .. status .. ", code=" .. (response_code or "unknown") .. " - " .. body)
            end
        end
    else
        error_count = error_count + 1
        if error_count <= 5 then
            print("HTTP错误响应: " .. status .. " - " .. body)
        end
    end
end

function done(summary, latency, requests)
    print("\n=== 事务提交接口性能测试结果 (使用创建的sagaId) ===")
    print(string.format("使用新创建sagaId数量: %d", #saga_ids))
    print(string.format("总请求数: %d", summary.requests))
    print(string.format("成功请求: %d", success_count))
    print(string.format("错误数: %d", error_count))
    print(string.format("成功率: %.2f%%", (success_count / summary.requests) * 100))
    print(string.format("平均QPS: %.2f", summary.requests / (summary.duration / 1000000)))
    print(string.format("平均延迟: %.2fms", latency.mean / 1000))
    print(string.format("P99延迟: %.2fms", latency:percentile(99) / 1000))
    print("✅ 业务约束: 使用create-saga-test.lua创建的真实sagaId")
    print("⚠️  注意: 新创建的saga默认为running状态，适合提交测试")
end
