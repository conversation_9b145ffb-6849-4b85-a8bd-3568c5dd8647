-- ============================================================================
-- Unit Tests for Core Functions - Data Initialization Script
-- ============================================================================
-- Version: 1.0.0
-- Created: 2025-08-27
-- Purpose: Comprehensive unit tests for GenerateUUID, GetBusinessConfig, and GenerateContextData functions
-- 
-- Test Coverage:
-- ✓ UUID generation function uniqueness and format validation
-- ✓ Business configuration function accuracy and edge cases
-- ✓ Context data generation validity for all business scenarios
-- 
-- Requirements Covered:
-- - 3.3: Deterministic UUID generation based on seeds
-- - 4.1: Appropriate compensation context data for each step type
-- - 2.1-2.5: Business scenario data generation (ecommerce, payment, inventory, user, sync)
-- ============================================================================

-- Test configuration
SET @test_start_time = NOW();
SET @total_tests = 0;
SET @passed_tests = 0;
SET @failed_tests = 0;

-- Create test results table
CREATE TEMPORARY TABLE IF NOT EXISTS test_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    test_category VARCHAR(50) NOT NULL,
    test_name VARCHAR(100) NOT NULL,
    test_description TEXT,
    expected_result TEXT,
    actual_result TEXT,
    status ENUM('PASS', 'FAIL', 'SKIP') NOT NULL,
    error_message TEXT NULL,
    execution_time_ms INT DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_category_status (test_category, status),
    INDEX idx_test_time (created_at)
) ENGINE=MEMORY COMMENT='Test results tracking table';

DELIMITER $

-- ============================================================================
-- Test Helper Procedures
-- ============================================================================

DROP PROCEDURE IF EXISTS RecordTestResult$

CREATE PROCEDURE RecordTestResult(
    IN p_category VARCHAR(50),
    IN p_test_name VARCHAR(100),
    IN p_description TEXT,
    IN p_expected TEXT,
    IN p_actual TEXT,
    IN p_status VARCHAR(10),
    IN p_error_message TEXT
)
COMMENT 'Record test result and update counters'
BEGIN
    INSERT INTO test_results (
        test_category, test_name, test_description, 
        expected_result, actual_result, status, error_message
    ) VALUES (
        p_category, p_test_name, p_description,
        p_expected, p_actual, p_status, p_error_message
    );
    
    SET @total_tests = @total_tests + 1;
    
    IF p_status = 'PASS' THEN
        SET @passed_tests = @passed_tests + 1;
    ELSE
        SET @failed_tests = @failed_tests + 1;
    END IF;
END$

DROP PROCEDURE IF EXISTS AssertEquals$

CREATE PROCEDURE AssertEquals(
    IN p_category VARCHAR(50),
    IN p_test_name VARCHAR(100),
    IN p_description TEXT,
    IN p_expected TEXT,
    IN p_actual TEXT
)
COMMENT 'Assert that two values are equal'
BEGIN
    DECLARE v_status VARCHAR(10);
    DECLARE v_error_message TEXT DEFAULT NULL;
    
    IF p_expected = p_actual THEN
        SET v_status = 'PASS';
    ELSE
        SET v_status = 'FAIL';
        SET v_error_message = CONCAT('Expected: ', COALESCE(p_expected, 'NULL'), ', Actual: ', COALESCE(p_actual, 'NULL'));
    END IF;
    
    CALL RecordTestResult(p_category, p_test_name, p_description, p_expected, p_actual, v_status, v_error_message);
END$

DROP PROCEDURE IF EXISTS AssertNotNull$

CREATE PROCEDURE AssertNotNull(
    IN p_category VARCHAR(50),
    IN p_test_name VARCHAR(100),
    IN p_description TEXT,
    IN p_actual TEXT
)
COMMENT 'Assert that a value is not null'
BEGIN
    DECLARE v_status VARCHAR(10);
    DECLARE v_error_message TEXT DEFAULT NULL;
    
    IF p_actual IS NOT NULL THEN
        SET v_status = 'PASS';
    ELSE
        SET v_status = 'FAIL';
        SET v_error_message = 'Expected non-null value, but got NULL';
    END IF;
    
    CALL RecordTestResult(p_category, p_test_name, p_description, 'NOT NULL', COALESCE(p_actual, 'NULL'), v_status, v_error_message);
END$

DROP PROCEDURE IF EXISTS AssertTrue$

CREATE PROCEDURE AssertTrue(
    IN p_category VARCHAR(50),
    IN p_test_name VARCHAR(100),
    IN p_description TEXT,
    IN p_condition BOOLEAN
)
COMMENT 'Assert that a condition is true'
BEGIN
    DECLARE v_status VARCHAR(10);
    DECLARE v_error_message TEXT DEFAULT NULL;
    DECLARE v_actual VARCHAR(10);
    
    SET v_actual = CASE WHEN p_condition THEN 'TRUE' ELSE 'FALSE' END;
    
    IF p_condition THEN
        SET v_status = 'PASS';
    ELSE
        SET v_status = 'FAIL';
        SET v_error_message = 'Expected TRUE, but condition evaluated to FALSE';
    END IF;
    
    CALL RecordTestResult(p_category, p_test_name, p_description, 'TRUE', v_actual, v_status, v_error_message);
END$

-- ============================================================================
-- UUID Generation Function Tests (Task 10.1 - Requirement 3.3)
-- ============================================================================

DROP PROCEDURE IF EXISTS TestGenerateUUID$

CREATE PROCEDURE TestGenerateUUID()
COMMENT 'Comprehensive tests for GenerateUUID function'
BEGIN
    DECLARE v_uuid1, v_uuid2, v_uuid3 CHAR(36);
    DECLARE v_uuid_length INT;
    DECLARE v_version_char CHAR(1);
    DECLARE v_variant_char CHAR(1);
    DECLARE v_format_valid BOOLEAN DEFAULT FALSE;
    DECLARE v_uniqueness_count INT DEFAULT 0;
    DECLARE i INT DEFAULT 1;
    
    -- Test 1: UUID Format Validation
    SET v_uuid1 = GenerateUUID(123, 456, 1);
    SET v_uuid_length = LENGTH(v_uuid1);
    
    CALL AssertEquals('UUID_Generation', 'UUID_Length', 'UUID should be exactly 36 characters long', '36', CAST(v_uuid_length AS CHAR));
    
    -- Test 2: UUID Version Validation (should be version 4)
    SET v_version_char = SUBSTRING(v_uuid1, 15, 1);
    CALL AssertEquals('UUID_Generation', 'UUID_Version', 'UUID should be version 4', '4', v_version_char);
    
    -- Test 3: UUID Variant Validation (should be 8, 9, a, or b)
    SET v_variant_char = SUBSTRING(v_uuid1, 20, 1);
    CALL AssertTrue('UUID_Generation', 'UUID_Variant', 'UUID variant bits should be valid (8, 9, a, or b)', 
                   v_variant_char IN ('8', '9', 'a', 'b'));
    
    -- Test 4: UUID Format Pattern Validation
    SET v_format_valid = v_uuid1 REGEXP '^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$';
    CALL AssertTrue('UUID_Generation', 'UUID_Format_Pattern', 'UUID should match standard UUID v4 pattern', v_format_valid);
    
    -- Test 5: Deterministic Behavior (same inputs produce same outputs)
    SET v_uuid2 = GenerateUUID(999, 888, 5);
    SET v_uuid3 = GenerateUUID(999, 888, 5);
    CALL AssertEquals('UUID_Generation', 'Deterministic_Same_Input', 'Same inputs should produce identical UUIDs', v_uuid2, v_uuid3);
    
    -- Test 6: Different Inputs Produce Different Outputs
    SET v_uuid2 = GenerateUUID(999, 888, 5);
    SET v_uuid3 = GenerateUUID(999, 888, 6);
    CALL AssertTrue('UUID_Generation', 'Different_Batch_Different_Output', 'Different batch numbers should produce different UUIDs', v_uuid2 != v_uuid3);
    
    SET v_uuid2 = GenerateUUID(999, 888, 5);
    SET v_uuid3 = GenerateUUID(999, 889, 5);
    CALL AssertTrue('UUID_Generation', 'Different_Seed2_Different_Output', 'Different seed2 values should produce different UUIDs', v_uuid2 != v_uuid3);
    
    SET v_uuid2 = GenerateUUID(999, 888, 5);
    SET v_uuid3 = GenerateUUID(998, 888, 5);
    CALL AssertTrue('UUID_Generation', 'Different_Seed1_Different_Output', 'Different seed1 values should produce different UUIDs', v_uuid2 != v_uuid3);
    
    -- Test 7: UUID Uniqueness Test (small sample)
    CREATE TEMPORARY TABLE uuid_test_sample (
        id INT AUTO_INCREMENT PRIMARY KEY,
        test_uuid CHAR(36),
        seed1 INT,
        seed2 INT,
        batch_num INT,
        UNIQUE KEY uq_uuid (test_uuid)
    );
    
    -- Generate 100 UUIDs with different parameters
    WHILE i <= 100 DO
        INSERT INTO uuid_test_sample (test_uuid, seed1, seed2, batch_num)
        VALUES (GenerateUUID(i, i*2, i*3), i, i*2, i*3);
        SET i = i + 1;
    END WHILE;
    
    SELECT COUNT(*) INTO v_uniqueness_count FROM uuid_test_sample;
    CALL AssertEquals('UUID_Generation', 'UUID_Uniqueness_100_Sample', 'All 100 generated UUIDs should be unique', '100', CAST(v_uniqueness_count AS CHAR));
    
    DROP TEMPORARY TABLE uuid_test_sample;
    
    -- Test 8: Null Input Handling
    -- Note: MySQL functions with DETERMINISTIC don't handle NULL gracefully in this context
    -- We'll test with edge case values instead
    SET v_uuid1 = GenerateUUID(0, 0, 0);
    CALL AssertNotNull('UUID_Generation', 'Zero_Input_Handling', 'UUID generation should handle zero inputs', v_uuid1);
    
    SET v_uuid1 = GenerateUUID(-1, -1, -1);
    CALL AssertNotNull('UUID_Generation', 'Negative_Input_Handling', 'UUID generation should handle negative inputs', v_uuid1);
    
    -- Test 9: Large Input Values
    SET v_uuid1 = GenerateUUID(2147483647, 2147483647, 2147483647);
    CALL AssertNotNull('UUID_Generation', 'Large_Input_Handling', 'UUID generation should handle large input values', v_uuid1);
    
    -- Test 10: UUID Case Consistency (should be lowercase)
    SET v_uuid1 = GenerateUUID(12345, 67890, 111);
    CALL AssertEquals('UUID_Generation', 'UUID_Case_Consistency', 'UUID should be in lowercase', LOWER(v_uuid1), v_uuid1);
    
END$

-- ============================================================================
-- Business Configuration Function Tests (Task 10.1 - Requirement 4.1, 2.1-2.5)
-- ============================================================================

DROP PROCEDURE IF EXISTS TestGetBusinessConfig$

CREATE PROCEDURE TestGetBusinessConfig()
COMMENT 'Comprehensive tests for GetBusinessConfig function'
BEGIN
    DECLARE v_config_value VARCHAR(200);
    
    -- Test 1: Valid Business Types - Compensation Windows
    SET v_config_value = GetBusinessConfig('ecommerce', 'compensation_window');
    CALL AssertEquals('Business_Config', 'Ecommerce_Compensation_Window', 'E-commerce compensation window should be 300 seconds', '300', v_config_value);
    
    SET v_config_value = GetBusinessConfig('payment', 'compensation_window');
    CALL AssertEquals('Business_Config', 'Payment_Compensation_Window', 'Payment compensation window should be 600 seconds', '600', v_config_value);
    
    SET v_config_value = GetBusinessConfig('inventory', 'compensation_window');
    CALL AssertEquals('Business_Config', 'Inventory_Compensation_Window', 'Inventory compensation window should be 180 seconds', '180', v_config_value);
    
    SET v_config_value = GetBusinessConfig('user', 'compensation_window');
    CALL AssertEquals('Business_Config', 'User_Compensation_Window', 'User compensation window should be 120 seconds', '120', v_config_value);
    
    SET v_config_value = GetBusinessConfig('sync', 'compensation_window');
    CALL AssertEquals('Business_Config', 'Sync_Compensation_Window', 'Sync compensation window should be 240 seconds', '240', v_config_value);
    
    -- Test 2: Valid Business Types - Name Prefixes
    SET v_config_value = GetBusinessConfig('ecommerce', 'name_prefix');
    CALL AssertEquals('Business_Config', 'Ecommerce_Name_Prefix', 'E-commerce name prefix should be ECOM', 'ECOM', v_config_value);
    
    SET v_config_value = GetBusinessConfig('payment', 'name_prefix');
    CALL AssertEquals('Business_Config', 'Payment_Name_Prefix', 'Payment name prefix should be PAY', 'PAY', v_config_value);
    
    SET v_config_value = GetBusinessConfig('inventory', 'name_prefix');
    CALL AssertEquals('Business_Config', 'Inventory_Name_Prefix', 'Inventory name prefix should be INV', 'INV', v_config_value);
    
    SET v_config_value = GetBusinessConfig('user', 'name_prefix');
    CALL AssertEquals('Business_Config', 'User_Name_Prefix', 'User name prefix should be USER', 'USER', v_config_value);
    
    SET v_config_value = GetBusinessConfig('sync', 'name_prefix');
    CALL AssertEquals('Business_Config', 'Sync_Name_Prefix', 'Sync name prefix should be SYNC', 'SYNC', v_config_value);
    
    -- Test 3: Valid Business Types - Service Prefixes
    SET v_config_value = GetBusinessConfig('ecommerce', 'service_prefix');
    CALL AssertEquals('Business_Config', 'Ecommerce_Service_Prefix', 'E-commerce service prefix should be ecommerce', 'ecommerce', v_config_value);
    
    SET v_config_value = GetBusinessConfig('payment', 'service_prefix');
    CALL AssertEquals('Business_Config', 'Payment_Service_Prefix', 'Payment service prefix should be payment', 'payment', v_config_value);
    
    -- Test 4: Valid Business Types - Priority Levels
    SET v_config_value = GetBusinessConfig('ecommerce', 'priority_level');
    CALL AssertEquals('Business_Config', 'Ecommerce_Priority', 'E-commerce priority should be high', 'high', v_config_value);
    
    SET v_config_value = GetBusinessConfig('payment', 'priority_level');
    CALL AssertEquals('Business_Config', 'Payment_Priority', 'Payment priority should be critical', 'critical', v_config_value);
    
    SET v_config_value = GetBusinessConfig('inventory', 'priority_level');
    CALL AssertEquals('Business_Config', 'Inventory_Priority', 'Inventory priority should be medium', 'medium', v_config_value);
    
    -- Test 5: Valid Business Types - Max Retry Counts
    SET v_config_value = GetBusinessConfig('ecommerce', 'max_retry_count');
    CALL AssertEquals('Business_Config', 'Ecommerce_Max_Retry', 'E-commerce max retry should be 3', '3', v_config_value);
    
    SET v_config_value = GetBusinessConfig('payment', 'max_retry_count');
    CALL AssertEquals('Business_Config', 'Payment_Max_Retry', 'Payment max retry should be 5', '5', v_config_value);
    
    SET v_config_value = GetBusinessConfig('sync', 'max_retry_count');
    CALL AssertEquals('Business_Config', 'Sync_Max_Retry', 'Sync max retry should be 4', '4', v_config_value);
    
    -- Test 6: Invalid Business Type
    SET v_config_value = GetBusinessConfig('invalid_type', 'compensation_window');
    CALL AssertEquals('Business_Config', 'Invalid_Business_Type', 'Invalid business type should return NULL', NULL, v_config_value);
    
    -- Test 7: Invalid Config Key
    SET v_config_value = GetBusinessConfig('ecommerce', 'invalid_key');
    CALL AssertEquals('Business_Config', 'Invalid_Config_Key', 'Invalid config key should return NULL', NULL, v_config_value);
    
    -- Test 8: Case Sensitivity Test
    SET v_config_value = GetBusinessConfig('ECOMMERCE', 'compensation_window');
    CALL AssertEquals('Business_Config', 'Case_Sensitivity_Business_Type', 'Business type should be case sensitive', NULL, v_config_value);
    
    SET v_config_value = GetBusinessConfig('ecommerce', 'COMPENSATION_WINDOW');
    CALL AssertEquals('Business_Config', 'Case_Sensitivity_Config_Key', 'Config key should be case sensitive', NULL, v_config_value);
    
    -- Test 9: Empty String Inputs
    SET v_config_value = GetBusinessConfig('', 'compensation_window');
    CALL AssertEquals('Business_Config', 'Empty_Business_Type', 'Empty business type should return NULL', NULL, v_config_value);
    
    SET v_config_value = GetBusinessConfig('ecommerce', '');
    CALL AssertEquals('Business_Config', 'Empty_Config_Key', 'Empty config key should return NULL', NULL, v_config_value);
    
    -- Test 10: All Business Types Have Required Configurations
    -- Test that all business types have the essential configuration keys
    CALL AssertNotNull('Business_Config', 'Ecommerce_Has_All_Configs', 'E-commerce should have default currency', GetBusinessConfig('ecommerce', 'default_currency'));
    CALL AssertNotNull('Business_Config', 'Payment_Has_All_Configs', 'Payment should have default currency', GetBusinessConfig('payment', 'default_currency'));
    CALL AssertNotNull('Business_Config', 'Inventory_Has_All_Configs', 'Inventory should have default currency', GetBusinessConfig('inventory', 'default_currency'));
    CALL AssertNotNull('Business_Config', 'User_Has_All_Configs', 'User should have default currency', GetBusinessConfig('user', 'default_currency'));
    CALL AssertNotNull('Business_Config', 'Sync_Has_All_Configs', 'Sync should have default currency', GetBusinessConfig('sync', 'default_currency'));
    
END$

-- ============================================================================
-- Context Data Generation Function Tests (Task 10.1 - Requirement 2.1-2.5)
-- ============================================================================

DROP PROCEDURE IF EXISTS TestGenerateContextData$

CREATE PROCEDURE TestGenerateContextData()
COMMENT 'Comprehensive tests for GenerateContextData function'
BEGIN
    DECLARE v_context_json JSON;
    DECLARE v_test_saga_id CHAR(36) DEFAULT '12345678-1234-4567-8901-123456789012';
    DECLARE v_json_valid BOOLEAN DEFAULT FALSE;
    DECLARE v_extracted_value VARCHAR(200);
    
    -- Test 1: E-commerce Context Data Generation
    SET v_context_json = GenerateContextData('ecommerce', v_test_saga_id);
    CALL AssertNotNull('Context_Generation', 'Ecommerce_Context_Not_Null', 'E-commerce context should not be null', CAST(v_context_json AS CHAR));
    
    -- Test e-commerce required fields
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.orderId'));
    CALL AssertTrue('Context_Generation', 'Ecommerce_OrderId_Present', 'E-commerce context should have orderId', v_extracted_value IS NOT NULL AND v_extracted_value LIKE 'ORD-%');
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.customerId'));
    CALL AssertTrue('Context_Generation', 'Ecommerce_CustomerId_Present', 'E-commerce context should have customerId', v_extracted_value IS NOT NULL AND v_extracted_value LIKE 'CUST-%');
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.amount'));
    CALL AssertNotNull('Context_Generation', 'Ecommerce_Amount_Present', 'E-commerce context should have amount', v_extracted_value);
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.currency'));
    CALL AssertEquals('Context_Generation', 'Ecommerce_Currency_CNY', 'E-commerce currency should be CNY', 'CNY', v_extracted_value);
    
    -- Test 2: Payment Context Data Generation
    SET v_context_json = GenerateContextData('payment', v_test_saga_id);
    CALL AssertNotNull('Context_Generation', 'Payment_Context_Not_Null', 'Payment context should not be null', CAST(v_context_json AS CHAR));
    
    -- Test payment required fields
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.paymentId'));
    CALL AssertTrue('Context_Generation', 'Payment_PaymentId_Present', 'Payment context should have paymentId', v_extracted_value IS NOT NULL AND v_extracted_value LIKE 'PAY-%');
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.accountId'));
    CALL AssertTrue('Context_Generation', 'Payment_AccountId_Present', 'Payment context should have accountId', v_extracted_value IS NOT NULL AND v_extracted_value LIKE 'ACC-%');
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.paymentMethod'));
    CALL AssertTrue('Context_Generation', 'Payment_Method_Valid', 'Payment method should be valid', 
                   v_extracted_value IN ('credit_card', 'debit_card', 'bank_transfer', 'digital_wallet'));
    
    -- Test 3: Inventory Context Data Generation
    SET v_context_json = GenerateContextData('inventory', v_test_saga_id);
    CALL AssertNotNull('Context_Generation', 'Inventory_Context_Not_Null', 'Inventory context should not be null', CAST(v_context_json AS CHAR));
    
    -- Test inventory required fields
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.inventoryId'));
    CALL AssertTrue('Context_Generation', 'Inventory_InventoryId_Present', 'Inventory context should have inventoryId', v_extracted_value IS NOT NULL AND v_extracted_value LIKE 'INV-%');
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.warehouseId'));
    CALL AssertTrue('Context_Generation', 'Inventory_WarehouseId_Present', 'Inventory context should have warehouseId', v_extracted_value IS NOT NULL AND v_extracted_value LIKE 'WH-%');
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.operation'));
    CALL AssertTrue('Context_Generation', 'Inventory_Operation_Valid', 'Inventory operation should be valid', 
                   v_extracted_value IN ('reserve', 'release', 'transfer', 'adjust'));
    
    -- Test 4: User Context Data Generation
    SET v_context_json = GenerateContextData('user', v_test_saga_id);
    CALL AssertNotNull('Context_Generation', 'User_Context_Not_Null', 'User context should not be null', CAST(v_context_json AS CHAR));
    
    -- Test user required fields
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.userId'));
    CALL AssertTrue('Context_Generation', 'User_UserId_Present', 'User context should have userId', v_extracted_value IS NOT NULL AND v_extracted_value LIKE 'USER-%');
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.email'));
    CALL AssertTrue('Context_Generation', 'User_Email_Valid', 'User email should be valid format', v_extracted_value IS NOT NULL AND v_extracted_value LIKE '%@example.com');
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.accountType'));
    CALL AssertTrue('Context_Generation', 'User_AccountType_Valid', 'User account type should be valid', 
                   v_extracted_value IN ('basic', 'premium', 'enterprise'));
    
    -- Test 5: Sync Context Data Generation
    SET v_context_json = GenerateContextData('sync', v_test_saga_id);
    CALL AssertNotNull('Context_Generation', 'Sync_Context_Not_Null', 'Sync context should not be null', CAST(v_context_json AS CHAR));
    
    -- Test sync required fields
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.syncId'));
    CALL AssertTrue('Context_Generation', 'Sync_SyncId_Present', 'Sync context should have syncId', v_extracted_value IS NOT NULL AND v_extracted_value LIKE 'SYNC-%');
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.sourceSystem'));
    CALL AssertTrue('Context_Generation', 'Sync_SourceSystem_Valid', 'Sync source system should be valid', 
                   v_extracted_value IN ('crm_system', 'erp_system', 'warehouse_system', 'analytics_system'));
    
    -- Test 6: Invalid Business Type
    SET v_context_json = GenerateContextData('invalid_type', v_test_saga_id);
    CALL AssertEquals('Context_Generation', 'Invalid_Business_Type_Returns_Null', 'Invalid business type should return NULL', NULL, CAST(v_context_json AS CHAR));
    
    -- Test 7: Deterministic Behavior (same inputs produce same outputs)
    DECLARE v_context1, v_context2 JSON;
    SET v_context1 = GenerateContextData('ecommerce', v_test_saga_id);
    SET v_context2 = GenerateContextData('ecommerce', v_test_saga_id);
    CALL AssertEquals('Context_Generation', 'Deterministic_Same_Input', 'Same inputs should produce identical context', CAST(v_context1 AS CHAR), CAST(v_context2 AS CHAR));
    
    -- Test 8: Different Saga IDs Produce Different Context
    SET v_context1 = GenerateContextData('ecommerce', '12345678-1234-4567-8901-123456789012');
    SET v_context2 = GenerateContextData('ecommerce', '87654321-4321-7654-1098-210987654321');
    CALL AssertTrue('Context_Generation', 'Different_SagaId_Different_Context', 'Different saga IDs should produce different context', 
                   CAST(v_context1 AS CHAR) != CAST(v_context2 AS CHAR));
    
    -- Test 9: JSON Structure Validation
    -- Test that generated JSON is valid and can be parsed
    SET v_context_json = GenerateContextData('ecommerce', v_test_saga_id);
    SET v_json_valid = JSON_VALID(v_context_json);
    CALL AssertTrue('Context_Generation', 'Ecommerce_JSON_Valid', 'E-commerce context should be valid JSON', v_json_valid);
    
    SET v_context_json = GenerateContextData('payment', v_test_saga_id);
    SET v_json_valid = JSON_VALID(v_context_json);
    CALL AssertTrue('Context_Generation', 'Payment_JSON_Valid', 'Payment context should be valid JSON', v_json_valid);
    
    -- Test 10: Nested Object Structure
    SET v_context_json = GenerateContextData('ecommerce', v_test_saga_id);
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.items[0].productId'));
    CALL AssertTrue('Context_Generation', 'Ecommerce_Nested_Items', 'E-commerce should have nested items array with productId', v_extracted_value IS NOT NULL AND v_extracted_value LIKE 'PROD-%');
    
    SET v_extracted_value = JSON_UNQUOTE(JSON_EXTRACT(v_context_json, '$.shippingAddress.city'));
    CALL AssertTrue('Context_Generation', 'Ecommerce_Nested_Address', 'E-commerce should have nested shipping address with city', 
                   v_extracted_value IN ('Beijing', 'Shanghai', 'Guangzhou'));
    
END$

DELIMITER ;

-- ============================================================================
-- Test Execution
-- ============================================================================

SELECT 
    '============================================================================' as separator,
    'Starting Core Functions Unit Tests' as status,
    'Testing GenerateUUID, GetBusinessConfig, and GenerateContextData functions' as description,
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') as start_time,
    '============================================================================' as separator2;

-- Execute all test suites
CALL TestGenerateUUID();
CALL TestGetBusinessConfig();
CALL TestGenerateContextData();

-- ============================================================================
-- Test Results Summary
-- ============================================================================

SELECT 
    '============================================================================' as separator,
    'CORE FUNCTIONS UNIT TEST RESULTS SUMMARY' as report_title,
    '============================================================================' as separator2;

-- Overall test statistics
SELECT 
    'OVERALL TEST STATISTICS' as summary_type,
    @total_tests as total_tests_executed,
    @passed_tests as tests_passed,
    @failed_tests as tests_failed,
    CONCAT(ROUND((@passed_tests / @total_tests) * 100, 1), '%') as pass_rate,
    TIMESTAMPDIFF(SECOND, @test_start_time, NOW()) as total_execution_time_seconds,
    CASE 
        WHEN @failed_tests = 0 THEN 'ALL TESTS PASSED ✓'
        ELSE CONCAT(@failed_tests, ' TESTS FAILED ✗')
    END as overall_status;

-- Test results by category
SELECT 
    'TEST RESULTS BY CATEGORY' as summary_type,
    test_category,
    COUNT(*) as total_tests,
    SUM(CASE WHEN status = 'PASS' THEN 1 ELSE 0 END) as passed,
    SUM(CASE WHEN status = 'FAIL' THEN 1 ELSE 0 END) as failed,
    CONCAT(ROUND((SUM(CASE WHEN status = 'PASS' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1), '%') as pass_rate
FROM test_results
GROUP BY test_category
ORDER BY test_category;

-- Failed tests details (if any)
SELECT 
    'FAILED TESTS DETAILS' as detail_type,
    test_category,
    test_name,
    test_description,
    expected_result,
    actual_result,
    error_message
FROM test_results
WHERE status = 'FAIL'
ORDER BY test_category, test_name;

-- Test execution summary
SELECT 
    'TEST EXECUTION COMPLETED' as status,
    CASE 
        WHEN @failed_tests = 0 THEN 'All core functions are working correctly and meet requirements'
        ELSE CONCAT('Some tests failed - review failed test details above for issues to address')
    END as conclusion,
    'Core functions tested: GenerateUUID, GetBusinessConfig, GenerateContextData' as functions_tested,
    'Requirements validated: 3.3 (UUID generation), 4.1 (compensation context), 2.1-2.5 (business scenarios)' as requirements_covered;

-- Cleanup
DROP TEMPORARY TABLE test_results;

SELECT 
    '============================================================================' as separator,
    'Core Functions Unit Tests Complete' as status,
    'Test results have been displayed above' as message,
    '============================================================================' as separator2;