# Saga 分布式事务服务配置 (Docker 环境) - V2 版本
saga:
  # 乐观锁配置
  optimisticLock:
    enabled: true               # 切换到乐观锁进行完整性能对比测试
    maxRetries: 8               # 100万级数据增加重试次数
    baseDelay: "3ms"            # 减少基础延迟提升性能
    maxDelay: "150ms"           # 适当增加最大延迟
    backoffMultiplier: 2.0      # 标准退避倍数

  # 补偿配置
  compensation:
    timeout: "30s"              # 补偿操作超时时间
    defaultWindowSec: 300       # 默认补偿窗口期（秒）
    reportMaxRetries: 3         # 补偿上报最大重试次数
    reportRetryDelay: "1s"      # 补偿上报重试延迟

    # 补偿处理服务配置 (原 recovery 配置)
    processing:
      enabled: true             # Docker 环境启用补偿处理服务
      taskTimeout: "5m"         # 任务超时时间
      processingInterval: "30s" # 处理检查间隔 (原 recoveryInterval)
      maxConcurrentTasks: 5     # Docker 环境适中的并发数

    # 回滚配置
    rollback:
      maxRetries: 3             # 回滚最大重试次数
      timeout: "30s"            # 回滚操作超时时间

  # 重试配置 (V2 新增)
  retry:
    maxRetries: 3               # 最大重试次数
    initialInterval: "1s"       # 初始重试间隔
    maxInterval: "30s"          # 最大重试间隔
    multiplier: 2.0             # 指数退避倍数

# 数据库配置 (使用 Docker 容器名) - 性能优化版
database:
  default:
    link: "mysql:root:12345678a@tcp(mysql:3306)/saga?timeout=10s&readTimeout=10s&writeTimeout=10s"
    debug: false                  # 关闭调试模式提升性能
    maxIdle: 20                   # 增加最大空闲连接数
    maxOpen: 100                  # 大幅增加最大打开连接数
    maxLifetime: "30m"            # 减少连接生存时间，避免长连接问题
    maxIdleTime: "10m"            # 设置空闲连接超时时间
    connMaxIdleTime: "5m"         # 连接池空闲超时
    
# 服务器配置 - 性能优化版
server:
  address: ":8080"
  readTimeout: "60s"              # 增加读取超时，避免高并发下超时
  writeTimeout: "60s"             # 增加写入超时
  idleTimeout: "120s"             # 增加空闲超时
  maxHeaderBytes: 2097152         # 增加最大请求头大小 (2MB)
  keepAlive: true                 # 启用HTTP Keep-Alive
  serverAgent: "Saga-Server"      # 设置服务器标识
  
# 日志配置
logger:
  level: "info"                   # Docker 环境使用 info 级别
  stdout: true

# HTTP客户端配置 (用于补偿调用)
httpclient:
  timeout: "30s"                  # HTTP客户端超时
  keepAlive: "30s"                # Keep-Alive时间
  maxIdleConns: 100               # 最大空闲连接数
  maxIdleConnsPerHost: 10         # 每个主机最大空闲连接数
  maxConnsPerHost: 50             # 每个主机最大连接数

# 性能监控配置
pprof:
  enabled: true                   # 启用性能分析
  address: ":6060"                # pprof 监听地址

# Go运行时配置
runtime:
  gomaxprocs: 0                   # 自动检测CPU核心数
  gogc: 100                       # GC目标百分比
  gomemlimit: "8GiB"              # 内存限制
