// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package saga_transactions

import (
	"context"

	"saga/api/saga_transactions/v1"
	"saga/api/saga_transactions/v2"
)

type ISagaTransactionsV1 interface {
	CreateSagaTransaction(ctx context.Context, req *v1.CreateSagaTransactionReq) (res *v1.CreateSagaTransactionRes, err error)
	ReportCompensation(ctx context.Context, req *v1.ReportCompensationReq) (res *v1.ReportCompensationRes, err error)
	GetSagaTransaction(ctx context.Context, req *v1.GetSagaTransactionReq) (res *v1.GetSagaTransactionRes, err error)
	CommitSagaTransaction(ctx context.Context, req *v1.CommitSagaTransactionReq) (res *v1.CommitSagaTransactionRes, err error)
	RollBackSagaTransaction(ctx context.Context, req *v1.RollBackSagaTransactionReq) (res *v1.RollBackSagaTransactionRes, err error)
}

type ISagaTransactionsV2 interface {
	CreateSagaTransaction(ctx context.Context, req *v2.CreateSagaTransactionReq) (res *v2.CreateSagaTransactionRes, err error)
	ReportCompensation(ctx context.Context, req *v2.ReportCompensationReq) (res *v2.ReportCompensationRes, err error)
	GetSagaTransaction(ctx context.Context, req *v2.GetSagaTransactionReq) (res *v2.GetSagaTransactionRes, err error)
	CommitSagaTransaction(ctx context.Context, req *v2.CommitSagaTransactionReq) (res *v2.CommitSagaTransactionRes, err error)
	RollBackSagaTransaction(ctx context.Context, req *v2.RollBackSagaTransactionReq) (res *v2.RollBackSagaTransactionRes, err error)
	GetLockStrategy(ctx context.Context, req *v2.GetLockStrategyReq) (res *v2.GetLockStrategyRes, err error)
	GetServiceHealth(ctx context.Context, req *v2.GetServiceHealthReq) (res *v2.GetServiceHealthRes, err error)
}
