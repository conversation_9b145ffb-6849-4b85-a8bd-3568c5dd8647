
# CLI tool, only in development environment.
# https://goframe.org/docs/cli
gfcli:
  gen:
    dao:
      - link: "mysql:root:12345678a@tcp(127.0.0.1:3306)/saga"
        descriptionTag: true

  docker:
    build: "-a amd64 -s linux -p temp -ew"
    tagPrefixes:
      - my.image.pub/my-app

# 应用程序配置
saga:
  # 补偿处理服务配置
  compensation:
    processing:
      enabled: true             # 是否启用补偿处理服务
      taskTimeout: "5m"         # 任务超时时间
      processingInterval: "30s" # 处理检查间隔
      maxConcurrentTasks: 10    # 最大并发任务数

    # 回滚配置
    rollback:
      maxRetries: 3           # 默认最大重试次数
      timeout: "30s"          # 默认补偿操作超时时间

  # 乐观锁配置
  optimisticLock:
    enabled: false            # 是否启用乐观锁模式（默认使用悲观锁）
    maxRetries: 3             # 乐观锁最大重试次数
    baseDelay: "10ms"         # 乐观锁重试基础延迟时间
    maxDelay: "100ms"         # 乐观锁重试最大延迟时间
    backoffMultiplier: 2.0    # 指数退避倍数