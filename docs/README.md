# Saga 分布式事务系统文档

本目录包含 Saga 分布式事务系统的完整文档。文档按功能分类组织，便于查找和维护。

## 📁 文档结构

### 🏗️ 架构设计 (architecture/)
- **service-layer-architecture.md** - 服务层架构设计
- **saga-pattern-comparison.md** - Saga模式对比分析
- **saga-pattern-requirements.md** - Saga模式需求分析
- **step-index-management.md** - 步骤索引管理机制
- **step-index-immutability.md** - 步骤索引不可变性设计

### ⚡ 性能优化 (performance/)
- **performance-benchmarks-and-optimization.md** - 性能基准测试与优化
- **performance-testing-plan.md** - 性能测试计划
- **performance-test-report-template.md** - 性能测试报告模板
- **wrk-tool-selection-summary.md** - 性能测试工具选择总结
- **test-data-generation.md** - 测试数据生成指南

### 🔧 运维监控 (operations/)
- **mysql_lock_transaction_report.md** - MySQL锁和事务使用情况报告
- **resource-configuration.md** - 资源配置指南
- **rollback-configuration.md** - 回滚配置说明
- **compensation-processor-service.md** - 补偿处理服务
- **saga-retry-config-guide.md** - Saga重试配置指南

### 📡 API文档 (api/)
- **saga-transactions-api.md** - Saga事务API文档
- **saga-service-usage.md** - Saga服务使用指南
- **compensation-service-integration.md** - 补偿服务集成指南
- **concurrent-reporting-guide.md** - 并发上报指南

### 🔍 故障排除 (troubleshooting/)
- **async-timing-race-conditions.md** - 异步时序竞态条件问题
- **compensation-window-issues.md** - 补偿窗口问题
- **rollback-transaction-fix.md** - 回滚事务修复
- **test-report-auto-mode-issues.md** - 测试报告自动模式问题
- **compensation-retry-mechanism.md** - 补偿重试机制问题

## 📋 快速导航

### 新用户入门
1. [Saga模式需求分析](saga-pattern-requirements.md)
2. [服务层架构设计](architecture/service-layer-architecture.md)
3. [Saga服务使用指南](api/saga-service-usage.md)

### 开发者指南
1. [Saga事务API文档](api/saga-transactions-api.md)
2. [步骤索引管理机制](architecture/step-index-management.md)
3. [补偿服务集成指南](api/compensation-service-integration.md)
4. [并发上报指南](api/concurrent-reporting-guide.md)

### 运维人员
1. [资源配置指南](operations/resource-configuration.md)
2. [MySQL锁和事务监控](operations/mysql_lock_transaction_report.md)
3. [补偿处理服务](operations/compensation-processor-service.md)
4. [Saga重试配置指南](operations/saga-retry-config-guide.md)

### 性能调优
1. [性能测试计划](performance/performance-testing-plan.md)
2. [性能基准测试与优化](performance/performance-benchmarks-and-optimization.md)
3. [性能测试工具选择](performance/wrk-tool-selection-summary.md)
4. [测试数据生成指南](performance/test-data-generation.md)

## 🔄 文档维护

### 文档分类规则
- **architecture/**: 系统架构、设计模式、核心机制
- **performance/**: 性能测试、基准测试、优化指南
- **operations/**: 运维配置、监控报告、部署指南
- **api/**: API文档、使用指南、集成说明
- **troubleshooting/**: 问题诊断、故障排除、修复指南

### 文档命名规范
- 使用小写字母和连字符
- 文件名应清晰描述内容
- 使用 `.md` 扩展名

### 更新指南
1. 新增文档时，请更新本README.md
2. 确保文档分类正确
3. 添加适当的交叉引用
4. 保持文档格式一致

## 📞 联系方式

如有文档相关问题或建议，请通过以下方式联系：
- 创建 Issue
- 提交 Pull Request
- 项目维护者邮箱

---

*最后更新: 2025-08-02*
