# 补偿调用重试机制

## 概述

Saga 分布式事务服务现在支持智能的补偿调用重试机制，采用指数退避算法来处理网络故障和临时性错误，提高系统的健壮性和可靠性。

## 重试策略

### 1. 重试条件

系统会在以下情况下自动重试补偿调用：

#### 网络错误（自动重试）
- 连接被拒绝 (connection refused)
- 连接超时 (connection timeout)
- 域名解析失败 (no such host)
- 网络不可达 (network unreachable)
- 连接被重置 (connection reset by peer)
- I/O 超时 (i/o timeout)
- 上下文超时 (context deadline exceeded)

#### HTTP 5xx 错误（自动重试）
- 500 内部服务器错误
- 502 网关错误
- 503 服务不可用
- 504 网关超时
- 其他 5xx 服务器错误

### 2. 不重试的情况

以下情况不会进行重试：

#### HTTP 4xx 错误（客户端错误）
- 400 请求格式错误
- 401 未授权
- 403 禁止访问
- 404 资源未找到
- 其他 4xx 客户端错误

#### 其他错误
- 非网络相关的业务逻辑错误
- 数据格式错误

## 指数退避算法

### 算法公式

```
delay = initial_interval * (multiplier ^ attempt)
```

- `initial_interval`: 初始重试间隔
- `multiplier`: 指数退避倍数
- `attempt`: 当前重试次数（从0开始）

### 最大延迟限制

为了避免无限增长的延迟时间，系统会限制最大重试间隔：

```go
if delay > max_retry_interval {
    delay = max_retry_interval
}
```

## 默认配置

```go
const (
    DefaultMaxRetries           = 3                // 默认最大重试次数
    DefaultInitialRetryInterval = time.Second      // 默认初始重试间隔（1秒）
    DefaultMaxRetryInterval     = 30 * time.Second // 默认最大重试间隔（30秒）
    DefaultRetryMultiplier      = 2.0              // 默认指数退避倍数
)
```

### 重试时间示例

基于默认配置，重试时间如下：

| 重试次数 | 延迟时间 | 计算过程 |
|---------|---------|---------|
| 1 | 1秒 | 1s * 2^0 = 1s |
| 2 | 2秒 | 1s * 2^1 = 2s |
| 3 | 4秒 | 1s * 2^2 = 4s |
| 4 | 8秒 | 1s * 2^3 = 8s |
| 5 | 16秒 | 1s * 2^4 = 16s |
| 6 | 30秒 | 1s * 2^5 = 32s（限制为30秒） |

## 使用方法

### 1. 使用默认配置

```go
// 创建服务实例（使用默认重试配置）
service := NewSagaTransactions()

// 补偿调用会自动使用默认重试配置
// - 最大重试次数：3
// - 初始重试间隔：1秒
// - 最大重试间隔：30秒
// - 指数退避倍数：2.0
```

### 2. 使用自定义配置

```go
// 创建具有自定义重试配置的服务实例
service := NewSagaTransactionsWithRetryConfig(
    5,                     // 最大重试次数
    500*time.Millisecond,  // 初始重试间隔
    1*time.Minute,         // 最大重试间隔
    3.0,                   // 指数退避倍数
)
```

### 3. 配置说明

#### 最大重试次数 (maxRetries)
- 建议值：3-5次
- 过小：可能无法处理临时性故障
- 过大：可能导致长时间阻塞

#### 初始重试间隔 (initialRetryInterval)
- 建议值：100ms-2s
- 过小：可能对目标服务造成压力
- 过大：影响故障恢复速度

#### 最大重试间隔 (maxRetryInterval)
- 建议值：30s-2min
- 避免无限长的等待时间
- 平衡恢复速度和资源消耗

#### 指数退避倍数 (retryMultiplier)
- 建议值：2.0-3.0
- 过小：退避效果不明显
- 过大：延迟增长过快

## 日志记录

系统会记录详细的重试日志，便于调试和监控：

### 开始调用
```
[INFO] 开始调用补偿接口: http://example.com/compensate, 超时时间: 5000ms, 最大重试次数: 3, SagaId: saga-123, StepId: step-456, StepIndex: 1
```

### 重试过程
```
[ERROR] 补偿调用失败: http://example.com/compensate, 第 1 次尝试, HTTP状态码: 500, 响应: Internal Server Error
[INFO] 服务器错误（500），1s 后重试，SagaId: saga-123, StepId: step-456
[INFO] 补偿调用重试，第 1 次，SagaId: saga-123, StepId: step-456, Endpoint: http://example.com/compensate
```

### 最终结果
```
[INFO] 补偿调用重试成功: http://example.com/compensate, 总尝试次数: 2, HTTP状态码: 200, SagaId: saga-123, StepId: step-456
```

或者

```
[ERROR] 补偿调用最终失败: http://example.com/compensate, 总尝试次数: 3, 最终状态码: 500, SagaId: saga-123, StepId: step-456
```

## 监控指标

建议监控以下指标来评估重试机制的效果：

### 重试相关指标
- 重试成功率：重试后成功的请求比例
- 平均重试次数：每个补偿调用的平均重试次数
- 重试失败率：经过重试仍然失败的请求比例
- 重试延迟时间：重试过程的总延迟时间

### 补偿调用指标
- 补偿调用成功率：包含重试后的总成功率
- 补偿调用延迟：包含重试时间的总延迟
- 网络错误频率：网络错误的发生频率
- 服务错误频率：5xx错误的发生频率

## 最佳实践

### 1. 合理设置重试次数

```go
// 对于关键业务，可以增加重试次数
criticalService := NewSagaTransactionsWithRetryConfig(
    5,                     // 关键业务：更多重试次数
    200*time.Millisecond,  // 较短的初始间隔
    1*time.Minute,         // 适中的最大间隔
    2.0,                   // 标准指数退避
)

// 对于非关键业务，可以减少重试次数
normalService := NewSagaTransactionsWithRetryConfig(
    2,                     // 非关键业务：较少重试次数
    1*time.Second,         // 较长的初始间隔
    30*time.Second,        // 较短的最大间隔
    2.0,                   // 标准指数退避
)
```

### 2. 根据网络环境调整配置

```go
// 内网环境：网络较稳定，可以使用较短的间隔
intranetService := NewSagaTransactionsWithRetryConfig(
    3,
    100*time.Millisecond,  // 内网延迟低
    10*time.Second,        // 较短的最大间隔
    2.0,
)

// 外网环境：网络不稳定，使用较长的间隔
internetService := NewSagaTransactionsWithRetryConfig(
    5,
    1*time.Second,         // 外网延迟高
    2*time.Minute,         // 较长的最大间隔
    2.0,
)
```

### 3. 监控和告警

```go
// 设置监控指标
func monitorRetryMetrics(service *SagaTransactionsService) {
    // 监控重试成功率
    retrySuccessRate := calculateRetrySuccessRate()
    if retrySuccessRate < 0.8 {
        alert("重试成功率过低", retrySuccessRate)
    }
    
    // 监控平均重试次数
    avgRetryCount := calculateAverageRetryCount()
    if avgRetryCount > 2.0 {
        alert("平均重试次数过高", avgRetryCount)
    }
}
```

### 4. 补偿接口优化

为了配合重试机制，补偿接口也需要进行优化：

```go
// 补偿接口应该实现幂等性
func compensateHandler(w http.ResponseWriter, r *http.Request) {
    stepId := r.Header.Get("X-Step-Id")
    
    // 检查是否已经处理过
    if isAlreadyProcessed(stepId) {
        w.WriteHeader(http.StatusOK)
        return
    }
    
    // 执行补偿逻辑
    if err := executeCompensation(r); err != nil {
        if isRetryableError(err) {
            // 返回 5xx 错误，触发重试
            w.WriteHeader(http.StatusInternalServerError)
        } else {
            // 返回 4xx 错误，不触发重试
            w.WriteHeader(http.StatusBadRequest)
        }
        return
    }
    
    // 记录已处理状态
    markAsProcessed(stepId)
    w.WriteHeader(http.StatusOK)
}
```

## 故障排查

### 1. 重试次数过多

**症状**：日志显示大量重试，但最终都失败了

**可能原因**：
- 目标服务持续不可用
- 网络配置问题
- 重试配置不合理

**解决方法**：
- 检查目标服务的健康状态
- 验证网络连接
- 调整重试配置

### 2. 重试间隔过长

**症状**：补偿调用耗时过长，影响整体性能

**可能原因**：
- 指数退避倍数过大
- 最大重试间隔设置过高
- 重试次数过多

**解决方法**：
- 减小指数退避倍数
- 降低最大重试间隔
- 减少重试次数

### 3. 重试无效

**症状**：重试后仍然失败，但错误类型应该可以重试

**可能原因**：
- 网络错误识别逻辑有问题
- 目标服务返回错误的状态码
- 超时时间设置不合理

**解决方法**：
- 检查错误类型识别逻辑
- 与目标服务方确认状态码使用
- 调整超时时间

## 总结

通过指数退避重试机制，Saga 分布式事务服务能够：

1. **提高可靠性**：自动处理临时性网络故障
2. **减少人工干预**：降低运维成本
3. **优化性能**：智能的退避策略避免雪崩效应
4. **增强监控**：详细的日志记录便于问题排查

合理配置重试参数，结合完善的监控告警，可以显著提升分布式事务系统的稳定性和用户体验。 