# Enhanced Data Initialization Script - Troubleshooting Guide

## Table of Contents
1. [Common Error Messages](#common-error-messages)
2. [Performance Issues](#performance-issues)
3. [Memory and Resource Problems](#memory-and-resource-problems)
4. [Configuration Issues](#configuration-issues)
5. [Data Integrity Problems](#data-integrity-problems)
6. [System-Specific Issues](#system-specific-issues)
7. [Recovery Procedures](#recovery-procedures)
8. [Diagnostic Tools](#diagnostic-tools)

## Common Error Messages

### 1. MySQL Connection Errors

#### Error: "MySQL server has gone away" (Error 2006)
**Cause:** Connection timeout during long-running operations

**Solutions:**
```sql
-- Increase timeout settings
SET SESSION wait_timeout = 28800;        -- 8 hours
SET SESSION interactive_timeout = 28800;  -- 8 hours
SET SESSION net_read_timeout = 600;      -- 10 minutes
SET SESSION net_write_timeout = 600;     -- 10 minutes

-- Or reduce batch size for faster processing
SET @batch_size = 10000;  -- Smaller batches complete faster
```

**Prevention:**
```bash
# Add to MySQL configuration
[mysqld]
wait_timeout = 28800
interactive_timeout = 28800
net_read_timeout = 600
net_write_timeout = 600
```

#### Error: "Lost connection to MySQL server" (Error 2013)
**Cause:** Network interruption or server restart

**Solutions:**
```bash
# Check MySQL server status
systemctl status mysql

# Check network connectivity
ping your-mysql-host

# Restart MySQL if needed
sudo systemctl restart mysql

# Re-run script with smaller batches
mysql -u username -p database_name -e "SET @batch_size = 5000;" < enhanced-initialize-test-data.sql
```

### 2. Memory Errors

#### Error: "Out of memory" (Error 1041)
**Cause:** Insufficient system memory for operation

**Solutions:**
```sql
-- Reduce memory usage
SET @batch_size = 5000;              -- Much smaller batches
SET @tmp_table_size = '64M';         -- Reduce temp table size
SET @max_heap_table_size = '64M';    -- Reduce heap table size
SET sql_big_tables = 1;              -- Use disk-based temp tables

-- Disable memory-intensive features
SET @enable_validation = FALSE;
SET @log_performance_metrics = FALSE;
```

**System-level solutions:**
```bash
# Check available memory
free -h

# Clear system cache if safe to do so
sudo sync && sudo sysctl vm.drop_caches=3

# Add swap space if needed
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

#### Error: "The table 'temp_table' is full" (Error 1114)
**Cause:** Temporary table size limit exceeded

**Solutions:**
```sql
-- Increase temporary table limits
SET tmp_table_size = 1073741824;      -- 1GB
SET max_heap_table_size = 1073741824; -- 1GB

-- Or force disk-based temporary tables
SET sql_big_tables = 1;
SET big_tables = 1;

-- Reduce batch size
SET @batch_size = 10000;
```

### 3. Disk Space Errors

#### Error: "No space left on device" (Error 28)
**Cause:** Insufficient disk space

**Solutions:**
```bash
# Check disk usage
df -h

# Find large files to clean up
du -h /var/lib/mysql/ | sort -rh | head -20

# Clean MySQL binary logs if safe
mysql -e "PURGE BINARY LOGS BEFORE DATE(NOW() - INTERVAL 7 DAY);"

# Reduce data generation volume
mysql -e "SET @total_sagas = 100000;" < enhanced-initialize-test-data.sql
```

### 4. Permission Errors

#### Error: "Access denied" (Error 1045)
**Cause:** Insufficient database privileges

**Required privileges:**
```sql
-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON database_name.* TO 'username'@'host';
GRANT CREATE TEMPORARY TABLES ON database_name.* TO 'username'@'host';
GRANT ALTER ON database_name.* TO 'username'@'host';
GRANT PROCESS ON *.* TO 'username'@'host';
FLUSH PRIVILEGES;
```

#### Error: "Table doesn't exist" (Error 1146)
**Cause:** Missing required tables

**Solutions:**
```sql
-- Check if tables exist
SHOW TABLES LIKE 'saga_%';

-- Create missing tables (example structure)
CREATE TABLE IF NOT EXISTS saga_transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    saga_id CHAR(36) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    saga_status ENUM('pending', 'running', 'completed', 'compensating', 'failed') NOT NULL,
    -- ... other columns
);

CREATE TABLE IF NOT EXISTS saga_steps (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    step_id CHAR(32) NOT NULL UNIQUE,
    saga_id CHAR(36) NOT NULL,
    -- ... other columns
    FOREIGN KEY (saga_id) REFERENCES saga_transactions(saga_id)
);
```

## Performance Issues

### 1. Slow Generation Speed (< 1000 records/second)

#### Diagnosis:
```sql
-- Check current performance
SELECT 
    batch_number,
    processing_rate,
    memory_usage_mb,
    status
FROM batch_progress_tracking 
ORDER BY batch_number DESC 
LIMIT 5;
```

#### Solutions:

**Enable Performance Mode:**
```sql
SET @enable_performance_mode = TRUE;
SET @bulk_insert_buffer_size = '256M';
SET @sort_buffer_size = '16M';
SET @read_buffer_size = '8M';
```

**Optimize Batch Size:**
```sql
-- For SSD storage
SET @batch_size = 100000;

-- For HDD storage
SET @batch_size = 25000;

-- For limited memory
SET @batch_size = 10000;
```

**Disable Non-Essential Features:**
```sql
SET @enable_validation = FALSE;
SET @log_performance_metrics = FALSE;
SET @enable_progress_output = FALSE;
```

### 2. High CPU Usage

#### Diagnosis:
```bash
# Monitor CPU usage
top -p $(pgrep mysqld)

# Check MySQL process list
mysql -e "SHOW PROCESSLIST;"
```

#### Solutions:
```sql
-- Reduce computational overhead
SET @use_deterministic_uuids = FALSE;  -- Use random UUIDs
SET @enable_validation = FALSE;        -- Skip validation
SET @log_batch_progress = FALSE;       -- Reduce logging

-- Optimize MySQL settings
SET SESSION sort_buffer_size = 2097152;  -- 2MB
SET SESSION read_buffer_size = 131072;   -- 128KB
```

### 3. High I/O Wait

#### Diagnosis:
```bash
# Check I/O statistics
iostat -x 1

# Monitor disk usage
iotop
```

#### Solutions:
```sql
-- Reduce I/O operations
SET @batch_size = 50000;              -- Larger batches, fewer commits
SET autocommit = 0;                   -- Manual transaction control
SET sync_binlog = 0;                  -- Reduce binary log sync
SET innodb_flush_log_at_trx_commit = 2; -- Reduce log flushing
```

## Memory and Resource Problems

### 1. Memory Leaks

#### Symptoms:
- Continuously increasing memory usage
- System becomes unresponsive
- MySQL crashes with out-of-memory errors

#### Diagnosis:
```sql
-- Monitor memory usage per batch
SELECT 
    batch_number,
    memory_usage_mb,
    records_processed,
    (memory_usage_mb / records_processed) as mb_per_record
FROM batch_progress_tracking 
ORDER BY batch_number;
```

#### Solutions:
```sql
-- Force garbage collection between batches
SET @batch_size = 10000;  -- Smaller batches
-- Add explicit cleanup in script

-- Use disk-based operations
SET sql_big_tables = 1;
SET tmp_table_size = 67108864;  -- 64MB limit
```

### 2. Connection Pool Exhaustion

#### Symptoms:
```
ERROR 1040 (HY000): Too many connections
```

#### Solutions:
```sql
-- Check current connections
SHOW STATUS LIKE 'Threads_connected';
SHOW VARIABLES LIKE 'max_connections';

-- Increase connection limit if needed
SET GLOBAL max_connections = 500;

-- Or use connection pooling
-- Configure application to use connection pools
```

### 3. Lock Contention

#### Symptoms:
- Long wait times between batches
- "Lock wait timeout exceeded" errors

#### Diagnosis:
```sql
-- Check for locks
SHOW ENGINE INNODB STATUS;
SELECT * FROM information_schema.INNODB_LOCKS;
SELECT * FROM information_schema.INNODB_LOCK_WAITS;
```

#### Solutions:
```sql
-- Reduce lock contention
SET @enable_foreign_key_checks = FALSE;
SET @enable_unique_checks = FALSE;
SET innodb_lock_wait_timeout = 120;

-- Use smaller transactions
SET @batch_size = 5000;
```

## Configuration Issues

### 1. Invalid Configuration Values

#### Error: "Configuration validation failed"

**Common Issues:**
```sql
-- Status percentages don't total 100%
SET @status_running_pct = 50;
SET @status_completed_pct = 25;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 10;
SET @status_failed_pct = 5;
-- Total must equal 100%

-- Step count percentages don't total 100%
SET @steps_3_pct = 20;
SET @steps_4_pct = 40;
SET @steps_5_pct = 40;
-- Total must equal 100%

-- Invalid batch size
SET @batch_size = 0;  -- Must be > 0
SET @batch_size = 2000000;  -- Must be <= @total_sagas
```

#### Validation Script:
```sql
-- Check configuration validity
SELECT 
    (@status_running_pct + @status_completed_pct + @status_pending_pct + 
     @status_compensating_pct + @status_failed_pct) as status_total_should_be_100,
    (@steps_3_pct + @steps_4_pct + @steps_5_pct) as steps_total_should_be_100,
    (@auto_mode_pct + @manual_mode_pct) as mode_total_should_be_100,
    CASE WHEN @batch_size > 0 AND @batch_size <= @total_sagas THEN 'Valid' ELSE 'Invalid' END as batch_size_valid;
```

### 2. MySQL Version Compatibility

#### Error: "You have an error in your SQL syntax"

**Check MySQL Version:**
```sql
SELECT VERSION();
```

**Required Features:**
- MySQL 8.0+ for JSON functions
- MySQL 5.7+ for generated columns
- MySQL 5.6+ for basic functionality

**Solutions:**
```sql
-- For older MySQL versions, disable JSON features
SET @validate_json_data = FALSE;

-- Use alternative UUID generation
SET @use_deterministic_uuids = FALSE;
```

## Data Integrity Problems

### 1. Foreign Key Constraint Violations

#### Error: "Cannot add or update a child row: a foreign key constraint fails"

**Diagnosis:**
```sql
-- Check foreign key constraints
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_NAME IN ('saga_transactions', 'saga_steps');
```

**Solutions:**
```sql
-- Temporarily disable foreign key checks (script should do this automatically)
SET foreign_key_checks = 0;

-- Re-enable after generation
SET foreign_key_checks = 1;

-- Validate data integrity manually
SELECT COUNT(*) FROM saga_steps s 
LEFT JOIN saga_transactions t ON s.saga_id = t.saga_id 
WHERE t.saga_id IS NULL;
```

### 2. Duplicate Key Errors

#### Error: "Duplicate entry for key"

**Solutions:**
```sql
-- Clear existing data first
SET @clear_existing_data = TRUE;

-- Or use different UUID seeds
SET @uuid_seed_base = 54321;  -- Different from default 12345

-- Check for existing duplicates
SELECT saga_id, COUNT(*) 
FROM saga_transactions 
GROUP BY saga_id 
HAVING COUNT(*) > 1;
```

### 3. Invalid JSON Data

#### Error: "Invalid JSON text"

**Diagnosis:**
```sql
-- Check for invalid JSON
SELECT step_id, context_data 
FROM saga_steps 
WHERE JSON_VALID(context_data) = 0 
LIMIT 10;
```

**Solutions:**
```sql
-- Disable JSON validation temporarily
SET @validate_json_data = FALSE;

-- Fix invalid JSON manually
UPDATE saga_steps 
SET context_data = '{}' 
WHERE JSON_VALID(context_data) = 0;
```

## System-Specific Issues

### 1. Windows-Specific Issues

#### Path Separator Issues:
```bash
# Use forward slashes in paths
mysql -u username -p database_name < ./enhanced-initialize-test-data.sql
```

#### Character Encoding Issues:
```bash
# Ensure UTF-8 encoding
mysql --default-character-set=utf8mb4 -u username -p database_name < enhanced-initialize-test-data.sql
```

### 2. Docker/Container Issues

#### Memory Limits:
```bash
# Increase container memory
docker run --memory=8g mysql:8.0

# Or use docker-compose
services:
  mysql:
    image: mysql:8.0
    deploy:
      resources:
        limits:
          memory: 8G
```

#### Volume Permissions:
```bash
# Fix volume permissions
docker exec -it mysql_container chown -R mysql:mysql /var/lib/mysql
```

### 3. Cloud Database Issues

#### Connection Limits:
```sql
-- Check cloud provider limits
SHOW VARIABLES LIKE 'max_connections';
SHOW STATUS LIKE 'Threads_connected';

-- Use smaller batch sizes for cloud databases
SET @batch_size = 5000;
```

#### Timeout Issues:
```sql
-- Increase timeouts for cloud databases
SET SESSION wait_timeout = 3600;
SET SESSION interactive_timeout = 3600;
```

## Recovery Procedures

### 1. Partial Completion Recovery

#### Assess Current State:
```sql
-- Check what was generated
SELECT 
    COUNT(*) as saga_count,
    MIN(created_at) as first_record,
    MAX(created_at) as last_record
FROM saga_transactions;

SELECT 
    COUNT(*) as steps_count,
    COUNT(DISTINCT saga_id) as unique_sagas
FROM saga_steps;
```

#### Clean Partial Data:
```sql
-- Option 1: Clean all and restart
DELETE FROM saga_steps;
DELETE FROM saga_transactions;
ALTER TABLE saga_transactions AUTO_INCREMENT = 1;
ALTER TABLE saga_steps AUTO_INCREMENT = 1;

-- Option 2: Continue from where left off
SET @clear_existing_data = FALSE;
-- Adjust @total_sagas to generate remaining records
```

### 2. Corruption Recovery

#### Check for Corruption:
```sql
-- Check table integrity
CHECK TABLE saga_transactions;
CHECK TABLE saga_steps;

-- Check for orphaned records
SELECT COUNT(*) FROM saga_steps s 
LEFT JOIN saga_transactions t ON s.saga_id = t.saga_id 
WHERE t.saga_id IS NULL;
```

#### Repair Procedures:
```sql
-- Repair tables if needed
REPAIR TABLE saga_transactions;
REPAIR TABLE saga_steps;

-- Remove orphaned records
DELETE s FROM saga_steps s 
LEFT JOIN saga_transactions t ON s.saga_id = t.saga_id 
WHERE t.saga_id IS NULL;
```

### 3. Emergency Cleanup

#### Complete Cleanup Script:
```sql
-- Emergency cleanup procedure
DROP TEMPORARY TABLE IF EXISTS batch_progress_tracking;
DROP TEMPORARY TABLE IF EXISTS overall_progress_summary;
DROP TEMPORARY TABLE IF EXISTS script_execution_log;
DROP TEMPORARY TABLE IF EXISTS business_step_templates;

-- Restore MySQL settings
SET foreign_key_checks = 1;
SET unique_checks = 1;
SET autocommit = 1;
SET sql_big_tables = 0;

-- Reset session variables
SET SESSION wait_timeout = DEFAULT;
SET SESSION interactive_timeout = DEFAULT;
SET SESSION net_read_timeout = DEFAULT;
SET SESSION net_write_timeout = DEFAULT;

-- Clear data if needed
-- DELETE FROM saga_steps;
-- DELETE FROM saga_transactions;
-- ALTER TABLE saga_transactions AUTO_INCREMENT = 1;
-- ALTER TABLE saga_steps AUTO_INCREMENT = 1;
```

## Diagnostic Tools

### 1. Performance Analysis

#### Real-time Monitoring:
```sql
-- Create monitoring view
CREATE OR REPLACE VIEW generation_monitor AS
SELECT 
    'Current Status' as metric,
    CONCAT(
        (SELECT COUNT(*) FROM saga_transactions), ' sagas, ',
        (SELECT COUNT(*) FROM saga_steps), ' steps'
    ) as value
UNION ALL
SELECT 
    'Processing Rate',
    CONCAT(
        COALESCE((
            SELECT ROUND(AVG(processing_rate), 0) 
            FROM batch_progress_tracking 
            WHERE status = 'completed'
        ), 0), ' rec/sec'
    )
UNION ALL
SELECT 
    'Memory Usage',
    CONCAT(
        COALESCE((
            SELECT MAX(memory_usage_mb) 
            FROM batch_progress_tracking
        ), 0), ' MB'
    );

-- Monitor during execution
SELECT * FROM generation_monitor;
```

### 2. Error Analysis

#### Error Log Analysis:
```sql
-- Check script execution log
SELECT 
    phase,
    status,
    error_message,
    records_processed,
    start_time,
    end_time
FROM script_execution_log 
WHERE status = 'failed' 
ORDER BY start_time DESC;
```

### 3. System Resource Monitoring

#### Database Monitoring:
```bash
#!/bin/bash
# monitor-generation.sh

echo "Starting monitoring..."
while true; do
    echo "=== $(date) ==="
    
    # Memory usage
    echo "Memory Usage:"
    free -h | grep -E "Mem:|Swap:"
    
    # Disk usage
    echo "Disk Usage:"
    df -h | grep -E "/$|mysql"
    
    # MySQL status
    echo "MySQL Status:"
    mysql -e "SHOW STATUS LIKE 'Threads_connected';" 2>/dev/null
    mysql -e "SELECT COUNT(*) as saga_count FROM saga_transactions;" 2>/dev/null
    
    echo "---"
    sleep 30
done
```

### 4. Configuration Validator

#### Pre-execution Validation:
```sql
-- Configuration validation script
DELIMITER $$
CREATE PROCEDURE ValidateConfiguration()
BEGIN
    DECLARE config_errors TEXT DEFAULT '';
    
    -- Check status percentages
    IF (@status_running_pct + @status_completed_pct + @status_pending_pct + 
        @status_compensating_pct + @status_failed_pct) != 100 THEN
        SET config_errors = CONCAT(config_errors, 'Status percentages must total 100%; ');
    END IF;
    
    -- Check step percentages
    IF (@steps_3_pct + @steps_4_pct + @steps_5_pct) != 100 THEN
        SET config_errors = CONCAT(config_errors, 'Step percentages must total 100%; ');
    END IF;
    
    -- Check batch size
    IF @batch_size <= 0 OR @batch_size > @total_sagas THEN
        SET config_errors = CONCAT(config_errors, 'Invalid batch size; ');
    END IF;
    
    -- Check total sagas
    IF @total_sagas <= 0 THEN
        SET config_errors = CONCAT(config_errors, 'Total sagas must be > 0; ');
    END IF;
    
    -- Display results
    IF config_errors = '' THEN
        SELECT 'Configuration Valid' as status, 'All parameters are correct' as message;
    ELSE
        SELECT 'Configuration Invalid' as status, config_errors as errors;
    END IF;
END$$
DELIMITER ;

-- Run validation
CALL ValidateConfiguration();
DROP PROCEDURE ValidateConfiguration;
```

---

This troubleshooting guide covers the most common issues encountered when running the Enhanced Data Initialization Script. For issues not covered here, check the MySQL error log and system resources, then consult the main documentation or contact support.