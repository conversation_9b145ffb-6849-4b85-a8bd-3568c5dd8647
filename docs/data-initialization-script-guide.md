# Enhanced Data Initialization Script - Complete User Guide

## Table of Contents
1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Installation and Setup](#installation-and-setup)
4. [Configuration Guide](#configuration-guide)
5. [Usage Examples](#usage-examples)
6. [Performance Tuning](#performance-tuning)
7. [Monitoring and Progress Tracking](#monitoring-and-progress-tracking)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Advanced Configuration](#advanced-configuration)
10. [Best Practices](#best-practices)
11. [FAQ](#faq)

## Overview

The Enhanced Data Initialization Script (v2.0.0) is a comprehensive MySQL-based solution for generating realistic test data for the Saga distributed transaction system. It creates large volumes of test data with proper proportional distributions, realistic business scenarios, and optimized performance for large-scale data generation.

### Key Features
- **Configurable Data Volumes**: Generate from thousands to millions of saga transactions
- **Realistic Business Scenarios**: Five distinct business types (ecommerce, payment, inventory, user registration, data synchronization)
- **Proper Status Distribution**: Configurable status percentages (50% running, 25% completed, etc.)
- **Batch Processing**: Memory-efficient processing with configurable batch sizes
- **Performance Optimization**: Automatic MySQL settings optimization during generation
- **Progress Monitoring**: Real-time progress tracking with performance metrics
- **Data Validation**: Comprehensive validation of generated data integrity
- **Deterministic Generation**: Reproducible test data using seed-based UUID generation

### Business Scenarios Supported

| Business Type | Description | Steps | Use Case |
|---------------|-------------|-------|----------|
| **E-commerce** | Order processing workflow | CreateOrder → ProcessPayment → ReserveInventory → SendNotification → UpdateUserPoints | Online shopping transactions |
| **Payment** | Payment processing workflow | ValidateAccount → ProcessPayment → UpdateBalance → LogTransaction → SendReceipt | Financial transactions |
| **Inventory** | Stock management workflow | CheckInventory → ReserveStock → UpdateWarehouse → UpdateCatalog → NotifySupplier | Inventory management |
| **User Registration** | User onboarding workflow | CreateUser → SendWelcomeEmail → InitializeProfile → GrantPermissions → CreateWallet | User account creation |
| **Data Sync** | ETL workflow | ExtractData → TransformData → LoadData → ValidateData → NotifyCompletion | Data synchronization |

## Prerequisites

### System Requirements
- **MySQL Version**: 8.0+ (required for JSON support)
- **Memory**: Minimum 4GB RAM for large datasets (8GB+ recommended for 1M+ records)
- **Storage**: SSD storage recommended for optimal performance
- **CPU**: Multi-core CPU recommended for large datasets

### Database Requirements
- Existing `saga_transactions` and `saga_steps` tables
- Appropriate MySQL user permissions:
  - `SELECT`, `INSERT`, `UPDATE`, `DELETE` on target tables
  - `CREATE TEMPORARY TABLES` privilege
  - `ALTER` privilege for optimization settings
  - `PROCESS` privilege for performance monitoring

### MySQL Configuration Recommendations
```ini
# Add to my.cnf for optimal performance
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 512M
innodb_flush_log_at_trx_commit = 2
bulk_insert_buffer_size = 256M
sort_buffer_size = 16M
read_buffer_size = 8M
tmp_table_size = 512M
max_heap_table_size = 512M
```

## Installation and Setup

### 1. Download the Script
```bash
# Download the enhanced script
curl -O https://your-repo/enhanced-initialize-test-data.sql

# Or copy from your project directory
cp enhanced-initialize-test-data.sql /path/to/your/scripts/
```

### 2. Verify Database Schema
Ensure your database has the required tables:
```sql
-- Check if required tables exist
SHOW TABLES LIKE 'saga_%';

-- Verify table structure
DESCRIBE saga_transactions;
DESCRIBE saga_steps;
```

### 3. Test Database Connection
```bash
# Test connection with your credentials
mysql -u your_username -p -h your_host your_database -e "SELECT 1;"
```

## Configuration Guide

### Basic Configuration

The script uses configuration variables at the top of the file. Here are the most important settings:

```sql
-- Core Generation Parameters
SET @total_sagas = 1000000;        -- Total saga transactions to generate
SET @batch_size = 50000;           -- Records per batch
SET @enable_progress_output = TRUE; -- Show progress during generation
SET @enable_validation = TRUE;      -- Validate data after generation
```

### Configuration Scenarios

#### Small Development Environment (1K records)
```sql
SET @total_sagas = 1000;
SET @batch_size = 500;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = FALSE;
```

#### Medium Testing Environment (100K records)
```sql
SET @total_sagas = 100000;
SET @batch_size = 10000;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = TRUE;
```

#### Large Production Testing (1M+ records)
```sql
SET @total_sagas = 1000000;
SET @batch_size = 50000;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = TRUE;
SET @bulk_insert_buffer_size = '512M';
SET @tmp_table_size = '1G';
SET @max_heap_table_size = '1G';
```

#### Performance Benchmarking (10M records)
```sql
SET @total_sagas = 10000000;
SET @batch_size = 100000;
SET @enable_progress_output = FALSE;  -- Reduce I/O overhead
SET @enable_validation = FALSE;       -- Skip validation for speed
SET @enable_performance_mode = TRUE;
SET @log_batch_progress = FALSE;      -- Minimal logging
```

### Status Distribution Configuration

Customize the distribution of saga statuses:
```sql
-- Default distribution (recommended for realistic testing)
SET @status_running_pct = 50;      -- 50% active transactions
SET @status_completed_pct = 25;    -- 25% successful completions
SET @status_pending_pct = 10;      -- 10% waiting to start
SET @status_compensating_pct = 10; -- 10% in rollback process
SET @status_failed_pct = 5;        -- 5% terminal failures

-- High-load testing scenario
SET @status_running_pct = 70;      -- More active transactions
SET @status_completed_pct = 15;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 3;
SET @status_failed_pct = 2;

-- Error scenario testing
SET @status_running_pct = 30;
SET @status_completed_pct = 20;
SET @status_pending_pct = 15;
SET @status_compensating_pct = 25;  -- More compensation scenarios
SET @status_failed_pct = 10;       -- More failures
```

## Usage Examples

### Basic Usage

#### 1. Standard Execution
```bash
# Execute with default settings (1M records)
mysql -u username -p database_name < enhanced-initialize-test-data.sql
```

#### 2. Custom Configuration Execution
```bash
# Create a custom configuration file
cat > custom-config.sql << 'EOF'
SET @total_sagas = 500000;
SET @batch_size = 25000;
SET @enable_progress_output = TRUE;
source enhanced-initialize-test-data.sql;
EOF

# Execute with custom configuration
mysql -u username -p database_name < custom-config.sql
```

#### 3. Execution with Logging
```bash
# Execute with detailed logging
mysql -u username -p database_name < enhanced-initialize-test-data.sql > generation.log 2>&1

# Monitor progress in real-time
tail -f generation.log
```

### Advanced Usage Examples

#### 1. Parallel Execution for Multiple Databases
```bash
#!/bin/bash
# parallel-generation.sh

databases=("test_db1" "test_db2" "test_db3")
username="your_user"

for db in "${databases[@]}"; do
    echo "Starting generation for $db..."
    mysql -u $username -p$password $db < enhanced-initialize-test-data.sql > "${db}_generation.log" 2>&1 &
done

wait
echo "All databases completed"
```

#### 2. Incremental Data Generation
```bash
#!/bin/bash
# incremental-generation.sh

# Generate data in smaller increments
for i in {1..10}; do
    echo "Generating batch $i of 10..."
    
    cat > temp-config.sql << EOF
SET @total_sagas = 100000;
SET @batch_size = 10000;
SET @clear_existing_data = FALSE;  -- Don't clear existing data
source enhanced-initialize-test-data.sql;
EOF
    
    mysql -u username -p database_name < temp-config.sql
    
    echo "Batch $i completed. Sleeping for 30 seconds..."
    sleep 30
done

rm temp-config.sql
```

#### 3. Performance Testing Setup
```bash
#!/bin/bash
# performance-test-setup.sh

# Setup for performance testing
echo "Setting up performance test data..."

# Configure MySQL for performance
mysql -u root -p << 'EOF'
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL sync_binlog = 0;
SET GLOBAL innodb_buffer_pool_size = 2147483648;  -- 2GB
EOF

# Generate large dataset
cat > perf-config.sql << 'EOF'
SET @total_sagas = 5000000;
SET @batch_size = 100000;
SET @enable_progress_output = FALSE;
SET @enable_validation = FALSE;
SET @log_batch_progress = FALSE;
source enhanced-initialize-test-data.sql;
EOF

time mysql -u username -p database_name < perf-config.sql

echo "Performance test data generation completed"
```

## Performance Tuning

### MySQL Optimization Settings

The script automatically optimizes MySQL settings during execution, but you can pre-configure your database for better performance:

```sql
-- Pre-execution optimization
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL sync_binlog = 0;
SET GLOBAL foreign_key_checks = 0;
SET GLOBAL unique_checks = 0;
SET GLOBAL bulk_insert_buffer_size = 268435456;  -- 256MB
SET GLOBAL sort_buffer_size = 16777216;          -- 16MB
SET GLOBAL read_buffer_size = 8388608;           -- 8MB
```

### Batch Size Optimization

Choose batch size based on available memory:

| Available RAM | Recommended Batch Size | Expected Memory Usage |
|---------------|------------------------|----------------------|
| 4GB | 25,000 | ~200MB |
| 8GB | 50,000 | ~400MB |
| 16GB | 100,000 | ~800MB |
| 32GB+ | 200,000 | ~1.6GB |

### Storage Optimization

```sql
-- For SSD storage
SET @batch_size = 100000;  -- Larger batches for faster I/O

-- For traditional HDD
SET @batch_size = 25000;   -- Smaller batches to reduce seek time
```

## Monitoring and Progress Tracking

### Real-time Progress Monitoring

The script provides comprehensive progress tracking:

```sql
-- Enable detailed progress reporting
SET @enable_progress_output = TRUE;
SET @log_batch_progress = TRUE;
SET @log_performance_metrics = TRUE;
```

### Progress Output Example
```
Batch Progress Report - SAGA
batch_number: 15
batch_progress: 15/20
records_progress: 750,000/1,000,000
batch_completion: 100.0%
overall_completion: 75.0%
current_rate: 12,500 rec/sec
average_rate: 11,800 rec/sec
estimated_memory: 45 MB
estimated_completion: ETA: 14:32:15
batch_status: completed
timestamp: 14:30:22
```

### Performance Metrics

Monitor key performance indicators:
- **Processing Rate**: Records per second
- **Memory Usage**: Current memory consumption
- **Batch Efficiency**: Performance rating (excellent/good/fair/poor)
- **ETA**: Estimated completion time

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Out of Memory Errors

**Symptoms:**
```
ERROR 1041 (HY000): Out of memory
ERROR 1114 (HY000): The table 'temp_table' is full
```

**Solutions:**
```sql
-- Reduce batch size
SET @batch_size = 10000;  -- Reduce from default 50000

-- Increase MySQL memory limits
SET @tmp_table_size = '1G';
SET @max_heap_table_size = '1G';

-- Or disable memory tables temporarily
SET sql_big_tables = 1;
```

#### 2. Slow Performance

**Symptoms:**
- Processing rate < 1000 records/second
- Long execution times

**Solutions:**
```sql
-- Enable performance mode
SET @enable_performance_mode = TRUE;

-- Optimize batch size for your system
SET @batch_size = 25000;  -- Try different sizes

-- Disable unnecessary features
SET @enable_validation = FALSE;
SET @log_performance_metrics = FALSE;
```

#### 3. Connection Timeouts

**Symptoms:**
```
ERROR 2006 (HY000): MySQL server has gone away
ERROR 2013 (HY000): Lost connection to MySQL server
```

**Solutions:**
```sql
-- Increase timeout settings
SET SESSION wait_timeout = 28800;
SET SESSION interactive_timeout = 28800;

-- Or reduce batch size to process faster
SET @batch_size = 10000;
```

#### 4. Disk Space Issues

**Symptoms:**
```
ERROR 1114 (HY000): The table is full
ERROR 28 (HY000): No space left on device
```

**Solutions:**
```bash
# Check available disk space
df -h

# Clean up temporary files
mysql -e "SHOW GLOBAL STATUS LIKE 'Created_tmp%';"

# Reduce data volume
SET @total_sagas = 100000;  -- Reduce from 1M
```

#### 5. Foreign Key Constraint Errors

**Symptoms:**
```
ERROR 1452 (23000): Cannot add or update a child row: a foreign key constraint fails
```

**Solutions:**
```sql
-- The script should handle this automatically, but if not:
SET foreign_key_checks = 0;

-- Re-run the problematic section
-- Then restore:
SET foreign_key_checks = 1;
```

### Debugging Steps

#### 1. Enable Detailed Logging
```sql
SET @log_error_details = TRUE;
SET @enable_progress_output = TRUE;
SET @log_performance_metrics = TRUE;
```

#### 2. Check System Resources
```bash
# Monitor memory usage
free -h

# Monitor disk usage
df -h

# Monitor MySQL processes
mysql -e "SHOW PROCESSLIST;"

# Check MySQL error log
tail -f /var/log/mysql/error.log
```

#### 3. Validate Configuration
```sql
-- Check configuration validation
SELECT 
    @total_sagas as total_sagas,
    @batch_size as batch_size,
    @total_batches as calculated_batches,
    (@status_running_pct + @status_completed_pct + @status_pending_pct + 
     @status_compensating_pct + @status_failed_pct) as status_total_should_be_100;
```

#### 4. Test with Smaller Dataset
```sql
-- Test with minimal data first
SET @total_sagas = 1000;
SET @batch_size = 100;
SET @enable_validation = TRUE;
```

### Error Recovery

#### 1. Partial Completion Recovery
If the script fails partway through:

```sql
-- Check what was generated
SELECT COUNT(*) FROM saga_transactions;
SELECT COUNT(*) FROM saga_steps;

-- Clear partial data and restart
DELETE FROM saga_steps;
DELETE FROM saga_transactions;
ALTER TABLE saga_transactions AUTO_INCREMENT = 1;
ALTER TABLE saga_steps AUTO_INCREMENT = 1;
```

#### 2. Cleanup After Failure
```sql
-- Clean up temporary tables
DROP TEMPORARY TABLE IF EXISTS batch_progress_tracking;
DROP TEMPORARY TABLE IF EXISTS overall_progress_summary;
DROP TEMPORARY TABLE IF EXISTS script_execution_log;

-- Restore MySQL settings
SET foreign_key_checks = 1;
SET unique_checks = 1;
SET autocommit = 1;
```

## Advanced Configuration

### Custom Business Scenarios

You can modify the business step templates to create custom scenarios:

```sql
-- Add custom business scenario
INSERT INTO business_step_templates (
    step_order, action, service_name, business_type, 
    compensate_endpoint, description
) VALUES 
(1, 'InitiateCustomFlow', 'custom-service', 'custom', 
 'http://custom-service:8080/saga/compensate/InitiateCustomFlow', 
 'Custom business flow step 1'),
(2, 'ProcessCustomData', 'custom-service', 'custom',
 'http://custom-service:8080/saga/compensate/ProcessCustomData',
 'Custom business flow step 2');

-- Update business types count
SET @business_types = 6;  -- Include custom type
```

### Environment-Specific Configurations

#### Development Environment
```sql
-- development-config.sql
SET @total_sagas = 1000;
SET @batch_size = 100;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @log_batch_progress = TRUE;
SET @log_performance_metrics = TRUE;
SET @log_error_details = TRUE;
```

#### Staging Environment
```sql
-- staging-config.sql
SET @total_sagas = 100000;
SET @batch_size = 10000;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = TRUE;
```

#### Production Testing Environment
```sql
-- production-config.sql
SET @total_sagas = 10000000;
SET @batch_size = 100000;
SET @enable_progress_output = FALSE;
SET @enable_validation = FALSE;
SET @enable_performance_mode = TRUE;
SET @log_batch_progress = FALSE;
```

## Best Practices

### 1. Pre-Execution Checklist
- [ ] Verify database schema compatibility
- [ ] Check available disk space (at least 2x expected data size)
- [ ] Confirm sufficient RAM (minimum 4GB for large datasets)
- [ ] Backup existing data if needed
- [ ] Test with small dataset first
- [ ] Review and customize configuration parameters

### 2. During Execution
- [ ] Monitor system resources (CPU, memory, disk)
- [ ] Watch for error messages in output
- [ ] Track progress and performance metrics
- [ ] Be prepared to stop execution if issues arise

### 3. Post-Execution
- [ ] Verify data integrity with built-in validation
- [ ] Check final statistics match expectations
- [ ] Review performance metrics for optimization opportunities
- [ ] Document any configuration changes for future use

### 4. Performance Optimization
- [ ] Use SSD storage for better I/O performance
- [ ] Optimize MySQL configuration for bulk operations
- [ ] Choose appropriate batch size for your system
- [ ] Consider running during off-peak hours
- [ ] Monitor and tune based on performance metrics

### 5. Data Quality
- [ ] Validate status distributions match requirements
- [ ] Verify business scenario completeness
- [ ] Check referential integrity between tables
- [ ] Confirm realistic timestamp distributions
- [ ] Test with generated data before using in applications

## FAQ

### General Questions

**Q: How long does it take to generate 1 million records?**
A: Typically 5-15 minutes depending on hardware. SSD storage and sufficient RAM (8GB+) provide optimal performance.

**Q: Can I run the script multiple times to add more data?**
A: Yes, set `@clear_existing_data = FALSE` to append to existing data instead of replacing it.

**Q: Is the generated data deterministic?**
A: Yes, when using the same configuration and seed values, the script generates identical data for reproducible testing.

**Q: Can I customize the business scenarios?**
A: Yes, modify the business_step_templates table or add custom scenarios by inserting new templates.

### Technical Questions

**Q: What MySQL versions are supported?**
A: MySQL 8.0+ is required for JSON support. Earlier versions are not supported.

**Q: How much memory does the script use?**
A: Memory usage depends on batch size. Approximately 1KB per record, so 50K batch ≈ 50MB memory usage.

**Q: Can I run this on a read replica?**
A: No, the script requires write permissions and creates temporary tables. Use a primary database instance.

**Q: Does the script affect production data?**
A: The script only affects the saga_transactions and saga_steps tables. It includes safety checks but always backup first.

### Troubleshooting Questions

**Q: What if the script fails halfway through?**
A: The script uses batch processing, so partial data can be cleaned up and the script restarted. See the Error Recovery section.

**Q: Why is generation slow on my system?**
A: Common causes: insufficient RAM, HDD instead of SSD, large batch size, or MySQL configuration. See Performance Tuning section.

**Q: Can I stop the script safely during execution?**
A: Yes, use Ctrl+C. The script commits data in batches, so completed batches are preserved.

**Q: How do I verify the generated data is correct?**
A: Enable validation (`@enable_validation = TRUE`) and review the statistics output. The script includes comprehensive validation checks.

---

For additional support or questions not covered in this guide, please refer to the project documentation or contact the development team.