# 回滚配置管理

## 概述

从配置中读取回滚相关参数，避免硬编码，提高系统的可配置性和灵活性。

## 配置项说明

### 回滚配置节点

在 `saga.compensation.rollback` 配置节点下配置回滚相关参数：

```yaml
saga:
  compensation:
    rollback:
      maxRetries: 3           # 默认最大重试次数
      timeout: "30s"          # 默认补偿操作超时时间
```

### 配置参数详解

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `maxRetries` | int | 3 | 每个补偿步骤的默认最大重试次数 |
| `timeout` | string | "30s" | 补偿操作的默认超时时间 |

## 配置示例

### 生产环境配置

```yaml
# config.yaml
saga:
  compensation:
    rollback:
      maxRetries: 5           # 生产环境增加重试次数
      timeout: "60s"          # 生产环境增加超时时间
```

### 开发环境配置

```yaml
# config.dev.yaml
saga:
  compensation:
    rollback:
      maxRetries: 2           # 开发环境减少重试次数
      timeout: "15s"          # 开发环境减少超时时间
```

### 测试环境配置

```yaml
# config.test.yaml
saga:
  compensation:
    rollback:
      maxRetries: 1           # 测试环境快速失败
      timeout: "10s"          # 测试环境快速超时
```

## 环境变量覆盖

支持通过环境变量覆盖配置文件中的设置：

```bash
# 设置最大重试次数
export SAGA_COMPENSATION_ROLLBACK_MAXRETRIES=5

# 设置超时时间
export SAGA_COMPENSATION_ROLLBACK_TIMEOUT=60s
```

## 配置生效逻辑

### 1. 参数优先级

1. **API 请求参数**：如果调用 `RollbackSagaTransaction` 时传递了 `maxRetries` 或 `compensationTimeout`，则使用传递的值
2. **配置文件**：如果API请求参数为空，则从配置文件读取默认值
3. **环境变量**：环境变量可以覆盖配置文件中的设置
4. **硬编码默认值**：作为最后的兜底值

### 2. 配置读取时机

- **服务启动时**：不预加载配置，每次调用时实时读取
- **动态生效**：修改配置文件后无需重启服务即可生效
- **环境变量**：环境变量修改后需要重启服务生效

## 使用示例

### 1. 使用默认配置

```json
{
  "sagaId": "saga-001",
  "failReason": "支付失败",
  "executionMode": "sync"
}
```

此时会使用配置文件中的默认值：
- `maxRetries`: 配置文件中的 `saga.compensation.rollback.maxRetries`
- `compensationTimeout`: 配置文件中的 `saga.compensation.rollback.timeout` (转换为毫秒)

### 2. 显式指定参数

```json
{
  "sagaId": "saga-001",
  "failReason": "支付失败",
  "maxRetries": 5,
  "compensationTimeout": 45000,
  "executionMode": "sync"
}
```

此时会使用显式指定的参数，忽略配置文件的默认值。

### 3. 部分显式指定

```json
{
  "sagaId": "saga-001",
  "failReason": "支付失败",
  "maxRetries": 5,
  "executionMode": "sync"
}
```

此时：
- `maxRetries`: 使用显式指定的 5
- `compensationTimeout`: 使用配置文件的默认值

## 配置验证

### 1. 超时时间格式

支持的时间单位：
- `ns` (纳秒)
- `us` (微秒)
- `ms` (毫秒)
- `s` (秒)
- `m` (分钟)
- `h` (小时)

示例：
```yaml
timeout: "30s"    # 30秒
timeout: "5m"     # 5分钟
timeout: "1h30m"  # 1小时30分钟
```

### 2. 重试次数限制

- **最小值**: 0 (不重试)
- **最大值**: 建议不超过 10，避免过度重试
- **推荐值**: 3-5 次

## 监控和日志

### 1. 配置读取日志

```log
2024-01-15T10:30:00Z [INFO] 使用配置项: saga.compensation.rollback.maxRetries=3
2024-01-15T10:30:00Z [INFO] 使用配置项: saga.compensation.rollback.timeout=30s
```

### 2. 参数应用日志

```log
2024-01-15T10:30:00Z [INFO] 回滚参数: SagaId=saga-001, MaxRetries=3, Timeout=30000ms, Mode=sync
```

## 注意事项

### 1. 配置文件格式

- 确保使用正确的 YAML 格式
- 注意缩进和层级关系
- 时间格式必须为字符串（带引号）

### 2. 性能影响

- 每次调用都会读取配置，对性能影响很小
- 建议在高并发场景下进行性能测试
- 可以考虑增加配置缓存机制

### 3. 向后兼容

- 如果配置文件中没有相关配置，会使用代码中的默认值
- 不会影响现有API的使用方式
- 逐步迁移现有硬编码参数到配置文件

## 部署建议

### 1. 环境隔离

```bash
# 生产环境
export SAGA_COMPENSATION_ROLLBACK_MAXRETRIES=5
export SAGA_COMPENSATION_ROLLBACK_TIMEOUT=60s

# 开发环境
export SAGA_COMPENSATION_ROLLBACK_MAXRETRIES=2
export SAGA_COMPENSATION_ROLLBACK_TIMEOUT=15s
```

### 2. 配置管理

- 使用配置中心统一管理配置
- 定期审查和优化配置参数
- 建立配置变更审批流程

### 3. 监控告警

- 监控补偿操作的重试次数和超时情况
- 设置告警阈值，及时发现异常
- 定期分析补偿失败的原因并优化配置

## 相关文档

- [Saga 事务 API 文档](saga-transactions-api.md)
- [补偿恢复服务文档](compensation-recovery-service.md)
- [高可用部署方案](high-availability-deployment.md) 