# Saga 重试与补偿窗口配置指南

## 概述

本文档提供了关于配置 Saga 分布式事务系统中重试机制和补偿窗口期的详细指南。正确的配置可以提高系统的可靠性和弹性，特别是在网络不稳定或高延迟的环境中。

## 重试配置

### 补偿重试配置

补偿重试配置控制执行补偿操作时的重试行为：

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `maxRetries` | 3 | 最大重试次数 |
| `initialRetryInterval` | 1秒 | 初始重试间隔 |
| `maxRetryInterval` | 30秒 | 最大重试间隔 |
| `retryMultiplier` | 2.0 | 指数退避倍数 |
| `compensationTimeout` | 30秒 | 补偿操作超时时间 |

### ReportCompensation 重试配置

ReportCompensation 重试配置控制上报补偿信息时的重试行为：

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `reportMaxRetries` | 3 | 上报最大重试次数 |
| `reportRetryDelay` | 50毫秒 | 上报重试基础延迟 |

## 补偿窗口期配置

### 什么是补偿窗口期？

补偿窗口期是指在 Saga 事务完成（`completed`）或失败（`failed`）后的一段时间内，系统仍然接受补偿信息上报的时间窗口。这个机制解决了异步系统中的时序竞争问题，允许延迟到达的补偿信息仍然能被正确处理。

### 补偿窗口期参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `compensationWindowSec` | 30秒 | 补偿窗口期（秒） |

## 配置方法

### 1. 创建 Saga 事务时配置

在创建 Saga 事务时，可以通过 `CreateSagaTransactionInput` 参数指定补偿窗口期：

```go
input := &model.CreateSagaTransactionInput{
    Name:                "订单支付流程",
    StepIndexMode:       "auto",
    CompensationWindowSec: 60, // 设置60秒的补偿窗口期
}

output, err := sagaService.CreateSagaTransaction(ctx, input)
```

### 2. 服务实例化时配置

在创建 SagaTransactionsService 实例时，可以配置默认的补偿窗口期和重试参数：

```go
// 使用默认配置
sagaService := service.NewSagaTransactions()

// 自定义重试配置
sagaService := service.NewSagaTransactionsWithRetryConfig(
    5,                  // maxRetries
    2 * time.Second,    // initialInterval
    60 * time.Second,   // maxInterval
    2.5,                // multiplier
)

// 完整配置（包括补偿窗口期）
sagaService := service.NewSagaTransactionsWithFullConfig(
    // 补偿重试配置
    5,                  // maxRetries
    2 * time.Second,    // initialInterval
    60 * time.Second,   // maxInterval
    2.5,                // multiplier
    45 * time.Second,   // timeout
    // ReportCompensation 重试配置
    3,                  // reportMaxRetries
    100 * time.Millisecond, // reportRetryDelay
    // 补偿窗口期配置
    60,                 // compensationWindowSec (60秒)
)
```

## 环境适配建议

不同的环境和业务场景需要不同的配置参数。以下是一些常见场景的建议：

### 1. 本地开发环境

```go
// 本地开发环境 - 快速失败，便于调试
sagaService := service.NewSagaTransactionsWithFullConfig(
    1,                  // maxRetries
    500 * time.Millisecond, // initialInterval
    1 * time.Second,    // maxInterval
    1.5,                // multiplier
    5 * time.Second,    // timeout
    1,                  // reportMaxRetries
    20 * time.Millisecond, // reportRetryDelay
    10,                 // compensationWindowSec (10秒)
)
```

### 2. 测试环境

```go
// 测试环境 - 适度重试，较短窗口期
sagaService := service.NewSagaTransactionsWithFullConfig(
    3,                  // maxRetries
    1 * time.Second,    // initialInterval
    10 * time.Second,   // maxInterval
    2.0,                // multiplier
    15 * time.Second,   // timeout
    2,                  // reportMaxRetries
    50 * time.Millisecond, // reportRetryDelay
    20,                 // compensationWindowSec (20秒)
)
```

### 3. 生产环境 - 同数据中心

```go
// 生产环境（同数据中心）- 适度重试，标准窗口期
sagaService := service.NewSagaTransactionsWithFullConfig(
    3,                  // maxRetries
    2 * time.Second,    // initialInterval
    30 * time.Second,   // maxInterval
    2.0,                // multiplier
    30 * time.Second,   // timeout
    3,                  // reportMaxRetries
    50 * time.Millisecond, // reportRetryDelay
    30,                 // compensationWindowSec (30秒)
)
```

### 4. 生产环境 - 跨数据中心

```go
// 生产环境（跨数据中心）- 更多重试，更长窗口期
sagaService := service.NewSagaTransactionsWithFullConfig(
    5,                  // maxRetries
    3 * time.Second,    // initialInterval
    60 * time.Second,   // maxInterval
    2.0,                // multiplier
    60 * time.Second,   // timeout
    4,                  // reportMaxRetries
    100 * time.Millisecond, // reportRetryDelay
    90,                 // compensationWindowSec (90秒)
)
```

### 5. 高可靠性场景

```go
// 高可靠性场景 - 大量重试，长窗口期
sagaService := service.NewSagaTransactionsWithFullConfig(
    8,                  // maxRetries
    5 * time.Second,    // initialInterval
    120 * time.Second,  // maxInterval
    1.8,                // multiplier
    90 * time.Second,   // timeout
    5,                  // reportMaxRetries
    200 * time.Millisecond, // reportRetryDelay
    180,                // compensationWindowSec (3分钟)
)
```

## 监控建议

为了确保重试机制和补偿窗口期正常工作，建议监控以下指标：

1. **补偿重试次数**：监控补偿操作的平均重试次数和分布
2. **补偿成功率**：监控补偿操作的成功率
3. **窗口期内上报计数**：监控在补偿窗口期内接收的上报数量
4. **窗口期外上报计数**：监控被拒绝的窗口期外上报数量

如果窗口期外上报数量较多，可能需要考虑延长补偿窗口期或优化系统架构。

## 故障排查

当遇到补偿失败或上报被拒绝的情况时，可以检查以下日志：

1. 补偿执行失败：
   ```
   补偿调用失败: {endpoint}, 第 {attempt} 次尝试, 错误: {error}
   ```

2. 窗口期外的上报被拒绝：
   ```
   检测到延迟的补偿上报: SagaId={sagaId}, 当前状态={status}, 服务={service}, 动作={action}
   ```

3. 窗口期内的补偿上报：
   ```
   检测到补偿窗口期内的补偿上报: SagaId={sagaId}, 当前状态={status}, 服务={service}, 动作={action}
   ```

## 最佳实践

1. **根据网络特性调整**：
   - 高延迟网络环境应使用更长的补偿窗口期和更多的重试次数
   - 低延迟环境可以使用较短的补偿窗口期和较少的重试次数

2. **考虑业务特性**：
   - 关键业务流程应使用更长的补偿窗口期和更多的重试次数
   - 非关键流程可以使用较短的补偿窗口期和较少的重试次数

3. **定期审查配置**：
   - 根据监控数据定期审查和调整配置参数
   - 特别关注窗口期外上报的比例，如果比例较高，考虑延长补偿窗口期

4. **考虑资源消耗**：
   - 过长的补偿窗口期和过多的重试次数会增加系统资源消耗
   - 在高并发场景下，需要平衡可靠性和资源消耗 