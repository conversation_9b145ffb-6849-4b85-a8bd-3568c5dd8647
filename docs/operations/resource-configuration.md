# Saga 分布式事务系统 - 资源配置指南

## 🎯 配置概述

基于性能测试计划，我们为 Saga 系统提供了两套资源配置方案，以适应不同的使用场景。

### 硬件环境
- **测试环境**: MacBook Pro M3 Max (14核CPU, 36GB内存)
- **部署方式**: 单机容器化部署
- **资源分配**: 数据库 + 应用 + 压测客户端共享资源

## 📊 配置方案对比

| 配置类型 | 数据库资源 | 应用资源 | 适用场景 | 预期性能 |
|---------|-----------|---------|----------|----------|
| **性能测试配置** | 4核16GB | 8核16GB | 性能压测、生产预演 | TPS≥500, P95≤800ms |
| **轻量级配置** | 2核4GB | 4核8GB | 开发调试、功能测试 | TPS≥200, P95≤1500ms |

## 🚀 启动命令

### 性能测试配置
```bash
# 方式1: 分别启动（推荐用于开发）
make start                    # 数据库4核16GB + 应用8核16GB

# 方式2: Docker 容器
make start-docker            # 独立容器启动

# 方式3: Docker Compose（推荐用于测试）
make start-compose           # 完整环境一键启动
```

### 轻量级配置
```bash
# 方式1: 分别启动
make start-light             # 数据库2核4GB + 应用4核8GB

# 方式2: Docker 容器
make start-docker-light      # 独立容器启动

# 方式3: Docker Compose
make start-compose-light     # 完整环境一键启动
```

## 🗄️ 数据库配置详情

### 性能测试配置 (MySQL)
```yaml
资源限制:
  CPU: 4核
  内存: 16GB
  
关键参数:
  innodb_buffer_pool_size: 12G
  innodb_log_file_size: 2G
  max_connections: 500
  innodb_flush_log_at_trx_commit: 2
  innodb_io_capacity: 2000
  
配置文件: manifest/config/mysql/performance.cnf
```

### 轻量级配置 (MySQL)
```yaml
资源限制:
  CPU: 2核
  内存: 4GB
  
关键参数:
  innodb_buffer_pool_size: 2G
  innodb_log_file_size: 512M
  max_connections: 200
  innodb_flush_log_at_trx_commit: 1
  innodb_io_capacity: 200
  
配置文件: manifest/config/mysql/light.cnf
```

## 🔧 应用配置详情

### 性能测试配置 (Go应用)
```yaml
资源限制:
  CPU: 8核
  内存: 16GB
  
Go运行时参数:
  GOMAXPROCS: 8
  GOGC: 50
  GOMEMLIMIT: 16GiB
  
优化特性:
  - 更激进的GC策略
  - 最大化CPU利用率
  - 大内存缓冲区
```

### 轻量级配置 (Go应用)
```yaml
资源限制:
  CPU: 4核
  内存: 8GB
  
Go运行时参数:
  GOMAXPROCS: 4
  GOGC: 100
  GOMEMLIMIT: 8GiB
  
优化特性:
  - 平衡的GC策略
  - 适中的CPU利用率
  - 合理的内存使用
```

## 📈 性能预期

### 性能测试配置预期指标
```yaml
Level 1 (10万数据):
  TPS: ≥500
  P95响应时间: ≤800ms
  错误率: ≤0.5%
  并发用户: 50-200

Level 2 (100万数据):
  TPS: ≥800
  P95响应时间: ≤1500ms
  错误率: ≤1%
  并发用户: 200-800

Level 3 (1000万数据):
  TPS: ≥1000
  P95响应时间: ≤3000ms
  错误率: ≤2%
  并发用户: 300-1000
```

### 轻量级配置预期指标
```yaml
基础功能测试:
  TPS: ≥200
  P95响应时间: ≤1500ms
  错误率: ≤1%
  并发用户: 20-100

开发调试:
  响应时间: 快速响应
  资源占用: 低
  稳定性: 高
```

## 🔍 监控和调优

### 资源监控命令
```bash
# 查看容器资源使用情况
make monitor

# 实时监控
make monitor-live

# 查看服务状态
make status
```

### 性能调优建议

#### 数据库调优
1. **连接池优化**
   ```sql
   -- 监控连接数
   SHOW STATUS LIKE 'Threads_connected';
   SHOW STATUS LIKE 'Max_used_connections';
   ```

2. **缓冲池优化**
   ```sql
   -- 监控缓冲池命中率
   SHOW STATUS LIKE 'Innodb_buffer_pool_read%';
   ```

3. **慢查询监控**
   ```bash
   # 查看慢查询日志
   docker exec mysql tail -f /var/lib/mysql/slow.log
   ```

#### 应用调优
1. **GC监控**
   ```bash
   # 启用GC日志
   GODEBUG=gctrace=1 make dev-perf
   ```

2. **内存分析**
   ```bash
   # 生成内存profile
   go tool pprof http://localhost:8080/debug/pprof/heap
   ```

3. **CPU分析**
   ```bash
   # 生成CPU profile
   go tool pprof http://localhost:8080/debug/pprof/profile
   ```

## 🛠 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 检查系统内存
   vm_stat
   
   # 降级到轻量配置
   make stop && make start-light
   ```

2. **CPU使用率过高**
   ```bash
   # 检查CPU使用情况
   top -pid $(pgrep saga)
   
   # 调整GOMAXPROCS
   GOMAXPROCS=4 make dev
   ```

3. **数据库连接超时**
   ```bash
   # 检查数据库状态
   make db-logs
   
   # 重启数据库
   make db-reset
   ```

## 🎯 最佳实践

### 开发环境
- 使用轻量级配置进行日常开发
- 定期使用性能测试配置验证性能
- 监控资源使用情况，避免资源竞争

### 测试环境
- 性能测试使用性能测试配置
- 功能测试可使用轻量级配置
- 使用Docker Compose确保环境一致性

### 生产环境
- 基于性能测试结果调整资源配置
- 实施资源监控和告警
- 定期进行性能回归测试

通过合理的资源配置和监控，可以确保 Saga 分布式事务系统在不同场景下都能提供稳定可靠的服务。
