# Step Index 不可变性设计文档

## 概述

在 Saga 分布式事务系统中，`step_index` 字段具有**不可变性**特征，即一旦创建就不能更改。这是一个重要的设计决策，确保了补偿操作的可靠性和数据一致性。

## 设计原理

### 为什么需要不可变性？

1. **补偿顺序的稳定性**
   - 补偿操作必须按照步骤的逆序执行
   - 如果 `step_index` 可以修改，会导致补偿顺序混乱
   - 可能造成业务逻辑错误和数据不一致

2. **事务完整性保证**
   - 步骤索引是事务执行顺序的唯一标识
   - 保持索引不变确保事务状态的可追溯性
   - 避免因索引变化导致的状态混乱

3. **并发安全性**
   - 在高并发环境下，固定的索引避免竞态条件
   - 防止多个服务同时修改索引导致的冲突

## 技术实现

### 数据库层面

```sql
-- 使用 ON DUPLICATE KEY UPDATE 排除 step_index 字段
INSERT INTO saga_steps (...) VALUES (...)
ON DUPLICATE KEY UPDATE 
    -- 更新所有字段，除了 step_index
    action = VALUES(action),
    service_name = VALUES(service_name),
    context_data = VALUES(context_data),
    compensation_context = VALUES(compensation_context),
    compensate_endpoint = VALUES(compensate_endpoint),
    compensation_status = VALUES(compensation_status),
    last_error = VALUES(last_error),
    retry_count = VALUES(retry_count),
    updated_at = VALUES(updated_at)
    -- 注意：step_index 被故意排除
```

### 代码实现

```go
// CreateOrUpdateStep 创建或更新步骤
// step_index 一旦创建就不能更改
func (dao *sagaStepsDao) CreateOrUpdateStep(ctx context.Context, tx gdb.TX, step *entity.SagaSteps) error {
    // 设置时间戳
    now := gtime.Now()
    if step.CreatedAt == nil {
        step.CreatedAt = now
    }
    step.UpdatedAt = now

    // OnDuplicateEx(dao.Columns().StepIndex) 确保在遇到唯一约束冲突时，
    // step_index 字段不会被更新，从而保证步骤索引的不可变性
    result, err := dao.DB().Model(dao.Table()).Ctx(ctx).OnDuplicateEx(dao.Columns().StepIndex).TX(tx).Save(step)
    if err != nil {
        return fmt.Errorf("保存步骤失败: %w", err)
    }

    // 检查操作结果
    affectedRows, err := result.RowsAffected()
    if err != nil {
        return fmt.Errorf("获取影响行数失败: %w", err)
    }
    if affectedRows == 0 {
        return fmt.Errorf("没有记录被保存")
    }

    return nil
}
```

## 工作流程

### 创建新步骤
1. 服务调用 `ReportCompensation` 上报补偿信息
2. 系统分配新的 `step_index`（Auto 模式）或使用预定义索引（Manual 模式）
3. 步骤记录被插入数据库，`step_index` 被永久固定

### 更新已存在步骤
1. 服务再次调用 `ReportCompensation`（幂等性场景）
2. 系统检测到重复的 `saga_id + action + service_name` 组合
3. 触发 `ON DUPLICATE KEY UPDATE` 逻辑
4. 更新除 `step_index` 之外的所有字段
5. `step_index` 保持原值不变

## 测试验证

### 单步骤测试
```go
func TestSagaSteps_StepIndexImmutability(t *testing.T) {
    // 场景1：首次创建步骤 - step_index 应该被正确设置
    // 场景2：更新已存在的步骤 - step_index 应该保持不变
}
```

### 多步骤测试
```go
func TestSagaSteps_MultipleStepsIndexStability(t *testing.T) {
    // 创建多个步骤，验证索引稳定性
    // 批量更新步骤，确认索引不变
}
```

## 业务场景示例

### 电商订单处理
```
步骤执行顺序：
1. step_index=1: 创建订单 (CreateOrder)
2. step_index=2: 处理支付 (ProcessPayment)  
3. step_index=3: 扣减库存 (ReserveInventory)
4. step_index=4: 创建配送 (CreateShipment)

补偿执行顺序（逆序）：
4. step_index=4: 取消配送 (CancelShipment)
3. step_index=3: 恢复库存 (RestoreInventory)
2. step_index=2: 退款处理 (RefundPayment)
1. step_index=1: 取消订单 (CancelOrder)
```

如果 `step_index` 可以修改，补偿顺序可能变成：
- 先退款再恢复库存 → 可能导致资金和库存不一致
- 先取消订单再取消配送 → 可能导致配送信息丢失

## 约束和限制

### 数据库约束
- `saga_id + action + service_name` 组合必须唯一
- `step_index` 在同一 `saga_id` 内必须唯一
- 不能直接通过 SQL 修改 `step_index`

### 应用层约束
- 服务不能主动修改已分配的 `step_index`
- Auto 模式下，索引由系统自动递增分配
- Manual 模式下，索引必须与预定义模板匹配

## 最佳实践

### 开发建议
1. **不要依赖 step_index 的可变性**
   - 设计业务逻辑时，假设索引是固定的
   - 如需重新排序，应该创建新的事务

2. **合理设计步骤粒度**
   - 步骤应该是原子性的业务操作
   - 避免过细或过粗的步骤划分

3. **充分测试补偿逻辑**
   - 验证补偿操作的逆序执行
   - 测试各种异常场景下的补偿行为

### 监控建议
1. **索引一致性监控**
   - 定期检查步骤索引的连续性
   - 监控是否存在重复或缺失的索引

2. **补偿执行监控**
   - 跟踪补偿操作的执行顺序
   - 确保按照正确的逆序执行

## 总结

`step_index` 的不可变性是 Saga 分布式事务系统的核心设计特性之一。它确保了：

- ✅ 补偿操作的正确执行顺序
- ✅ 事务状态的一致性和可追溯性  
- ✅ 高并发环境下的数据安全性
- ✅ 系统的可靠性和稳定性

通过严格的代码实现和全面的测试验证，这一特性为分布式事务的可靠执行提供了坚实的基础。
