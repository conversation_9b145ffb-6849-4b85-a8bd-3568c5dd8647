# StepIndex 管理模式使用指南

## 概述

Saga 分布式事务服务支持两种 `step_index` 管理模式，以适应不同的业务场景和复杂度需求：

1. **自增模式 (auto)**：适用于简单、线性流程，系统自动分配递增的步骤索引
2. **手动模式 (manual)**：适用于复杂流程，预定义步骤结构

## ⚠️ 重要限制说明

**当前版本存在以下限制：**

1. **不支持并行补偿**：所有补偿操作都是串行执行的，按照 `step_index` 逆序依次执行
2. **不支持复杂依赖关系**：手动模式虽然可以预定义步骤，但暂不支持复杂的步骤依赖关系管理
3. **不支持条件分支**：不支持基于条件的流程分支执行

**推荐的使用策略：**
- 对于简单的线性流程，优先使用自增模式
- 对于需要精确控制步骤顺序的复杂流程，使用手动模式
- 复杂的并行处理需求暂时需要在业务层协调实现

## 一、自增模式 (Auto Mode)

### 适用场景

✅ **推荐使用的场景：**
- 简单的串行业务流程
- 步骤顺序相对固定
- 不需要复杂的依赖关系
- 快速原型开发

❌ **不推荐的场景：**
- 复杂的并行处理需求
- 步骤间有复杂依赖关系
- 需要精确控制补偿顺序
- 频繁的流程结构变更

### 使用方法

#### 1. 创建 Saga 事务（自增模式）

```go
package main

import (
    "context"
    "sage/internal/service"
)

func createAutoModeSaga() {
    ctx := context.Background()
    sagaService := &service.SagaTransactionsService{}
    
    // 创建使用自增模式的 Saga 事务
    output, err := sagaService.CreateSagaTransaction(ctx, &model.CreateSagaTransactionInput{
        Name:          "用户注册流程",
        TimeoutSec:    3600,
        StepIndexMode: "auto", // 自增模式（也可以省略，默认为 auto）
        // StepTemplates: 不需要提供，系统会自动管理
    })
    
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("Saga 创建成功: %+v\n", output)
}
```

#### 2. 服务上报补偿操作

```go
func reportAutoModeCompensation() {
    ctx := context.Background()
    sagaService := &service.SagaTransactionsService{}
    
    // 第一个服务上报 - 自动分配 step_index = 1
    output1, err := sagaService.ReportCompensation(ctx, &model.ReportCompensationInput{
        SagaId:           "saga-12345",
        Action:           "CreateUser",
        ServiceName:      "user-service",
        CompensateAction: "DeleteUser",
        ContextData:      `{"user_id": "user-001", "username": "john_doe"}`,
        CompensationContext: `{"user_id": "user-001", "reason": "rollback"}`,
        CompensateEndpoint: "http://user-service/compensate/delete-user",
        // 注意：不需要提供 StepIndex，系统自动分配
    })
    
    // 第二个服务上报 - 自动分配 step_index = 2
    output2, err := sagaService.ReportCompensation(ctx, &model.ReportCompensationInput{
        SagaId:           "saga-12345",
        Action:           "SendWelcomeEmail",
        ServiceName:      "notification-service",
        CompensateAction: "SendCancelEmail",
        ContextData:      `{"user_id": "user-001", "email": "<EMAIL>"}`,
        CompensationContext: `{"user_id": "user-001", "email": "<EMAIL>", "reason": "registration_cancelled"}`,
        CompensateEndpoint: "http://notification-service/compensate/send-cancel-email",
    })
    
    // 第三个服务上报 - 自动分配 step_index = 3
    output3, err := sagaService.ReportCompensation(ctx, &model.ReportCompensationInput{
        SagaId:           "saga-12345",
        Action:           "CreateProfile",
        ServiceName:      "profile-service",
        CompensateAction: "DeleteProfile",
        ContextData:      `{"user_id": "user-001", "profile_id": "profile-001"}`,
        CompensationContext: `{"user_id": "user-001", "profile_id": "profile-001", "reason": "user_creation_failed"}`,
        CompensateEndpoint: "http://profile-service/compensate/delete-profile",
    })
}
```

### 工作原理

```mermaid
sequenceDiagram
    participant S1 as Service A
    participant S2 as Service B
    participant S3 as Service C
    participant Saga as Saga Service
    participant DB as Database

    S1->>Saga: ReportCompensation(Action="CreateUser")
    Saga->>DB: 查询最大 step_index (结果: 0)
    Saga->>DB: 插入记录 step_index=1
    Saga-->>S1: 返回成功

    S2->>Saga: ReportCompensation(Action="SendEmail")
    Saga->>DB: 查询最大 step_index (结果: 1)
    Saga->>DB: 插入记录 step_index=2
    Saga-->>S2: 返回成功

    S3->>Saga: ReportCompensation(Action="CreateProfile")
    Saga->>DB: 查询最大 step_index (结果: 2)
    Saga->>DB: 插入记录 step_index=3
    Saga-->>S3: 返回成功
```

## 二、手动模式 (Manual Mode)

### 适用场景

✅ **推荐使用的场景：**
- 复杂的业务流程
- 需要精确的补偿顺序
- 预定义的步骤结构
- 生产环境的关键业务

❌ **不推荐的场景：**
- 简单的线性流程
- 快速原型开发
- 流程结构经常变动

⚠️ **限制说明**：当前版本的手动模式不支持并行步骤处理和复杂的依赖关系控制，所有补偿操作都是串行执行的。

### 使用方法

#### 1. 创建 Saga 事务（手动模式）

```go
func createManualModeSaga() {
    ctx := context.Background()
    sagaService := &service.SagaTransactionsService{}
    
    // 定义步骤模板（基于实际的 SagaStepTemplate 结构体）
    templates := []model.SagaStepTemplate{
        {
            StepIndex:   1,
            Service:     "order-service",
            Action:      "CreateOrder",
            Description: "创建订单",
        },
        {
            StepIndex:   2,
            Service:     "payment-service",
            Action:      "ProcessPayment",
            Description: "处理支付",
        },
        {
            StepIndex:   3,
            Service:     "inventory-service",
            Action:      "ReserveInventory",
            Description: "预留库存",
        },
        {
            StepIndex:   4,
            Service:     "shipping-service",
            Action:      "CreateShipment",
            Description: "创建发货单",
        },
        {
            StepIndex:   5,
            Service:     "notification-service",
            Action:      "SendNotification",
            Description: "发送通知",
        },
    }
    
    // 创建使用手动模式的 Saga 事务
    output, err := sagaService.CreateSagaTransaction(ctx, &model.CreateSagaTransactionInput{
        Name:          "电商订单处理流程",
        TimeoutSec:    7200,
        StepIndexMode: "manual", // 手动模式
        StepTemplates: templates, // 提供步骤模板
    })
    
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("Saga 创建成功: %+v\n", output)
}
```

#### 2. 服务上报补偿操作

```go
func reportManualModeCompensation() {
    ctx := context.Background()
    sagaService := &service.SagaTransactionsService{}
    
    // 各服务按照预定义的步骤动作名称上报
    // 系统会自动匹配对应的 step_index
    
    // 订单服务上报 - 匹配 step_index = 1
    output1, err := sagaService.ReportCompensation(ctx, &model.ReportCompensationInput{
        SagaId:           "saga-12345",
        Action:           "CreateOrder", // 匹配模板中的 Action
        ServiceName:      "order-service",
        CompensateAction: "CancelOrder",
        ContextData:      `{"order_id": "12345", "user_id": "user-001"}`,
        CompensationContext: `{"order_id": "12345", "reason": "rollback"}`,
        CompensateEndpoint: "http://order-service/compensate/cancel-order",
    })
    
    // 支付服务上报 - 匹配 step_index = 2
    output2, err := sagaService.ReportCompensation(ctx, &model.ReportCompensationInput{
        SagaId:           "saga-12345",
        Action:           "ProcessPayment", // 匹配模板中的 Action
        ServiceName:      "payment-service",
        CompensateAction: "RefundPayment",
        ContextData:      `{"payment_id": "pay-67890", "amount": 100.00}`,
        CompensationContext: `{"payment_id": "pay-67890", "refund_reason": "saga_rollback"}`,
        CompensateEndpoint: "http://payment-service/compensate/refund-payment",
    })
    
    // 库存服务上报 - 匹配 step_index = 3
    output3, err := sagaService.ReportCompensation(ctx, &model.ReportCompensationInput{
        SagaId:           "saga-12345",
        Action:           "ReserveInventory", // 匹配模板中的 Action
        ServiceName:      "inventory-service",
        CompensateAction: "ReleaseInventory",
        ContextData:      `{"sku": "PROD-001", "quantity": 2}`,
        CompensationContext: `{"sku": "PROD-001", "quantity": 2, "reason": "order_cancelled"}`,
        CompensateEndpoint: "http://inventory-service/compensate/release-inventory",
    })
}
```

### 工作原理

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Saga as Saga Service
    participant DB as Database
    participant S1 as Order Service
    participant S2 as Payment Service
    participant S3 as Inventory Service

    Client->>Saga: CreateSagaTransaction(templates)
    Saga->>DB: 插入 saga_transactions 记录
    Saga->>DB: 预创建所有 saga_steps 记录
    Saga-->>Client: 返回 SagaId

    S1->>Saga: ReportCompensation(Action="CreateOrder")
    Saga->>DB: 查找 Action="CreateOrder" (找到 step_index=1)
    Saga->>DB: 更新 step_index=1 的补偿信息
    Saga-->>S1: 返回成功

    S2->>Saga: ReportCompensation(Action="ProcessPayment")
    Saga->>DB: 查找 Action="ProcessPayment" (找到 step_index=2)
    Saga->>DB: 更新 step_index=2 的补偿信息
    Saga-->>S2: 返回成功
```

## 三、对比分析

| 特性 | 自增模式 (Auto) | 手动模式 (Manual) |
|------|----------------|-------------------|
| **复杂度** | 简单 | 中等 |
| **初始化** | 无需预定义 | 需要预定义模板 |
| **步骤顺序** | 按上报顺序自动递增 | 按模板预定义 |
| **依赖管理** | 不支持 | 暂不支持 |
| **并行处理** | 不支持 | 暂不支持 |
| **补偿顺序** | 按 step_index 逆序 | 按 step_index 逆序 |
| **流程可视化** | 较难 | 容易 |
| **开发成本** | 低 | 中等 |
| **维护成本** | 低 | 中等 |
| **适用规模** | 小型项目 | 中大型项目 |

## 四、最佳实践

### 1. 选择合适的模式

```go
// 简单场景：用户注册流程
// CreateUser -> SendEmail -> CreateProfile
// 推荐：自增模式
sagaInput := &model.CreateSagaTransactionInput{
    Name:          "用户注册",
    StepIndexMode: "auto",
}

// 复杂场景：电商订单流程
// CreateOrder -> ProcessPayment -> ReserveInventory -> CreateShipment -> SendNotification
// 推荐：手动模式（注意：当前版本不支持并行处理）
sagaInput := &model.CreateSagaTransactionInput{
    Name:          "订单处理",
    StepIndexMode: "manual",
    StepTemplates: complexTemplates,
}
```

### 2. 步骤命名规范

```go
// 推荐的步骤命名规范
const (
    // 正向动作命名（使用动词+名词的格式）
    ActionCreateOrder      = "CreateOrder"
    ActionProcessPayment   = "ProcessPayment"
    ActionReserveInventory = "ReserveInventory"
    ActionCreateShipment   = "CreateShipment"
    ActionSendNotification = "SendNotification"
    
    // 补偿动作命名
    CompensateCancelOrder      = "CancelOrder"
    CompensateRefundPayment    = "RefundPayment"
    CompensateReleaseInventory = "ReleaseInventory"
    CompensateCancelShipment   = "CancelShipment"
    CompensateSendCancelEmail  = "SendCancelEmail"
)
```

### 3. 错误处理

```go
func handleCompensationWithRetry(sagaService *service.SagaTransactionsService, input *model.ReportCompensationInput) error {
    const maxRetries = 3
    var err error
    
    for i := 0; i < maxRetries; i++ {
        _, err = sagaService.ReportCompensation(context.Background(), input)
        if err == nil {
            return nil
        }
        
        // 记录重试
        log.Printf("补偿上报失败，第 %d 次重试: %v", i+1, err)
        
        // 指数退避
        time.Sleep(time.Duration(math.Pow(2, float64(i))) * time.Second)
    }
    
    return fmt.Errorf("补偿上报最终失败: %w", err)
}
```

## 五、注意事项

### 自增模式注意事项

1. **并发安全**：系统使用数据库锁保证 step_index 分配的原子性
2. **顺序依赖**：补偿时按 step_index 逆序执行，确保业务逻辑正确
3. **重复上报**：相同 Action 的重复上报会更新现有记录，不会创建新的 step_index

### 手动模式注意事项

1. **模板验证**：创建时会验证 StepIndex 的唯一性
2. **名称匹配**：服务上报时必须使用与模板完全一致的 Action 名称
3. **依赖关系**：当前版本不支持复杂的依赖关系控制，需要业务层自行协调步骤执行顺序
4. **并行处理**：当前版本不支持并行补偿操作，所有补偿都是串行执行

### 通用注意事项

1. **幂等性**：所有补偿操作都应该是幂等的
2. **超时处理**：长时间未上报的步骤可能需要超时处理机制
3. **状态一致性**：确保补偿操作的状态与实际业务状态一致

通过合理选择和使用这两种 StepIndex 管理模式，可以有效满足不同复杂度的分布式事务管理需求。

## 📋 快速参考

### SagaStepTemplate 结构体字段

```go
type SagaStepTemplate struct {
    StepIndex   int    `json:"step_index"`  // 步骤索引
    Service     string `json:"service"`     // 服务名称
    Action      string `json:"action"`      // 动作名称
    Description string `json:"description"` // 描述
}
```

### 模式选择建议

| 场景类型 | 推荐模式 | 理由 |
|---------|---------|------|
| 用户注册流程 | 自增模式 | 简单、线性、步骤固定 |
| 简单订单处理 | 自增模式 | 步骤较少、流程简单 |
| 复杂电商流程 | 手动模式 | 步骤较多、需要精确控制 |
| 金融交易处理 | 手动模式 | 严格的步骤顺序要求 |

### 注意事项提醒

- ✅ 所有补偿操作都必须实现幂等性
- ✅ 使用正确的结构体字段名称（Action 而非 StepName）
- ⚠️ 当前版本不支持并行补偿操作
- ⚠️ 复杂依赖关系需要业务层协调
- ⚠️ 建议在生产环境中进行充分测试 

## 六、异步系统适用性分析

### Manual 模式在异步系统中的考虑

#### ✅ **适用的异步场景**

1. **严格顺序的异步流程**
```go
// 金融交易：步骤必须按顺序执行
templates := []model.SagaStepTemplate{
    {StepIndex: 1, Service: "risk-service", Action: "RiskAssessment"},
    {StepIndex: 2, Service: "account-service", Action: "DebitAccount"},
    {StepIndex: 3, Service: "account-service", Action: "CreditAccount"},
    {StepIndex: 4, Service: "audit-service", Action: "RecordTransaction"},
}
```

2. **长时间运行的批处理流程**
```go
// ETL 流程：预定义步骤便于监控
templates := []model.SagaStepTemplate{
    {StepIndex: 1, Service: "extract-service", Action: "ExtractData"},
    {StepIndex: 2, Service: "transform-service", Action: "TransformData"},
    {StepIndex: 3, Service: "load-service", Action: "LoadData"},
    {StepIndex: 4, Service: "notify-service", Action: "SendNotification"},
}
```

#### ❌ **不适用的异步场景**

1. **高并发无序消息处理**
```go
// 问题：消息可能无序到达，但manual模式期望固定顺序
// 用户行为分析：点击、浏览、购买等事件可能乱序
// 推荐：使用 auto 模式
```

2. **动态流程和条件分支**
```go
// 问题：异步系统常需要根据条件动态调整流程
// Manual模式的预定义结构无法支持这种灵活性
```

3. **高度并行的异步处理**
```go
// 问题：当前manual模式不支持并行补偿
// 异步系统通常需要同时处理多个独立任务
```

### 异步系统使用建议

#### 1. **消息幂等性设计**
```go
func (s *SagaTransactionsService) ReportCompensation(ctx context.Context, input *model.ReportCompensationInput) (*model.ReportCompensationOutput, error) {
    // 异步消息可能重复，确保幂等性
    // 相同的 Action 重复上报会更新现有记录
    return s.handleIdempotentReport(ctx, input)
}
```

#### 2. **超时和重试机制**
```go
// 异步系统中某些步骤可能延迟或失败
// 建议配置合适的超时时间
sagaInput := &model.CreateSagaTransactionInput{
    Name:          "异步订单处理",
    TimeoutSec:    7200, // 2小时超时
    StepIndexMode: "manual",
    StepTemplates: templates,
}
```

#### 3. **监控和可观测性**
```go
// 异步系统中步骤执行状态难以追踪
// Manual模式的预定义结构有助于监控
func monitorAsyncSagaProgress(sagaId string) {
    // 检查每个预定义步骤的执行状态
    // 识别延迟或失败的步骤
}
```

### 替代方案建议

#### 对于复杂异步系统，考虑以下替代方案：

1. **混合模式**：核心流程使用 manual 模式，辅助流程使用 auto 模式
2. **状态机模式**：使用状态机来管理复杂的异步流程
3. **事件驱动架构**：基于事件的异步处理，而非预定义步骤

### 总结

- **适合**：有序、固定的异步流程，如金融交易、ETL 处理
- **不适合**：高并发、动态、无序的异步消息处理
- **关键**：确保异步消息的幂等性和顺序性处理

## 七、最佳实践 