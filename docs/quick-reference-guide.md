# Enhanced Data Initialization Script - Quick Reference Guide

## Quick Start Commands

### Basic Usage
```bash
# Standard execution (1M records)
mysql -u username -p database_name < enhanced-initialize-test-data.sql

# With logging
mysql -u username -p database_name < enhanced-initialize-test-data.sql > generation.log 2>&1
```

## Common Configuration Scenarios

### 1. Development Environment (Fast Setup)
```sql
SET @total_sagas = 1000;
SET @batch_size = 500;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
```
**Expected time:** 1-2 minutes  
**Memory usage:** ~5MB

### 2. Testing Environment (Moderate Dataset)
```sql
SET @total_sagas = 100000;
SET @batch_size = 10000;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = TRUE;
```
**Expected time:** 3-8 minutes  
**Memory usage:** ~80MB

### 3. Performance Testing (Large Dataset)
```sql
SET @total_sagas = 1000000;
SET @batch_size = 50000;
SET @enable_progress_output = TRUE;
SET @enable_validation = TRUE;
SET @enable_performance_mode = TRUE;
```
**Expected time:** 8-15 minutes  
**Memory usage:** ~400MB

### 4. Stress Testing (Maximum Dataset)
```sql
SET @total_sagas = 10000000;
SET @batch_size = 100000;
SET @enable_progress_output = FALSE;
SET @enable_validation = FALSE;
SET @enable_performance_mode = TRUE;
```
**Expected time:** 45-90 minutes  
**Memory usage:** ~800MB

## Status Distribution Presets

### Realistic Production Simulation
```sql
SET @status_running_pct = 50;      -- Active transactions
SET @status_completed_pct = 25;    -- Successful completions
SET @status_pending_pct = 10;      -- Waiting to start
SET @status_compensating_pct = 10; -- In rollback
SET @status_failed_pct = 5;        -- Terminal failures
```

### High Load Testing
```sql
SET @status_running_pct = 70;      -- More active load
SET @status_completed_pct = 15;
SET @status_pending_pct = 10;
SET @status_compensating_pct = 3;
SET @status_failed_pct = 2;
```

### Error Scenario Testing
```sql
SET @status_running_pct = 30;
SET @status_completed_pct = 20;
SET @status_pending_pct = 15;
SET @status_compensating_pct = 25; -- More compensation testing
SET @status_failed_pct = 10;       -- More error scenarios
```

## Performance Optimization Quick Settings

### For SSD Storage
```sql
SET @batch_size = 100000;
SET @bulk_insert_buffer_size = '512M';
SET @tmp_table_size = '1G';
```

### For Limited Memory (< 4GB)
```sql
SET @batch_size = 10000;
SET @bulk_insert_buffer_size = '128M';
SET @tmp_table_size = '256M';
SET @enable_validation = FALSE;
```

### For Maximum Speed
```sql
SET @enable_progress_output = FALSE;
SET @enable_validation = FALSE;
SET @log_batch_progress = FALSE;
SET @log_performance_metrics = FALSE;
```

## Troubleshooting Quick Fixes

### Out of Memory
```sql
SET @batch_size = 5000;           -- Reduce batch size
SET @tmp_table_size = '128M';     -- Reduce temp table size
SET sql_big_tables = 1;           -- Use disk-based temp tables
```

### Slow Performance
```sql
SET @enable_performance_mode = TRUE;
SET @enable_validation = FALSE;
SET @batch_size = 25000;
```

### Connection Timeouts
```sql
SET SESSION wait_timeout = 28800;
SET SESSION interactive_timeout = 28800;
SET @batch_size = 10000;          -- Smaller batches process faster
```

## Monitoring Commands

### Check Progress During Execution
```sql
-- In another MySQL session
SELECT COUNT(*) as current_sagas FROM saga_transactions;
SELECT COUNT(*) as current_steps FROM saga_steps;

-- Check batch progress
SELECT * FROM batch_progress_tracking ORDER BY batch_number DESC LIMIT 5;
```

### System Resource Monitoring
```bash
# Memory usage
free -h

# Disk space
df -h

# MySQL processes
mysql -e "SHOW PROCESSLIST;"

# Real-time log monitoring
tail -f generation.log
```

## Validation Quick Checks

### Data Integrity
```sql
-- Check record counts
SELECT 
    (SELECT COUNT(*) FROM saga_transactions) as saga_count,
    (SELECT COUNT(*) FROM saga_steps) as steps_count,
    (SELECT COUNT(*) FROM saga_steps) / (SELECT COUNT(*) FROM saga_transactions) as avg_steps_per_saga;

-- Check status distribution
SELECT 
    saga_status, 
    COUNT(*) as count, 
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 1) as percentage
FROM saga_transactions 
GROUP BY saga_status 
ORDER BY count DESC;
```

### Business Scenario Distribution
```sql
-- Check business type distribution (from saga names)
SELECT 
    CASE 
        WHEN name LIKE 'Ecommerce%' THEN 'ecommerce'
        WHEN name LIKE 'Payment%' THEN 'payment'
        WHEN name LIKE 'Inventory%' THEN 'inventory'
        WHEN name LIKE 'User%' THEN 'user'
        WHEN name LIKE 'Sync%' THEN 'sync'
        ELSE 'other'
    END as business_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM saga_transactions), 1) as percentage
FROM saga_transactions 
GROUP BY business_type 
ORDER BY count DESC;
```

## Emergency Cleanup

### Stop and Clean Up
```sql
-- Clean up temporary tables
DROP TEMPORARY TABLE IF EXISTS batch_progress_tracking;
DROP TEMPORARY TABLE IF EXISTS overall_progress_summary;
DROP TEMPORARY TABLE IF EXISTS script_execution_log;
DROP TEMPORARY TABLE IF EXISTS business_step_templates;

-- Restore MySQL settings
SET foreign_key_checks = 1;
SET unique_checks = 1;
SET autocommit = 1;
```

### Complete Data Reset
```sql
-- WARNING: This deletes all saga data
DELETE FROM saga_steps;
DELETE FROM saga_transactions;
ALTER TABLE saga_transactions AUTO_INCREMENT = 1;
ALTER TABLE saga_steps AUTO_INCREMENT = 1;
```

## File Locations and Logs

### Script Files
- Main script: `enhanced-initialize-test-data.sql`
- Documentation: `docs/data-initialization-script-guide.md`
- Quick reference: `docs/quick-reference-guide.md`

### Log Files
- Execution log: `generation.log` (if redirected)
- MySQL error log: `/var/log/mysql/error.log`
- Performance metrics: Stored in temporary tables during execution

## Support and Resources

### Getting Help
1. Check this quick reference for common scenarios
2. Review the complete user guide: `docs/data-initialization-script-guide.md`
3. Check MySQL error logs for detailed error messages
4. Test with smaller datasets to isolate issues

### Performance Benchmarks
| Dataset Size | Expected Time | Memory Usage | Recommended Batch Size |
|--------------|---------------|--------------|----------------------|
| 1K records | 30 seconds | 5MB | 500 |
| 10K records | 2 minutes | 20MB | 2,000 |
| 100K records | 5 minutes | 80MB | 10,000 |
| 1M records | 12 minutes | 400MB | 50,000 |
| 10M records | 60 minutes | 800MB | 100,000 |

*Times are approximate and depend on hardware configuration*