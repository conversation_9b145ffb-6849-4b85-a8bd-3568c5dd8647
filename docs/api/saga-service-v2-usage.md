# Saga 服务 V2 使用指南

## 概述

Saga 服务 V2 是基于策略模式重构的分布式事务服务，支持在乐观锁和悲观锁之间灵活切换，提供更好的可维护性和扩展性。

## 主要特性

### 🔄 锁策略切换
- **悲观锁模式**：使用数据库行锁，适合低并发场景
- **乐观锁模式**：使用版本号控制，适合高并发场景
- **动态切换**：运行时可以切换锁策略，无需重启服务

### 🏗️ 架构优势
- **策略模式**：锁机制与业务逻辑分离
- **代码复用**：消除重复代码，提高可维护性
- **易于扩展**：新增锁策略只需实现接口
- **职责清晰**：每个类只负责一个职责

## 快速开始

### 1. 创建服务管理器

```go
package main

import (
    "context"
    "saga/internal/service"
)

func main() {
    // 创建服务管理器
    manager := service.NewSagaServiceManager()
    
    // 获取服务实例
    sagaService := manager.GetService()
    
    // 查看当前锁策略
    info := sagaService.GetLockStrategyInfo()
    fmt.Printf("当前锁策略: %s\n", info["strategy_name"])
}
```

### 2. 基本操作

```go
ctx := context.Background()

// 创建事务
createInput := &model.CreateSagaTransactionInput{
    Name:                  "订单处理事务",
    StepIndexMode:         consts.StepIndexModeAuto,
    CompensationWindowSec: 300,
}

createOutput, err := sagaService.CreateSagaTransaction(ctx, createInput)
if err != nil {
    log.Fatal(err)
}

sagaId := createOutput.SagaId

// 上报补偿信息
reportInput := &model.ReportCompensationInput{
    SagaId:              sagaId,
    Action:              "create_order",
    ServiceName:         "order_service",
    ContextData:         `{"orderId": "12345", "amount": 100}`,
    CompensationContext: `{"orderId": "12345"}`,
    CompensateEndpoint:  "http://order-service/compensate",
}

reportOutput, err := sagaService.ReportCompensation(ctx, reportInput)
if err != nil {
    log.Fatal(err)
}

// 提交事务
commitInput := &model.CommitSagaTransactionInput{
    SagaId: sagaId,
}

commitOutput, err := sagaService.CommitSagaTransaction(ctx, commitInput)
if err != nil {
    log.Fatal(err)
}
```

### 3. 锁策略切换

```go
// 获取服务管理器
manager := service.NewSagaServiceManager()
sagaService := manager.GetService()

// 切换到乐观锁模式
optimisticConfig := &service.OptimisticLockConfig{
    MaxRetries:        5,
    BaseDelay:         5 * time.Millisecond,
    MaxDelay:          200 * time.Millisecond,
    BackoffMultiplier: 2.0,
}
manager.SwitchLockStrategy(true, optimisticConfig)

// 切换到悲观锁模式
manager.SwitchLockStrategy(false, nil)

// 查看当前策略信息
info := manager.GetLockStrategyInfo()
fmt.Printf("策略: %s, 乐观锁: %v\n", info["strategy_name"], info["is_optimistic"])
```

## 配置管理

### 配置文件示例

```yaml
# config-v2.yaml
saga:
  # 乐观锁配置
  optimisticLock:
    enabled: false              # 是否启用乐观锁模式
    maxRetries: 5               # 最大重试次数
    baseDelay: "5ms"            # 基础延迟时间
    maxDelay: "200ms"           # 最大延迟时间
    backoffMultiplier: 2.0      # 指数退避倍数

  # 补偿配置
  compensation:
    timeout: "30s"              # 补偿操作超时时间
    defaultWindowSec: 300       # 默认补偿窗口期
    reportMaxRetries: 3         # 补偿上报最大重试次数
    reportRetryDelay: "1s"      # 补偿上报重试延迟

  # 重试配置
  retry:
    maxRetries: 3               # 最大重试次数
    initialInterval: "1s"       # 初始重试间隔
    maxInterval: "30s"          # 最大重试间隔
    multiplier: 2.0             # 指数退避倍数
```

### 动态配置更新

```go
// 重新加载配置
manager.ReloadConfig()

// 使用自定义配置创建服务
customConfig := &service.SagaServiceConfig{
    OptimisticLock: service.OptimisticLockSettings{
        Enabled:           true,
        MaxRetries:        10,
        BaseDelay:         1 * time.Millisecond,
        MaxDelay:          500 * time.Millisecond,
        BackoffMultiplier: 1.5,
    },
    // ... 其他配置
}

factory := service.NewSagaServiceFactory()
customService := factory.CreateServiceWithCustomConfig(customConfig)
```

## 性能优化建议

### 1. 锁策略选择

**悲观锁适用场景：**
- 低并发环境（< 100 TPS）
- 锁冲突概率低
- 对一致性要求极高

**乐观锁适用场景：**
- 高并发环境（> 100 TPS）
- 锁冲突概率相对较低
- 可以容忍重试开销

### 2. 乐观锁参数调优

```go
// 高并发场景推荐配置
highConcurrencyConfig := service.OptimisticLockConfig{
    MaxRetries:        10,      // 增加重试次数
    BaseDelay:         1 * time.Millisecond,  // 减少基础延迟
    MaxDelay:          100 * time.Millisecond, // 适中的最大延迟
    BackoffMultiplier: 1.5,     // 较小的退避倍数
}

// 低冲突场景推荐配置
lowConflictConfig := service.OptimisticLockConfig{
    MaxRetries:        3,       // 较少重试次数
    BaseDelay:         10 * time.Millisecond, // 较大基础延迟
    MaxDelay:          200 * time.Millisecond, // 较大最大延迟
    BackoffMultiplier: 2.0,     // 标准退避倍数
}
```

### 3. 监控指标

```go
// 获取锁策略信息用于监控
info := sagaService.GetLockStrategyInfo()

// 记录关键指标
metrics := map[string]interface{}{
    "lock_strategy":      info["strategy_name"],
    "is_optimistic":      info["is_optimistic"],
    "max_retries":        info["max_retries"],
    "base_delay_ms":      info["base_delay"],
}

// 发送到监控系统
sendMetrics(metrics)
```

## 最佳实践

### 1. 错误处理

```go
reportOutput, err := sagaService.ReportCompensation(ctx, reportInput)
if err != nil {
    // 检查是否是版本冲突错误
    if strings.Contains(err.Error(), "version_conflict") {
        log.Warn("乐观锁版本冲突，可能需要调整重试参数")
    }
    
    // 记录详细错误信息
    log.Errorf("补偿上报失败: SagaId=%s, Error=%v", reportInput.SagaId, err)
    return err
}
```

### 2. 性能测试

```go
// 并发测试
func TestConcurrentOperations(t *testing.T) {
    manager := service.NewSagaServiceManager()
    sagaService := manager.GetService()
    
    // 测试悲观锁性能
    manager.SwitchLockStrategy(false, nil)
    pessimisticDuration := benchmarkOperations(sagaService, 100)
    
    // 测试乐观锁性能
    optimisticConfig := &service.OptimisticLockConfig{
        MaxRetries:        5,
        BaseDelay:         1 * time.Millisecond,
        MaxDelay:          50 * time.Millisecond,
        BackoffMultiplier: 2.0,
    }
    manager.SwitchLockStrategy(true, optimisticConfig)
    optimisticDuration := benchmarkOperations(sagaService, 100)
    
    log.Printf("悲观锁耗时: %v, 乐观锁耗时: %v", pessimisticDuration, optimisticDuration)
}
```

### 3. 渐进式迁移

```go
// 在现有代码中逐步引入V2服务
type SagaServiceWrapper struct {
    v1Service *service.SagaTransactionsService
    v2Service *service.SagaTransactionsServiceV2
    useV2     bool
}

func (w *SagaServiceWrapper) ReportCompensation(ctx context.Context, input *model.ReportCompensationInput) (*model.ReportCompensationOutput, error) {
    if w.useV2 {
        return w.v2Service.ReportCompensation(ctx, input)
    }
    return w.v1Service.ReportCompensation(ctx, input)
}

// 通过配置控制是否使用V2
func (w *SagaServiceWrapper) EnableV2(enable bool) {
    w.useV2 = enable
    log.Printf("Saga服务V2已%s", map[bool]string{true: "启用", false: "禁用"}[enable])
}
```

## 故障排除

### 常见问题

1. **乐观锁重试失败**
   - 检查并发量是否过高
   - 调整重试参数
   - 考虑切换到悲观锁

2. **性能下降**
   - 监控锁等待时间
   - 分析锁冲突模式
   - 优化业务逻辑

3. **配置不生效**
   - 检查配置文件路径
   - 验证配置格式
   - 重新加载配置

### 调试技巧

```go
// 启用详细日志
g.Log().SetLevel("debug")

// 查看当前配置
config := manager.GetCurrentConfig()
fmt.Printf("当前配置: %+v\n", config)

// 监控锁策略切换
originalStrategy := sagaService.GetLockStrategy().GetStrategyName()
// ... 执行操作
newStrategy := sagaService.GetLockStrategy().GetStrategyName()
if originalStrategy != newStrategy {
    log.Printf("锁策略已切换: %s -> %s", originalStrategy, newStrategy)
}
```

## 总结

Saga 服务 V2 通过策略模式重构，提供了更灵活的锁机制选择和更好的代码结构。在使用时，建议：

1. 根据业务场景选择合适的锁策略
2. 通过性能测试验证配置参数
3. 建立完善的监控和告警机制
4. 采用渐进式迁移策略

通过合理使用 V2 服务，可以在保证功能完整性的同时，获得更好的性能和可维护性。
