# Saga 事务 API 文档

## 概述

本文档描述了 Saga 事务管理系统的 REST API 接口，包含事务创建、补偿上报、事务提交和回滚等核心功能。

## 基础信息

- **Base URL**: `http://localhost:8080`
- **Content-Type**: `application/json`
- **API 版本**: v1

## API 接口列表

### 1. 创建分布式事务

创建一个新的 Saga 分布式事务。

**请求信息**
- **方法**: `POST`
- **路径**: `/saga/transactions`
- **Tags**: Saga事务

**请求参数**

| 参数名 | 类型 | 必填 | 说明 | 验证规则 |
|--------|------|------|------|----------|
| name | string | 是 | Saga名称 | 长度1-100字符 |
| stepIndexMode | string | 否 | StepIndex管理模式 | auto/manual |
| stepTemplates | array | 否 | 步骤模板（仅manual模式使用） | - |

**步骤模板 (SagaStepTemplate)**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| step_index | int | 步骤索引 |
| service | string | 服务名称 |
| action | string | 动作名称 |
| description | string | 描述 |

**响应参数**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| sagaId | string | Saga事务ID |
| name | string | Saga名称 |
| status | string | 当前状态 |
| createdAt | string | 创建时间 |

**curl 示例**

```bash
# 创建 Auto 模式的 Saga 事务
curl -X POST http://localhost:8080/saga/transactions \
  -H "Content-Type: application/json" \
  -d '{
    "name": "订单处理事务",
    "stepIndexMode": "auto"
  }'

# 创建 Manual 模式的 Saga 事务
curl -X POST http://localhost:8080/saga/transactions \
  -H "Content-Type: application/json" \
  -d '{
    "name": "支付处理事务",
    "stepIndexMode": "manual",
    "stepTemplates": [
      {
        "step_index": 1,
        "service": "order-service",
        "action": "CreateOrder",
        "description": "创建订单"
      },
      {
        "step_index": 2,
        "service": "payment-service",
        "action": "ProcessPayment",
        "description": "处理支付"
      },
      {
        "step_index": 3,
        "service": "inventory-service",
        "action": "ReserveInventory",
        "description": "库存预留"
      }
    ]
  }'
```

**响应示例**

```json
{
  "sagaId": "63f1e2b4-1234-5678-9abc-def012345678",
  "name": "订单处理事务",
  "status": "pending",
  "createdAt": "2024-01-15T10:30:00Z"
}
```

---

### 2. 上报补偿操作结果

服务节点向 Saga 事务上报补偿操作的相关信息。

**请求信息**
- **方法**: `POST`
- **路径**: `/saga/transactions/compensation`
- **Tags**: Saga事务

**请求参数**

| 参数名 | 类型 | 必填 | 说明 | 验证规则 |
|--------|------|------|------|----------|
| sagaId | string | 是 | Saga事务ID | 长度1-50字符 |
| action | string | 是 | 步骤名称 | 长度1-100字符 |
| serviceName | string | 是 | 服务名称 | 长度1-100字符 |
| contextData | string | 否 | 正向执行的上下文参数 | - |
| compensationContext | string | 是 | 补偿需要的上下文参数 | 必须是有效JSON |
| compensateEndpoint | string | 否 | 补偿接口URL | 最大长度500字符 |

**响应参数**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 是否成功 |

**curl 示例**

```bash
curl -X POST http://localhost:8080/saga/transactions/compensation \
  -H "Content-Type: application/json" \
  -d '{
    "sagaId": "8yp5ou0tuk0dbb1lqzg2e6o100snda2i",
    "action": "PayOrder",
    "serviceName": "order-service",
    "contextData": "{\"orderId\":\"12345\",\"userId\":\"user123\"}",
    "compensationContext": "{\"orderId\":\"12345\",\"reason\":\"cancel_order\"}",
    "compensateEndpoint": "http://order-service:8080/orders/compensate"
  }'
```

**响应示例**

```json
{
  "success": true
}
```

---

### 3. 获取Saga事务信息

根据 SagaId 获取 Saga 事务的详细信息。

**请求信息**
- **方法**: `GET`
- **路径**: `/saga/transactions/{sagaId}`
- **Tags**: Saga事务

**路径参数**

| 参数名 | 类型 | 必填 | 说明 | 验证规则 |
|--------|------|------|------|----------|
| sagaId | string | 是 | Saga事务ID | 长度1-50字符 |

**响应参数**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| sagaId | string | Saga事务ID |
| name | string | Saga名称 |
| status | string | 当前状态 |
| currentStep | string | 当前步骤 |
| retryCount | int | 重试次数 |
| createdAt | string | 创建时间 |
| updatedAt | string | 更新时间 |

**curl 示例**

```bash
curl -X GET http://localhost:8080/saga/transactions/8yp5ou0tuk0dbb1lqzg2e6o100snda2i \
  -H "Content-Type: application/json"
```

**响应示例**

```json
{
  "sagaId": "63f1e2b4-1234-5678-9abc-def012345678",
  "name": "订单处理事务",
  "status": "running",
  "currentStep": "2",
  "retryCount": 0,
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:35:00Z"
}
```

---

### 4. 提交分布式事务

提交 Saga 事务，将事务状态标记为完成。

**请求信息**
- **方法**: `POST`
- **路径**: `/saga/transactions/commit`
- **Tags**: Saga事务

**请求参数**

| 参数名 | 类型 | 必填 | 说明 | 验证规则 |
|--------|------|------|------|----------|
| sagaId | string | 是 | Saga事务ID | 长度1-50字符 |

**响应参数**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 是否成功 |
| message | string | 响应消息 |
| sagaId | string | Saga事务ID |
| completedSteps | int | 已完成步骤数 |
| expectedSteps | int | 预期步骤数 |
| newStatus | string | 新的状态 |
| completedAt | string | 完成时间 |

**curl 示例**

```bash
curl -X POST http://localhost:8080/saga/transactions/commit \
  -H "Content-Type: application/json" \
  -d '{
    "sagaId": "8yp5ou0tuk0dbb1lqzg2e6o100snda2i"
  }'
```

**响应示例**

```json
{
  "success": true,
  "message": "分布式事务提交成功",
  "sagaId": "63f1e2b4-1234-5678-9abc-def012345678",
  "completedSteps": 3,
  "expectedSteps": 3,
  "newStatus": "completed",
  "completedAt": "2024-01-15T10:45:00Z"
}
```

---

### 5. 回滚分布式事务

回滚 Saga 事务，支持多种执行模式。

**请求信息**
- **方法**: `POST`
- **路径**: `/saga/transactions/rollback`
- **Tags**: Saga事务

**请求参数**

| 参数名 | 类型 | 必填 | 说明 | 验证规则 |
|--------|------|------|------|----------|
| sagaId | string | 是 | Saga事务ID | 长度1-50字符 |
| failReason | string | 是 | 回滚原因 | 长度1-500字符 |
| failedStep | string | 否 | 失败的步骤名称 | 最大长度100字符 |
| executionMode | string | 否 | 执行模式 | none/sync/async |

**执行模式说明**

- **none**: 仅标记状态为补偿中，不执行补偿操作
- **sync**: 同步执行所有补偿操作
- **async**: 异步执行补偿操作

**响应参数**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 是否成功启动回滚 |
| message | string | 响应消息 |
| sagaId | string | Saga事务ID |
| newStatus | string | 新的状态 |
| totalStepsToRoll | int | 需要回滚的步骤总数 |
| completedCompensations | int | 已完成补偿的步骤数 |
| failedCompensations | int | 补偿失败的步骤数 |
| pendingCompensations | int | 待补偿的步骤数 |
| isRollbackCompleted | boolean | 回滚是否完成 |
| startedAt | string | 回滚开始时间 |
| completedAt | string | 回滚完成时间（如果已完成） |
| failReason | string | 失败原因 |

**curl 示例**

```bash
# 仅标记状态（none 模式）
curl -X POST http://localhost:8080/saga/transactions/rollback \
  -H "Content-Type: application/json" \
  -d '{
    "sagaId": "63f1e2b4-1234-5678-9abc-def012345678",
    "failReason": "支付服务异常",
    "failedStep": "ProcessPayment",
    "maxRetries": 3,
    "compensationTimeout": 30000,
    "executionMode": "none"
  }'

# 同步执行补偿（sync 模式）
curl -X POST http://localhost:8080/saga/transactions/rollback \
  -H "Content-Type: application/json" \
  -d '{
    "sagaId": "63f1e2b4-1234-5678-9abc-def012345678",
    "failReason": "库存不足",
    "failedStep": "ReserveInventory",
    "maxRetries": 5,
    "compensationTimeout": 45000,
    "executionMode": "sync"
  }'

# 异步执行补偿（async 模式）
curl -X POST http://localhost:8080/saga/transactions/rollback \
  -H "Content-Type: application/json" \
  -d '{
    "sagaId": "63f1e2b4-1234-5678-9abc-def012345678",
    "failReason": "订单处理超时",
    "failedStep": "CreateOrder",
    "maxRetries": 2,
    "compensationTimeout": 60000,
    "executionMode": "async"
  }'
```

**响应示例**

```json
{
  "success": true,
  "message": "回滚已启动，正在异步执行补偿操作",
  "sagaId": "63f1e2b4-1234-5678-9abc-def012345678",
  "newStatus": "compensating",
  "totalStepsToRoll": 2,
  "completedCompensations": 0,
  "failedCompensations": 0,
  "pendingCompensations": 2,
  "isRollbackCompleted": false,
  "startedAt": "2024-01-15T10:40:00Z",
  "completedAt": null,
  "failReason": "订单处理超时"
}
```

---

## 状态码说明

### Saga 事务状态

| 状态 | 说明 |
|------|------|
| pending | 待处理 |
| running | 运行中 |
| completed | 已完成 |
| compensating | 补偿中 |
| failed | 已失败 |

### 补偿状态

| 状态 | 说明 |
|------|------|
| uninitialized | 未初始化 |
| pending | 待补偿 |
| running | 补偿中 |
| completed | 补偿完成 |
| failed | 补偿失败 |

## 错误码说明

### HTTP 状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 业务错误示例

```json
{
  "error": {
    "code": "SAGA_NOT_FOUND",
    "message": "saga 事务不存在: sagaId=invalid-saga-id"
  }
}
```

```json
{
  "error": {
    "code": "INVALID_STATUS",
    "message": "无法提交事务，当前状态为: compensating，只有 running 状态的事务才能提交"
  }
}
```

## 使用流程示例

### 1. 正常流程

```bash
# 1. 创建 Saga 事务
curl -X POST http://localhost:8080/saga/transactions \
  -H "Content-Type: application/json" \
  -d '{"name": "订单处理", "stepIndexMode": "auto"}'

# 2. 各服务上报补偿信息
curl -X POST http://localhost:8080/saga/transactions/compensation \
  -H "Content-Type: application/json" \
  -d '{
    "sagaId": "saga-001",
    "action": "CreateOrder",
    "serviceName": "order-service",
    "compensationContext": "{\"orderId\":\"12345\"}",
    "compensateEndpoint": "http://order-service:8080/compensate"
  }'

# 3. 提交事务
curl -X POST http://localhost:8080/saga/transactions/commit \
  -H "Content-Type: application/json" \
  -d '{"sagaId": "saga-001"}'
```

### 2. 异常流程（回滚）

```bash
# 1. 创建 Saga 事务和上报补偿信息（同上）

# 2. 发生异常，执行回滚
curl -X POST http://localhost:8080/saga/transactions/rollback \
  -H "Content-Type: application/json" \
  -d '{
    "sagaId": "saga-001",
    "failReason": "支付失败",
    "executionMode": "sync"
  }'
```

## 注意事项

1. **StepIndex 管理模式**：
   - `auto` 模式：系统自动分配递增的步骤索引
   - `manual` 模式：使用预定义的步骤模板匹配索引

2. **补偿执行顺序**：
   - 补偿操作按照 `step_index` 逆序执行
   - 确保业务逻辑的一致性

3. **超时设置**：
   - 合理设置 `compensationTimeout` 避免长时间阻塞
   - 建议根据业务复杂度调整超时时间

4. **重试机制**：
   - 系统支持指数退避重试
   - 可通过 `maxRetries` 参数控制重试次数

5. **并发控制**：
   - 系统内部使用信号量控制并发任务数量
   - 避免过多并发导致系统负载过高 