# 补偿服务对接文档

## 概述

本文档描述了业务服务如何实现补偿接口，以便与 Saga 分布式事务管理系统进行集成。Saga 服务会在事务回滚时调用各业务服务的补偿接口来撤销已执行的操作。

## 接口规范

### HTTP 方法
- **POST** - 所有补偿接口必须使用 POST 方法

### 请求头部

Saga 服务会在调用补偿接口时自动添加以下HTTP头部信息：

| 头部名称 | 类型 | 说明 | 示例值 |
|---------|------|------|--------|
| `X-Saga-Id` | string | 分布式事务ID，用于关联整个事务流程 | `saga-12345-abcde` |
| `X-Step-Id` | string | 步骤ID，唯一标识当前补偿步骤 | `step-67890-fghij` |
| `X-Step-Index` | string | 步骤索引，表示在事务中的执行顺序 | `3` |
| `X-Service-Name` | string | 服务名称，标识当前业务服务 | `user-service` |
| `X-Action` | string | 原始操作名称，标识需要补偿的操作 | `create_user` |
| `Content-Type` | string | 请求内容类型 | `application/json` |

### 请求体

请求体包含补偿操作所需的上下文数据，格式为 JSON：

```json
{
  "user_id": "12345",
  "order_id": "order-67890",
  "amount": 100.50,
  "operation_time": "2024-01-15T10:30:00Z",
  "additional_data": {
    "reason": "transaction_rollback",
    "rollback_time": "2024-01-15T11:00:00Z"
  }
}
```

### 响应规范

#### 成功响应
- **HTTP状态码**: `200-299` 范围内的任意状态码都表示补偿成功
- **响应体**: 可选，Saga 服务不会解析响应体内容

#### 失败响应
- **HTTP状态码**: `300` 及以上的状态码都表示补偿失败
- **响应体**: 可选，建议包含错误信息用于调试

#### 推荐的响应示例

**成功响应 (200 OK):**
```json
{
  "success": true,
  "message": "用户删除补偿操作完成",
  "compensation_id": "comp-12345",
  "timestamp": "2024-01-15T11:00:00Z"
}
```

**失败响应 (500 Internal Server Error):**
```json
{
  "success": false,
  "error_code": "COMPENSATION_FAILED",
  "error_message": "用户数据已被其他事务修改，无法执行补偿",
  "timestamp": "2024-01-15T11:00:00Z"
}
```

## 实现示例

### Go 语言示例

```go
package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "time"
)

// 补偿上下文数据结构
type CompensationContext struct {
    UserID         string  `json:"user_id"`
    OrderID        string  `json:"order_id"`
    Amount         float64 `json:"amount"`
    OperationTime  string  `json:"operation_time"`
}

// 补偿响应结构
type CompensationResponse struct {
    Success       bool   `json:"success"`
    Message       string `json:"message"`
    CompensationID string `json:"compensation_id,omitempty"`
    Timestamp     string `json:"timestamp"`
}

// 生成补偿ID (示例)
func generateCompensationID() string {
    return "comp-" + time.Now().Format("20060102150405") + "-" + fmt.Sprintf("%d", time.Now().UnixNano()/1000000)
}

// 用户删除补偿接口
func userDeleteCompensationHandler(w http.ResponseWriter, r *http.Request) {
    // 1. 验证请求方法
    if r.Method != http.MethodPost {
        http.Error(w, "只支持 POST 方法", http.StatusMethodNotAllowed)
        return
    }

    // 2. 获取 Saga 头部信息
    sagaId := r.Header.Get("X-Saga-Id")
    stepId := r.Header.Get("X-Step-Id")
    stepIndex := r.Header.Get("X-Step-Index")
    serviceName := r.Header.Get("X-Service-Name")
    action := r.Header.Get("X-Action")

    log.Printf("收到补偿请求: SagaId=%s, StepId=%s, StepIndex=%s, Service=%s, Action=%s",
        sagaId, stepId, stepIndex, serviceName, action)

    // 3. 幂等性验证（推荐使用 X-Step-Id）
    if stepId == "" {
        log.Printf("缺少步骤ID，无法进行幂等性验证")
        http.Error(w, "请求头缺少 X-Step-Id", http.StatusBadRequest)
        return
    }

    // 检查是否已经处理过该步骤
    if isCompensationAlreadyProcessed(stepId) {
        log.Printf("补偿操作已经处理过: StepId=%s", stepId)
        // 返回成功响应，因为补偿已经完成
        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusOK)
        json.NewEncoder(w).Encode(CompensationResponse{
            Success:        true,
            Message:        "补偿操作已经完成（幂等性检查）",
            CompensationID: getExistingCompensationID(stepId),
            Timestamp:      time.Now().Format(time.RFC3339),
        })
        return
    }

    // 4. 解析补偿上下文
    var context CompensationContext
    if err := json.NewDecoder(r.Body).Decode(&context); err != nil {
        log.Printf("解析补偿上下文失败: %v", err)
        http.Error(w, "无效的请求数据", http.StatusBadRequest)
        return
    }

    // 5. 执行补偿逻辑
    err := executeUserDeletionCompensation(context)
    if err != nil {
        log.Printf("补偿执行失败: %v", err)
        
        // 返回失败响应
        w.Header().Set("Content-Type", "application/json")
        w.WriteHeader(http.StatusInternalServerError)
        json.NewEncoder(w).Encode(CompensationResponse{
            Success:   false,
            Message:   fmt.Sprintf("补偿失败: %v", err),
            Timestamp: time.Now().Format(time.RFC3339),
        })
        return
    }

    // 6. 记录补偿完成状态（用于幂等性检查）
    recordCompensationCompleted(stepId, generateCompensationID())

    // 7. 返回成功响应
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(CompensationResponse{
        Success:        true,
        Message:        "用户删除补偿完成",
        CompensationID: generateCompensationID(),
        Timestamp:      time.Now().Format(time.RFC3339),
    })
}

// 执行用户删除补偿逻辑
func executeUserDeletionCompensation(context CompensationContext) error {
    // 实现具体的补偿逻辑
    // 例如：删除已创建的用户、回滚数据库操作等
    
    log.Printf("执行用户删除补偿: UserID=%s, OrderID=%s", 
        context.UserID, context.OrderID)
    
    // 这里实现实际的补偿逻辑
    // 如果补偿失败，返回 error
    // 如果补偿成功，返回 nil
    
    return nil
}

// 幂等性验证相关函数（示例实现）
var compensationCache = make(map[string]string) // 实际项目中应使用数据库或 Redis

// 检查补偿是否已经处理过
func isCompensationAlreadyProcessed(stepId string) bool {
    _, exists := compensationCache[stepId]
    return exists
}

// 记录补偿完成状态
func recordCompensationCompleted(stepId, compensationId string) {
    compensationCache[stepId] = compensationId
    log.Printf("记录补偿完成: StepId=%s, CompensationId=%s", stepId, compensationId)
}

// 获取已存在的补偿ID
func getExistingCompensationID(stepId string) string {
    if id, exists := compensationCache[stepId]; exists {
        return id
    }
    return ""
}

func main() {
    http.HandleFunc("/saga/compensate/delete_user", userDeleteCompensationHandler)
    log.Println("补偿服务启动在端口 8080")
    log.Fatal(http.ListenAndServe(":8080", nil))
}
```

## 最佳实践

### 1. 接口设计原则

- **幂等性**: 补偿接口必须支持重复调用，多次调用应产生相同的结果
  - **推荐使用 `X-Step-Id` 进行幂等性验证**: 该头部包含步骤的唯一标识，接入方可以将其作为幂等性键值，确保相同步骤的补偿操作不会重复执行
- **原子性**: 补偿操作应该是原子的，要么全部成功，要么全部失败
- **快速响应**: 补偿接口应尽快完成，避免长时间阻塞

### 2. 幂等性实现建议

```go
// 推荐的幂等性实现模式
func compensationHandler(w http.ResponseWriter, r *http.Request) {
    stepId := r.Header.Get("X-Step-Id")
    
    // 1. 检查幂等性
    if result, exists := getCompensationResult(stepId); exists {
        // 返回已缓存的结果
        writeResponse(w, result)
        return
    }
    
    // 2. 执行补偿逻辑
    result, err := executeCompensation(r)
    if err != nil {
        writeErrorResponse(w, err)
        return
    }
    
    // 3. 缓存结果
    cacheCompensationResult(stepId, result)
    writeResponse(w, result)
}

// 生产环境推荐使用 Redis 进行幂等性管理
func cacheCompensationResult(stepId string, result interface{}) {
    // Redis 示例：
    // rdb.Set(ctx, "compensation:"+stepId, result, 24*time.Hour)
}
```

### 3. 错误处理

```go
// 推荐的错误处理模式
func compensationHandler(w http.ResponseWriter, r *http.Request) {
    // 业务逻辑检查
    if err := validateCompensationRequest(r); err != nil {
        http.Error(w, "请求验证失败", http.StatusBadRequest)
        return
    }
    
    // 补偿操作执行
    if err := executeCompensation(r); err != nil {
        if isRetryableError(err) {
            // 可重试错误，返回 5xx
            http.Error(w, err.Error(), http.StatusInternalServerError)
        } else {
            // 不可重试错误，返回 4xx
            http.Error(w, err.Error(), http.StatusUnprocessableEntity)
        }
        return
    }
    
    // 成功响应
    w.WriteHeader(http.StatusOK)
}
```

### 4. 日志记录

建议记录以下关键信息：

```go
log.Printf("补偿请求开始: SagaId=%s, StepId=%s, Action=%s, Context=%s",
    sagaId, stepId, action, string(contextJson))

log.Printf("补偿执行完成: SagaId=%s, StepId=%s, 耗时=%dms, 结果=%s",
    sagaId, stepId, duration.Milliseconds(), result)
```

### 5. 监控和指标

建议监控以下指标：

- 补偿接口的成功率
- 补偿接口的响应时间
- 补偿失败的错误类型分布
- 补偿重试次数统计
- 幂等性命中率（重复请求比例）

## 状态码规范

| 状态码范围 | 含义 | Saga 行为 | 使用场景 |
|-----------|------|-----------|----------|
| `200-299` | 补偿成功 | 标记步骤为已补偿 | 补偿操作正常完成 |
| `400-499` | 客户端错误 | 标记步骤补偿失败 | 请求数据无效、业务规则不满足 |
| `500-599` | 服务器错误 | 标记步骤补偿失败 | 系统异常、数据库错误、网络问题 |

## 常见问题

### Q1: 如果补偿操作本身失败了怎么办？
A1: 返回适当的HTTP错误状态码（4xx或5xx），Saga 服务会将该步骤标记为补偿失败。管理员可以通过监控系统发现失败的补偿操作并进行人工干预。

### Q2: 如何实现补偿接口的幂等性？
A2: 推荐使用 `X-Step-Id` 进行幂等性验证：
- **存储机制**: 将已处理的 `X-Step-Id` 存储在数据库、Redis 或内存中
- **检查逻辑**: 在执行补偿前检查该 `X-Step-Id` 是否已处理过
- **响应一致性**: 重复请求应返回相同的结果（包括补偿ID）
- **存储策略**: 建议设置合理的过期时间，避免存储空间无限增长

### Q3: 补偿接口需要验证调用方身份吗？
A3: 建议实现适当的安全验证，可以通过以下方式：
- 验证请求来源IP
- 使用共享密钥进行签名验证
- 通过服务网格的安全策略

### Q4: 补偿操作的上下文数据如何确定？
A4: 上下文数据在业务服务上报补偿信息时确定，应包含执行补偿操作所需的最小必要信息。

### Q5: 补偿接口的超时时间是多少？
A5: 默认超时时间由 Saga 服务配置决定，建议补偿操作在 30 秒内完成。

### Q6: 如何测试补偿接口？
A6: 可以使用以下工具进行测试：

```bash
# 使用 curl 测试补偿接口
curl -X POST http://your-service/saga/compensate/delete_user \
  -H "Content-Type: application/json" \
  -H "X-Saga-Id: test-saga-123" \
  -H "X-Step-Id: test-step-456" \
  -H "X-Step-Index: 1" \
  -H "X-Service-Name: user-service" \
  -H "X-Action: create_user" \
  -d '{
    "user_id": "12345",
    "operation_time": "2024-01-15T10:30:00Z"
  }'
```
