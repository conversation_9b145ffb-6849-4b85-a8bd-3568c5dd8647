package saga_transactions

import (
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"

	v2 "saga/api/saga_transactions/v2"
	"saga/internal/consts"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
)

// TestControllerV2_CreateSagaTransaction 测试V2控制器创建事务
func TestControllerV2_CreateSagaTransaction(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		controller := NewV2()
		t.AssertNE(controller, nil)

		ctx := context.Background()
		req := &v2.CreateSagaTransactionReq{
			Name:          "V2测试事务",
			StepIndexMode: consts.StepIndexModeAuto,
		}

		res, err := controller.CreateSagaTransaction(ctx, req)
		t.AssertNil(err)
		t.AssertNE(res, nil)
		t.AssertNE(res.SagaId, "")
		t.<PERSON>ser<PERSON>(res.Name, "V2测试事务")
		t.<PERSON><PERSON><PERSON>(res.Status, consts.SagaStatusPending)
	})
}

// TestControllerV2_GetLockStrategy 测试获取锁策略信息
func TestControllerV2_GetLockStrategy(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		controller := NewV2()
		t.AssertNE(controller, nil)

		ctx := context.Background()
		req := &v2.GetLockStrategyReq{}

		res, err := controller.GetLockStrategy(ctx, req)
		t.AssertNil(err)
		t.AssertNE(res, nil)
		t.AssertNE(res.StrategyName, "")

		// 策略名称应该是 PessimisticLock 或 OptimisticLock
		t.Assert(res.StrategyName == "PessimisticLock" || res.StrategyName == "OptimisticLock", true)
	})
}

// TestControllerV2_GetServiceHealth 测试获取服务健康状态
func TestControllerV2_GetServiceHealth(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		controller := NewV2()
		t.AssertNE(controller, nil)

		ctx := context.Background()
		req := &v2.GetServiceHealthReq{}

		res, err := controller.GetServiceHealth(ctx, req)
		t.AssertNil(err)
		t.AssertNE(res, nil)
		t.Assert(res.Status, "healthy")
		t.Assert(res.Version, "v2.0.0")
		t.AssertNE(res.LockStrategy, "")
		t.AssertNE(res.Configuration, nil)
		t.AssertNE(res.Uptime, "")

		// 验证配置信息结构
		config := res.Configuration
		t.AssertNE(config["optimistic_lock"], nil)
		t.AssertNE(config["compensation"], nil)
		t.AssertNE(config["retry"], nil)
		t.AssertNE(config["current_lock_strategy"], nil)
	})
}

// TestControllerV2_ReportCompensation 测试上报补偿信息
func TestControllerV2_ReportCompensation(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 跳过集成测试，除非明确启用
		if !isIntegrationTestEnabled() {
			t.Skip("集成测试已跳过，设置环境变量启用")
			return
		}

		controller := NewV2()
		ctx := context.Background()

		// 先创建一个事务
		createReq := &v2.CreateSagaTransactionReq{
			Name:          "V2补偿测试事务",
			StepIndexMode: consts.StepIndexModeAuto,
		}

		createRes, err := controller.CreateSagaTransaction(ctx, createReq)
		t.AssertNil(err)
		sagaId := createRes.SagaId

		// 上报补偿信息
		reportReq := &v2.ReportCompensationReq{
			SagaId:              sagaId,
			Action:              "test_action_v2",
			ServiceName:         "test_service_v2",
			ContextData:         `{"test": "data"}`,
			CompensationContext: `{"compensation": "data"}`,
			CompensateEndpoint:  "http://localhost:8080/test/compensate",
		}

		reportRes, err := controller.ReportCompensation(ctx, reportReq)
		t.AssertNil(err)
		t.AssertNE(reportRes, nil)
		t.Assert(reportRes.Success, true)
	})
}

// TestControllerV2_FullWorkflow 测试完整的工作流程
func TestControllerV2_FullWorkflow(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 跳过集成测试，除非明确启用
		if !isIntegrationTestEnabled() {
			t.Skip("集成测试已跳过，设置环境变量启用")
			return
		}

		controller := NewV2()
		ctx := context.Background()

		// 1. 创建事务
		createReq := &v2.CreateSagaTransactionReq{
			Name:          "V2完整流程测试",
			StepIndexMode: consts.StepIndexModeAuto,
		}

		createRes, err := controller.CreateSagaTransaction(ctx, createReq)
		t.AssertNil(err)
		sagaId := createRes.SagaId

		// 2. 获取事务信息
		getReq := &v2.GetSagaTransactionReq{
			SagaId: sagaId,
		}

		getRes, err := controller.GetSagaTransaction(ctx, getReq)
		t.AssertNil(err)
		t.Assert(getRes.SagaId, sagaId)
		t.Assert(getRes.Status, consts.SagaStatusPending)

		// 3. 上报补偿信息
		reportReq := &v2.ReportCompensationReq{
			SagaId:              sagaId,
			Action:              "workflow_action",
			ServiceName:         "workflow_service",
			ContextData:         `{"workflow": "test"}`,
			CompensationContext: `{"workflow": "compensation"}`,
			CompensateEndpoint:  "http://localhost:8080/workflow/compensate",
		}

		reportRes, err := controller.ReportCompensation(ctx, reportReq)
		t.AssertNil(err)
		t.Assert(reportRes.Success, true)

		// 4. 提交事务
		commitReq := &v2.CommitSagaTransactionReq{
			SagaId: sagaId,
		}

		commitRes, err := controller.CommitSagaTransaction(ctx, commitReq)
		t.AssertNil(err)
		t.Assert(commitRes.Success, true)
		t.Assert(commitRes.SagaId, sagaId)
		t.Assert(commitRes.NewStatus, consts.SagaStatusCompleted)
	})
}

// createAndTestTransaction 辅助函数：创建并测试事务
func createAndTestTransaction(t *gtest.T, controller *ControllerV2, ctx context.Context, name string) string {
	// 创建事务
	createReq := &v2.CreateSagaTransactionReq{
		Name:          name,
		StepIndexMode: consts.StepIndexModeAuto,
	}

	createRes, err := controller.CreateSagaTransaction(ctx, createReq)
	t.AssertNil(err)
	sagaId := createRes.SagaId

	// 上报补偿信息
	reportReq := &v2.ReportCompensationReq{
		SagaId:              sagaId,
		Action:              "test_action",
		ServiceName:         "test_service",
		ContextData:         `{"test": "data"}`,
		CompensationContext: `{"test": "compensation"}`,
		CompensateEndpoint:  "http://localhost:8080/test/compensate",
	}

	reportRes, err := controller.ReportCompensation(ctx, reportReq)
	t.AssertNil(err)
	t.Assert(reportRes.Success, true)

	return sagaId
}

// isIntegrationTestEnabled 检查是否启用集成测试
func isIntegrationTestEnabled() bool {
	// 可以通过环境变量或配置文件控制
	// 这里简化为总是返回false，避免在单元测试中执行数据库操作
	return false
}
