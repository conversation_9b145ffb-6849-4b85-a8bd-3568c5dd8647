package saga_transactions

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"

	v2 "saga/api/saga_transactions/v2"
	"saga/internal/consts"
	"saga/internal/model"
	"saga/internal/service"
)

// ControllerV2 V2版本的控制器，使用重构后的服务
type ControllerV2 struct {
	serviceManager *service.SagaServiceManager
	startTime      time.Time
}

// NewV2 创建V2控制器实例
func NewV2() *ControllerV2 {
	return &ControllerV2{
		serviceManager: service.NewSagaServiceManager(),
		startTime:      time.Now(),
	}
}

// CreateSagaTransaction 创建分布式事务
func (c *ControllerV2) CreateSagaTransaction(ctx context.Context, req *v2.CreateSagaTransactionReq) (res *v2.CreateSagaTransactionRes, err error) {
	sagaService := c.serviceManager.GetService()

	g.Log().Infof(ctx, "V2 创建分布式事务: Name=%s, StepIndexMode=%s, 锁策略=%s",
		req.Name, req.StepIndexMode, sagaService.GetLockStrategy().GetStrategyName())

	// 构建 Service 输入参数
	input := &model.CreateSagaTransactionInput{
		Name:          req.Name,
		StepIndexMode: req.StepIndexMode,
	}

	// 转换步骤模板
	if len(req.StepTemplates) > 0 {
		for _, template := range req.StepTemplates {
			input.StepTemplates = append(input.StepTemplates, model.SagaStepTemplate{
				StepIndex:   template.StepIndex,
				Service:     template.Service,
				Action:      template.Action,
				Description: template.Description,
			})
		}
	}

	// 调用 Service 层
	output, err := sagaService.CreateSagaTransaction(ctx, input)
	if err != nil {
		g.Log().Errorf(ctx, "V2 创建分布式事务失败: %v", err)
		return nil, err
	}

	// 构建响应
	res = &v2.CreateSagaTransactionRes{
		SagaId:    output.SagaId,
		Name:      output.Name,
		Status:    output.Status,
		CreatedAt: output.CreatedAt,
	}

	g.Log().Infof(ctx, "V2 创建分布式事务成功: SagaId=%s", output.SagaId)
	return res, nil
}

// ReportCompensation 上报补偿操作结果
func (c *ControllerV2) ReportCompensation(ctx context.Context, req *v2.ReportCompensationReq) (res *v2.ReportCompensationRes, err error) {
	sagaService := c.serviceManager.GetService()

	g.Log().Infof(ctx, "V2 上报补偿信息: SagaId=%s, Action=%s, Service=%s, 锁策略=%s",
		req.SagaId, req.Action, req.ServiceName, sagaService.GetLockStrategy().GetStrategyName())

	// 构建 Service 输入参数
	input := &model.ReportCompensationInput{
		SagaId:              req.SagaId,
		Action:              req.Action,
		ServiceName:         req.ServiceName,
		ContextData:         req.ContextData,
		CompensationContext: req.CompensationContext,
		CompensateEndpoint:  req.CompensateEndpoint,
	}

	// 调用 Service 层（使用V2服务，自动根据配置选择锁策略）
	output, err := sagaService.ReportCompensation(ctx, input)
	if err != nil {
		g.Log().Errorf(ctx, "V2 上报补偿信息失败: SagaId=%s, Error=%v", req.SagaId, err)
		return nil, err
	}

	// 构建响应
	res = &v2.ReportCompensationRes{
		Success: output.Success,
	}

	g.Log().Infof(ctx, "V2 上报补偿信息成功: SagaId=%s, Message=%s", req.SagaId, output.Message)
	return res, nil
}

// GetSagaTransaction 获取Saga事务信息
func (c *ControllerV2) GetSagaTransaction(ctx context.Context, req *v2.GetSagaTransactionReq) (res *v2.GetSagaTransactionRes, err error) {
	sagaService := c.serviceManager.GetService()

	g.Log().Debugf(ctx, "V2 获取Saga事务信息: SagaId=%s", req.SagaId)

	// 构建 Service 输入参数
	input := &model.GetSagaTransactionInput{
		SagaId: req.SagaId,
	}

	// 调用 Service 层
	output, err := sagaService.GetSagaTransaction(ctx, input)
	if err != nil {
		g.Log().Errorf(ctx, "V2 获取Saga事务信息失败: SagaId=%s, Error=%v", req.SagaId, err)
		return nil, err
	}

	// 构建响应
	res = &v2.GetSagaTransactionRes{
		SagaId:      output.SagaId,
		Name:        output.Name,
		Status:      output.Status,
		CurrentStep: output.CurrentStep,
		RetryCount:  output.RetryCount,
		CreatedAt:   output.CreatedAt,
		UpdatedAt:   output.UpdatedAt,
	}

	return res, nil
}

// CommitSagaTransaction 提交分布式事务
func (c *ControllerV2) CommitSagaTransaction(ctx context.Context, req *v2.CommitSagaTransactionReq) (res *v2.CommitSagaTransactionRes, err error) {
	sagaService := c.serviceManager.GetService()

	g.Log().Infof(ctx, "V2 提交分布式事务: SagaId=%s, 锁策略=%s",
		req.SagaId, sagaService.GetLockStrategy().GetStrategyName())

	// 构建 Service 输入参数
	input := &model.CommitSagaTransactionInput{
		SagaId: req.SagaId,
	}

	// 调用 Service 层
	output, err := sagaService.CommitSagaTransaction(ctx, input)
	if err != nil {
		g.Log().Errorf(ctx, "V2 提交分布式事务失败: SagaId=%s, Error=%v", req.SagaId, err)
		return nil, err
	}

	// 构建响应
	res = &v2.CommitSagaTransactionRes{
		Success:        output.Success,
		Message:        output.Message,
		SagaId:         output.SagaId,
		CompletedSteps: output.CompletedSteps,
		ExpectedSteps:  output.ExpectedSteps,
		NewStatus:      output.NewStatus,
		CompletedAt:    output.CompletedAt,
	}

	g.Log().Infof(ctx, "V2 提交分布式事务结果: SagaId=%s, Success=%v, Message=%s",
		req.SagaId, output.Success, output.Message)
	return res, nil
}

// RollBackSagaTransaction 回滚分布式事务
func (c *ControllerV2) RollBackSagaTransaction(ctx context.Context, req *v2.RollBackSagaTransactionReq) (res *v2.RollBackSagaTransactionRes, err error) {
	sagaService := c.serviceManager.GetService()

	g.Log().Infof(ctx, "V2 回滚分布式事务: SagaId=%s, FailReason=%s, ExecutionMode=%s, 锁策略=%s",
		req.SagaId, req.FailReason, req.ExecutionMode, sagaService.GetLockStrategy().GetStrategyName())

	// 构建 Service 输入参数
	input := &model.RollbackSagaTransactionInput{
		SagaId:        req.SagaId,
		FailReason:    req.FailReason,
		FailedStep:    req.FailedStep,
		ExecutionMode: req.ExecutionMode,
	}

	if input.ExecutionMode == "" {
		input.ExecutionMode = consts.CompensationExecutionModeNone // 默认不执行
	}

	// 调用 Service 层
	output, err := sagaService.RollbackSagaTransaction(ctx, input)
	if err != nil {
		g.Log().Errorf(ctx, "V2 回滚分布式事务失败: SagaId=%s, Error=%v", req.SagaId, err)
		return nil, err
	}

	// 构建响应
	res = &v2.RollBackSagaTransactionRes{
		Success:             output.Success,
		Message:             output.Message,
		SagaId:              output.SagaId,
		NewStatus:           output.NewStatus,
		IsRollbackCompleted: output.IsRollbackCompleted,
		StartedAt:           output.StartedAt,
		CompletedAt:         output.CompletedAt,
		FailReason:          output.FailReason,
	}

	g.Log().Infof(ctx, "V2 回滚分布式事务结果: SagaId=%s, Success=%v, IsCompleted=%v",
		req.SagaId, output.Success, output.IsRollbackCompleted)
	return res, nil
}

// GetLockStrategy 获取当前锁策略信息
func (c *ControllerV2) GetLockStrategy(ctx context.Context, req *v2.GetLockStrategyReq) (res *v2.GetLockStrategyRes, err error) {
	sagaService := c.serviceManager.GetService()

	g.Log().Debugf(ctx, "V2 获取锁策略信息")

	// 获取锁策略信息
	info := sagaService.GetLockStrategyInfo()

	// 构建响应
	res = &v2.GetLockStrategyRes{
		StrategyName: fmt.Sprintf("%v", info["strategy_name"]),
		IsOptimistic: fmt.Sprintf("%v", info["is_optimistic"]) == "true",
	}

	// 如果是乐观锁，添加额外信息
	if res.IsOptimistic {
		if maxRetries, ok := info["max_retries"]; ok {
			res.MaxRetries = int(maxRetries.(int))
		}
		if baseDelay, ok := info["base_delay"]; ok {
			res.BaseDelay = fmt.Sprintf("%v", baseDelay)
		}
		if maxDelay, ok := info["max_delay"]; ok {
			res.MaxDelay = fmt.Sprintf("%v", maxDelay)
		}
		if backoffMultiplier, ok := info["backoff_multiplier"]; ok {
			res.BackoffMultiplier = backoffMultiplier.(float64)
		}
	}

	g.Log().Infof(ctx, "V2 当前锁策略: %s (乐观锁: %v)", res.StrategyName, res.IsOptimistic)
	return res, nil
}

// GetServiceHealth 获取服务健康状态
func (c *ControllerV2) GetServiceHealth(ctx context.Context, req *v2.GetServiceHealthReq) (res *v2.GetServiceHealthRes, err error) {
	sagaService := c.serviceManager.GetService()

	g.Log().Debugf(ctx, "V2 获取服务健康状态")

	// 获取当前配置
	config := c.serviceManager.GetCurrentConfig()
	lockInfo := sagaService.GetLockStrategyInfo()

	// 计算运行时间
	uptime := time.Since(c.startTime)

	// 构建配置信息
	configuration := map[string]interface{}{
		"optimistic_lock": map[string]interface{}{
			"enabled":            config.OptimisticLock.Enabled,
			"max_retries":        config.OptimisticLock.MaxRetries,
			"base_delay":         config.OptimisticLock.BaseDelay.String(),
			"max_delay":          config.OptimisticLock.MaxDelay.String(),
			"backoff_multiplier": config.OptimisticLock.BackoffMultiplier,
		},
		"compensation": map[string]interface{}{
			"timeout":            config.Compensation.Timeout.String(),
			"default_window_sec": config.Compensation.DefaultWindowSec,
			"report_max_retries": config.Compensation.ReportMaxRetries,
			"report_retry_delay": config.Compensation.ReportRetryDelay.String(),
		},
		"retry": map[string]interface{}{
			"max_retries":      config.Retry.MaxRetries,
			"initial_interval": config.Retry.InitialInterval.String(),
			"max_interval":     config.Retry.MaxInterval.String(),
			"multiplier":       config.Retry.Multiplier,
		},
		"current_lock_strategy": lockInfo,
	}

	// 构建响应
	res = &v2.GetServiceHealthRes{
		Status:        "healthy",
		Version:       "v2.0.0",
		LockStrategy:  fmt.Sprintf("%v", lockInfo["strategy_name"]),
		Configuration: configuration,
		Uptime:        uptime.String(),
	}

	return res, nil
}
