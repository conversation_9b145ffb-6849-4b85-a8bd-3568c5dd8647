package cmd

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"

	"saga/internal/controller/hello"
	"saga/internal/controller/saga_transactions"
	"saga/internal/service"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			// 读取补偿处理服务配置
			processingEnabled := g.Cfg().MustGetWithEnv(ctx, "saga.compensation.processing.enabled", true).Bool()

			if processingEnabled {
				// 读取具体配置参数
				taskTimeout := g.Cfg().MustGetWithEnv(ctx, "saga.compensation.processing.taskTimeout", "5m").Duration()
				processingInterval := g.Cfg().MustGetWithEnv(ctx, "saga.compensation.processing.processingInterval", "30s").Duration()
				maxConcurrentTasks := g.Cfg().MustGetWithEnv(ctx, "saga.compensation.processing.maxConcurrentTasks", 10).Int()

				// 初始化补偿处理服务
				processorService := service.NewCompensationProcessorService()

				// 设置配置
				processorService.SetConfig(taskTimeout, processingInterval, maxConcurrentTasks)

				// 启动补偿处理服务
				if err := processorService.StartProcessing(ctx); err != nil {
					g.Log().Errorf(ctx, "启动补偿处理服务失败: %v", err)
					return err
				}

				g.Log().Infof(ctx, "补偿处理服务已启动 (超时=%v, 间隔=%v, 并发=%d)",
					taskTimeout, processingInterval, maxConcurrentTasks)
			} else {
				g.Log().Infof(ctx, "补偿处理服务已禁用")
			}

			s := g.Server()

			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(ghttp.MiddlewareHandlerResponse)
				group.Bind(
					hello.NewV1(),
					saga_transactions.NewV1(),
				)
			})

			// V2 API 路由组
			s.Group("/v2", func(group *ghttp.RouterGroup) {
				group.Middleware(ghttp.MiddlewareHandlerResponse)
				group.Bind(
					saga_transactions.NewV2(),
				)
			})
			s.Run()
			return nil
		},
	}
)
