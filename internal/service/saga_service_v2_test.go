package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/test/gtest"

	"saga/internal/consts"
	"saga/internal/model"
	"saga/internal/model/entity"
)

// TestLockStrategyFactory 测试锁策略工厂
func TestLockStrategyFactory(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := &LockStrategyFactory{}

		// 测试创建悲观锁策略
		pessimisticStrategy := factory.CreateLockStrategy(false, OptimisticLockConfig{})
		t.<PERSON>sert(pessimisticStrategy.GetStrategyName(), "PessimisticLock")
		t.<PERSON>sert(pessimisticStrategy.IsOptimistic(), false)

		// 测试创建乐观锁策略
		optimisticConfig := OptimisticLockConfig{
			MaxRetries:        3,
			BaseDelay:         10 * time.Millisecond,
			MaxDelay:          100 * time.Millisecond,
			BackoffMultiplier: 2.0,
		}
		optimisticStrategy := factory.CreateLockStrategy(true, optimisticConfig)
		t.<PERSON>ser<PERSON>(optimisticStrategy.GetStrategyName(), "OptimisticLock")
		t.Assert(optimisticStrategy.IsOptimistic(), true)
	})
}

// TestSagaServiceFactory 测试服务工厂
func TestSagaServiceFactory(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		factory := NewSagaServiceFactory()
		t.AssertNE(factory, nil)
		t.AssertNE(factory.GetConfig(), nil)

		// 测试创建服务
		service := factory.CreateService()
		t.AssertNE(service, nil)
		t.AssertNE(service.GetLockStrategy(), nil)

		// 测试获取锁策略信息
		info := service.GetLockStrategyInfo()
		t.AssertNE(info["strategy_name"], "")
		t.AssertNE(info["is_optimistic"], nil)
	})
}

// TestSagaServiceManager 测试服务管理器
func TestSagaServiceManager(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		manager := NewSagaServiceManager()
		t.AssertNE(manager, nil)

		service := manager.GetService()
		t.AssertNE(service, nil)

		// 测试切换锁策略
		originalStrategy := service.GetLockStrategy().GetStrategyName()

		// 切换到乐观锁
		optimisticConfig := &OptimisticLockConfig{
			MaxRetries:        5,
			BaseDelay:         5 * time.Millisecond,
			MaxDelay:          200 * time.Millisecond,
			BackoffMultiplier: 2.0,
		}
		manager.SwitchLockStrategy(true, optimisticConfig)
		t.Assert(service.GetLockStrategy().GetStrategyName(), "OptimisticLock")
		t.Assert(service.GetLockStrategy().IsOptimistic(), true)

		// 切换回悲观锁
		manager.SwitchLockStrategy(false, nil)
		t.Assert(service.GetLockStrategy().GetStrategyName(), "PessimisticLock")
		t.Assert(service.GetLockStrategy().IsOptimistic(), false)

		// 验证策略确实发生了变化
		if originalStrategy == "OptimisticLock" {
			t.Assert(service.GetLockStrategy().GetStrategyName(), "PessimisticLock")
		} else {
			// 如果原来是悲观锁，现在应该还是悲观锁
			t.Assert(service.GetLockStrategy().GetStrategyName(), "PessimisticLock")
		}
	})
}

// TestOptimisticLockRetryDelay 测试乐观锁重试延迟计算
func TestOptimisticLockRetryDelay(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		config := OptimisticLockConfig{
			MaxRetries:        3,
			BaseDelay:         10 * time.Millisecond,
			MaxDelay:          100 * time.Millisecond,
			BackoffMultiplier: 2.0,
		}

		strategy := NewOptimisticLockStrategy(config).(*OptimisticLockStrategy)

		// 测试延迟计算
		delay0 := strategy.calculateRetryDelay(0)
		delay1 := strategy.calculateRetryDelay(1)
		delay2 := strategy.calculateRetryDelay(2)
		delay3 := strategy.calculateRetryDelay(3)

		t.Assert(delay0, 10*time.Millisecond) // 10ms * 2^0 = 10ms
		t.Assert(delay1, 20*time.Millisecond) // 10ms * 2^1 = 20ms
		t.Assert(delay2, 40*time.Millisecond) // 10ms * 2^2 = 40ms
		t.Assert(delay3, 80*time.Millisecond) // 10ms * 2^3 = 80ms

		// 测试最大延迟限制
		delay10 := strategy.calculateRetryDelay(10)
		t.Assert(delay10, 100*time.Millisecond) // 应该被限制在maxDelay
	})
}

// TestVersionConflictDetection 测试版本冲突检测
func TestVersionConflictDetection(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		config := OptimisticLockConfig{
			MaxRetries:        3,
			BaseDelay:         10 * time.Millisecond,
			MaxDelay:          100 * time.Millisecond,
			BackoffMultiplier: 2.0,
		}

		strategy := NewOptimisticLockStrategy(config).(*OptimisticLockStrategy)

		// 测试版本冲突错误检测
		t.Assert(strategy.isVersionConflictError(nil), false)
		t.Assert(strategy.isVersionConflictError(errors.New("some other error")), false)
		t.Assert(strategy.isVersionConflictError(errors.New("version_conflict: test")), true)
		t.Assert(strategy.isVersionConflictError(errors.New("乐观锁版本冲突")), true)
	})
}

// MockSagaOperation 模拟业务操作
type MockSagaOperation struct {
	name                string
	requiresTransaction bool
	executeFunc         func(ctx context.Context, saga *entity.SagaTransactions, tx gdb.TX) error
}

func (m *MockSagaOperation) GetOperationName() string {
	return m.name
}

func (m *MockSagaOperation) RequiresTransaction() bool {
	return m.requiresTransaction
}

func (m *MockSagaOperation) Execute(ctx context.Context, saga *entity.SagaTransactions, tx gdb.TX) error {
	if m.executeFunc != nil {
		return m.executeFunc(ctx, saga, tx)
	}
	return nil
}

// BenchmarkLockStrategies 性能基准测试
func BenchmarkLockStrategies(b *testing.B) {
	// 这里可以添加性能基准测试
	// 比较悲观锁和乐观锁在不同并发场景下的性能

	b.Run("PessimisticLock", func(b *testing.B) {
		strategy := NewPessimisticLockStrategy(g.DB())
		operation := &MockSagaOperation{
			name:                "BenchmarkOperation",
			requiresTransaction: false,
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			ctx := context.Background()
			_ = strategy.ExecuteWithLock(ctx, "test-saga-id", operation)
		}
	})

	b.Run("OptimisticLock", func(b *testing.B) {
		config := OptimisticLockConfig{
			MaxRetries:        3,
			BaseDelay:         1 * time.Millisecond,
			MaxDelay:          10 * time.Millisecond,
			BackoffMultiplier: 2.0,
		}
		strategy := NewOptimisticLockStrategy(config)
		operation := &MockSagaOperation{
			name:                "BenchmarkOperation",
			requiresTransaction: false,
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			ctx := context.Background()
			_ = strategy.ExecuteWithLock(ctx, "test-saga-id", operation)
		}
	})
}

// TestServiceV2Integration 集成测试（需要数据库连接）
func TestServiceV2Integration(t *testing.T) {
	// 这个测试需要真实的数据库连接，可以在集成测试环境中运行
	gtest.C(t, func(t *gtest.T) {
		// 跳过集成测试，除非明确启用
		if !g.Cfg().MustGet(context.Background(), "test.integration.enabled", true).Bool() {
			t.Skip("集成测试已跳过，设置 test.integration.enabled=true 启用")
			return
		}

		manager := NewSagaServiceManager()
		service := manager.GetService()

		ctx := context.Background()

		// 测试创建事务
		createInput := &model.CreateSagaTransactionInput{
			Name:                  "测试事务V2",
			StepIndexMode:         consts.StepIndexModeAuto,
			CompensationWindowSec: 300,
		}

		createOutput, err := service.CreateSagaTransaction(ctx, createInput)
		t.AssertNil(err)
		t.AssertNE(createOutput, nil)
		t.AssertNE(createOutput.SagaId, "")

		sagaId := createOutput.SagaId

		// 测试获取事务
		getInput := &model.GetSagaTransactionInput{
			SagaId: sagaId,
		}

		getOutput, err := service.GetSagaTransaction(ctx, getInput)
		t.AssertNil(err)
		t.AssertNE(getOutput, nil)
		t.Assert(getOutput.SagaId, sagaId)

		// 测试上报补偿信息
		reportInput := &model.ReportCompensationInput{
			SagaId:              sagaId,
			Action:              "test_action",
			ServiceName:         "test_service",
			ContextData:         `{"test": "data"}`,
			CompensationContext: `{"compensation": "data"}`,
			CompensateEndpoint:  "http://localhost:8080/compensate",
		}

		reportOutput, err := service.ReportCompensation(ctx, reportInput)
		t.AssertNil(err)
		t.AssertNE(reportOutput, nil)
		t.Assert(reportOutput.Success, true)

		// 测试切换锁策略后再次上报
		originalStrategy := service.GetLockStrategy().GetStrategyName()

		// 切换锁策略
		if originalStrategy == "PessimisticLock" {
			optimisticConfig := OptimisticLockConfig{
				MaxRetries:        3,
				BaseDelay:         5 * time.Millisecond,
				MaxDelay:          50 * time.Millisecond,
				BackoffMultiplier: 2.0,
			}
			service.SwitchToOptimisticLock(optimisticConfig)
		} else {
			service.SwitchToPessimisticLock()
		}

		// 再次上报，验证锁策略切换后功能正常
		reportInput2 := &model.ReportCompensationInput{
			SagaId:              sagaId,
			Action:              "test_action_2",
			ServiceName:         "test_service_2",
			ContextData:         `{"test": "data2"}`,
			CompensationContext: `{"compensation": "data2"}`,
			CompensateEndpoint:  "http://localhost:8080/compensate2",
		}

		reportOutput2, err := service.ReportCompensation(ctx, reportInput2)
		t.AssertNil(err)
		t.AssertNE(reportOutput2, nil)
		t.Assert(reportOutput2.Success, true)

		g.Log().Infof(ctx, "集成测试完成: SagaId=%s, 原策略=%s, 当前策略=%s",
			sagaId, originalStrategy, service.GetLockStrategy().GetStrategyName())
	})
}
