package service

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/guid"

	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"saga/internal/model/entity"
)

// SagaTransactionsService Saga 事务服务
// Service 层专注业务逻辑，不做参数验证
// 参数验证由 Controller 层负责
type SagaTransactionsService struct {
	httpClient *gclient.Client
	// 补偿重试配置
	maxRetries           int           // 最大重试次数
	initialRetryInterval time.Duration // 初始重试间隔
	maxRetryInterval     time.Duration // 最大重试间隔
	retryMultiplier      float64       // 指数退避倍数
	compensationTimeout  time.Duration // 补偿超时时间
	// ReportCompensation 重试配置
	reportMaxRetries int           // ReportCompensation 最大重试次数
	reportRetryDelay time.Duration // ReportCompensation 基础重试延迟
	// 补偿窗口期配置
	defaultCompensationWindow int // 默认补偿窗口期（秒）

	// 乐观锁配置
	optimisticLockEnabled           bool          // 是否启用乐观锁模式
	optimisticLockMaxRetries        int           // 乐观锁最大重试次数
	optimisticLockBaseDelay         time.Duration // 乐观锁重试基础延迟时间
	optimisticLockMaxDelay          time.Duration // 乐观锁重试最大延迟时间
	optimisticLockBackoffMultiplier float64       // 指数退避倍数
}

// NewSagaTransactions 获取 SagaTransactionsService 实例
func NewSagaTransactions() *SagaTransactionsService {
	service := &SagaTransactionsService{
		httpClient:                gclient.New(),
		maxRetries:                consts.DefaultMaxRetries,
		initialRetryInterval:      consts.DefaultInitialRetryInterval,
		maxRetryInterval:          consts.DefaultMaxRetryInterval,
		retryMultiplier:           consts.DefaultRetryMultiplier,
		compensationTimeout:       consts.DefaultCompensationTimeout,
		reportMaxRetries:          consts.DefaultReportMaxRetries,
		reportRetryDelay:          consts.DefaultReportRetryDelay,
		defaultCompensationWindow: consts.DefaultCompensationWindowSec,
	}

	// 从配置文件读取乐观锁配置
	service.loadOptimisticLockConfig()

	return service
}

// loadOptimisticLockConfig 从配置文件加载乐观锁配置
func (s *SagaTransactionsService) loadOptimisticLockConfig() {
	// 设置默认值
	s.optimisticLockEnabled = false
	s.optimisticLockMaxRetries = 3
	s.optimisticLockBaseDelay = 10 * time.Millisecond
	s.optimisticLockMaxDelay = 100 * time.Millisecond
	s.optimisticLockBackoffMultiplier = 2.0

	// 从配置文件读取
	ctx := context.Background()
	if g.Cfg().Available(ctx) {
		// 读取乐观锁配置
		s.optimisticLockEnabled = g.Cfg().MustGet(ctx, "saga.optimisticLock.enabled", false).Bool()
		s.optimisticLockMaxRetries = g.Cfg().MustGet(ctx, "saga.optimisticLock.maxRetries", 3).Int()
		s.optimisticLockBaseDelay = g.Cfg().MustGet(ctx, "saga.optimisticLock.baseDelay", "10ms").Duration()
		s.optimisticLockMaxDelay = g.Cfg().MustGet(ctx, "saga.optimisticLock.maxDelay", "100ms").Duration()
		s.optimisticLockBackoffMultiplier = g.Cfg().MustGet(ctx, "saga.optimisticLock.backoffMultiplier", 2.0).Float64()

		g.Log().Infof(ctx, "乐观锁配置加载完成: enabled=%v, maxRetries=%d, baseDelay=%v, maxDelay=%v, backoffMultiplier=%.1f",
			s.optimisticLockEnabled, s.optimisticLockMaxRetries, s.optimisticLockBaseDelay, s.optimisticLockMaxDelay, s.optimisticLockBackoffMultiplier)
	}
}

// NewSagaTransactionsWithRetryConfig 创建带自定义重试配置的 SagaTransactionsService 实例
func NewSagaTransactionsWithRetryConfig(maxRetries int, initialInterval, maxInterval time.Duration, multiplier float64) *SagaTransactionsService {
	return &SagaTransactionsService{
		httpClient:                gclient.New(),
		maxRetries:                maxRetries,
		initialRetryInterval:      initialInterval,
		maxRetryInterval:          maxInterval,
		retryMultiplier:           multiplier,
		compensationTimeout:       consts.DefaultCompensationTimeout,
		reportMaxRetries:          consts.DefaultReportMaxRetries,
		reportRetryDelay:          consts.DefaultReportRetryDelay,
		defaultCompensationWindow: consts.DefaultCompensationWindowSec,
	}
}

// NewSagaTransactionsWithFullConfig 创建带完整配置的 SagaTransactionsService 实例
// 允许配置补偿重试和 ReportCompensation 重试的所有参数
func NewSagaTransactionsWithFullConfig(
	// 补偿重试配置
	maxRetries int, initialInterval, maxInterval time.Duration, multiplier float64, timeout time.Duration,
	// ReportCompensation 重试配置
	reportMaxRetries int, reportRetryDelay time.Duration,
	// 补偿窗口期配置
	compensationWindow int,
) *SagaTransactionsService {
	return &SagaTransactionsService{
		httpClient:                gclient.New(),
		maxRetries:                maxRetries,
		initialRetryInterval:      initialInterval,
		maxRetryInterval:          maxInterval,
		retryMultiplier:           multiplier,
		compensationTimeout:       timeout,
		reportMaxRetries:          reportMaxRetries,
		reportRetryDelay:          reportRetryDelay,
		defaultCompensationWindow: compensationWindow,
	}
}

// CreateSagaTransaction 创建分布式事务
// 支持两种 StepIndex 管理模式：
// 1. auto: 自增方式 - 服务上报时自动分配递增的 step_index
// 2. manual: 模板方式 - 预定义步骤模板，上报时根据 step_name 匹配
func (s *SagaTransactionsService) CreateSagaTransaction(ctx context.Context, input *model.CreateSagaTransactionInput) (*model.CreateSagaTransactionOutput, error) {
	// 生成唯一的 Saga ID
	sagaId := guid.S()

	// 设置默认的 StepIndex 管理模式
	if input.StepIndexMode == "" {
		input.StepIndexMode = consts.StepIndexModeAuto
	}

	// 设置默认的补偿窗口期
	compensationWindowSec := s.defaultCompensationWindow
	if input.CompensationWindowSec > 0 {
		compensationWindowSec = input.CompensationWindowSec
	}

	// 创建 Saga 事务记录
	sagaTransaction := &entity.SagaTransactions{
		SagaId:                sagaId,
		Name:                  input.Name,
		SagaStatus:            consts.SagaStatusPending, // 默认状态为 pending
		StepIndexMode:         input.StepIndexMode,      // 步骤索引管理模式
		CurStepIndex:          0,                        // 初始为 0
		CompensationWindowSec: compensationWindowSec,    // 补偿窗口期
		CreatedAt:             gtime.Now(),
		UpdatedAt:             gtime.Now(),
	}

	// 如果是manual模式，需要序列化步骤模板
	if input.StepIndexMode == consts.StepIndexModeManual && len(input.StepTemplates) > 0 {
		templatesJSON, err := json.Marshal(input.StepTemplates)
		if err != nil {
			return nil, fmt.Errorf("序列化步骤模板失败: %w", err)
		}
		sagaTransaction.StepTemplates = string(templatesJSON)
	}

	// 插入 Saga 事务记录
	_, err := dao.SagaTransactions.Insert(ctx, sagaTransaction)
	if err != nil {
		g.Log().Errorf(ctx, "创建 saga 事务失败: %v", err)
		return nil, fmt.Errorf("创建 saga 事务记录失败: %w", err)
	}

	if input.StepIndexMode == consts.StepIndexModeManual && len(input.StepTemplates) > 0 {
		g.Log().Infof(ctx, "创建 Saga 事务成功（manual模式），SagaId: %s, Name: %s, 步骤模板数: %d",
			sagaId, input.Name, len(input.StepTemplates))
	} else {
		g.Log().Infof(ctx, "创建 Saga 事务成功（auto模式），SagaId: %s, Name: %s", sagaId, input.Name)
	}

	return &model.CreateSagaTransactionOutput{
		SagaId:    sagaId,
		Name:      input.Name,
		Status:    consts.SagaStatusPending,
		CreatedAt: gtime.Now(),
	}, nil
}

// GetSagaTransaction 获取 Saga 事务信息
func (s *SagaTransactionsService) GetSagaTransaction(ctx context.Context, input *model.GetSagaTransactionInput) (*model.GetSagaTransactionOutput, error) {
	result, err := dao.SagaTransactions.FindBySagaId(ctx, input.SagaId)
	if err != nil {
		g.Log().Errorf(ctx, "获取 saga 事务失败: %v", err)
		return nil, err
	}

	if result == nil {
		return nil, fmt.Errorf("saga 事务不存在: sagaId=%s", input.SagaId)
	}

	return &model.GetSagaTransactionOutput{
		SagaId:      result.SagaId,
		Name:        result.Name,
		Status:      result.SagaStatus,
		CurrentStep: fmt.Sprintf("%d", result.CurStepIndex),
		RetryCount:  0,
		CreatedAt:   result.CreatedAt,
		UpdatedAt:   result.UpdatedAt,
	}, nil
}

// isInCompensationWindow 检查事务是否在补偿窗口期内
func (s *SagaTransactionsService) isInCompensationWindow(sagaTransaction *entity.SagaTransactions) bool {
	// 如果事务状态是 completed、failed 或 compensating，检查是否在补偿窗口期内
	if sagaTransaction.SagaStatus == consts.SagaStatusCompleted ||
		sagaTransaction.SagaStatus == consts.SagaStatusFailed ||
		sagaTransaction.SagaStatus == consts.SagaStatusCompensating {
		// 获取有效的补偿窗口期（秒）
		windowSec := sagaTransaction.CompensationWindowSec
		if windowSec <= 0 {
			windowSec = s.defaultCompensationWindow
		}

		// 计算事务状态更新后经过的时间（秒）
		timeSinceUpdate := time.Since(sagaTransaction.UpdatedAt.Time).Seconds()

		// 记录详细的计算过程，帮助调试
		g.Log().Debugf(context.Background(), "补偿窗口期计算: UpdatedAt=%v, 当前时间=%v, 经过时间=%.2f秒, 窗口期=%d秒, 是否在窗口期内=%v",
			sagaTransaction.UpdatedAt, time.Now(), timeSinceUpdate, windowSec, timeSinceUpdate <= float64(windowSec))

		// 如果在补偿窗口期内，返回 true
		return timeSinceUpdate <= float64(windowSec)
	}
	return false
}

// ReportCompensation 服务上报补偿操作结果
// 自动检测 StepIndex 管理模式：
// - manual模式：从 saga_transactions 表的 step_templates 中根据 service+action 匹配 step_index
// - auto模式：自动分配递增的 step_index（使用原子操作确保并发安全）
func (s *SagaTransactionsService) ReportCompensation(ctx context.Context, input *model.ReportCompensationInput) (*model.ReportCompensationOutput, error) {
	// 根据配置选择锁机制
	if s.optimisticLockEnabled {
		g.Log().Infof(ctx, "使用乐观锁模式处理补偿上报: SagaId=%s, Action=%s, Service=%s",
			input.SagaId, input.Action, input.ServiceName)
		return s.ReportCompensationWithOptimisticLock(ctx, input)
	}

	g.Log().Infof(ctx, "使用悲观锁模式处理补偿上报: SagaId=%s, Action=%s, Service=%s",
		input.SagaId, input.Action, input.ServiceName)

	// 使用数据库事务确保原子性（悲观锁模式）
	var result *model.ReportCompensationOutput
	// 定义事务外需要执行的补偿信息
	var compensationInfo *model.ExecuteCompensationInput

	err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 使用 dao 层的方法锁定并获取 Saga 事务记录
		sagaTransaction, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, input.SagaId)
		if err != nil {
			g.Log().Errorf(ctx, "锁定并获取Saga事务失败: %v", err)
			return err
		}

		if sagaTransaction == nil {
			g.Log().Errorf(ctx, "Saga事务不存在: %s", input.SagaId)
			return fmt.Errorf("saga 事务不存在: sagaId=%s", input.SagaId)
		}

		g.Log().Infof(ctx, "获取到Saga事务: SagaId=%s, Status=%s, UpdatedAt=%v",
			sagaTransaction.SagaId, sagaTransaction.SagaStatus, sagaTransaction.UpdatedAt)

		// 2. 检查 Saga 状态：只有 pending 和 running 状态才能上报补偿信息
		// 或者在补偿窗口期内的 completed、failed 和 compensating 状态也可以上报
		inCompensationWindow := s.isInCompensationWindow(sagaTransaction)
		g.Log().Infof(ctx, "补偿窗口期检查: SagaId=%s, Status=%s, inCompensationWindow=%v",
			sagaTransaction.SagaId, sagaTransaction.SagaStatus, inCompensationWindow)

		// 检查是否允许处理补偿信息
		allowProcessing := sagaTransaction.SagaStatus == consts.SagaStatusPending ||
			sagaTransaction.SagaStatus == consts.SagaStatusRunning ||
			(inCompensationWindow && (sagaTransaction.SagaStatus == consts.SagaStatusFailed ||
				sagaTransaction.SagaStatus == consts.SagaStatusCompensating))

		g.Log().Infof(ctx, "补偿处理决策: SagaId=%s, Status=%s, allowProcessing=%v",
			sagaTransaction.SagaId, sagaTransaction.SagaStatus, allowProcessing)

		if !allowProcessing {
			// 处理不允许处理补偿信息的情况
			processResult, processErr := s.handleDisallowedCompensationProcessing(ctx, input, sagaTransaction)
			if processErr != nil {
				return processErr
			}
			if processResult != nil {
				result = processResult
				return nil
			}
		}

		g.Log().Infof(ctx, "准备创建或更新步骤: SagaId=%s, Action=%s, Service=%s",
			input.SagaId, input.Action, input.ServiceName)

		// 3. 如果事务在补偿窗口期内且当前是 failed 或 compensating 状态，则设置步骤状态为 delay
		stepStatus := consts.CompensationStatusUninitialized
		if inCompensationWindow && (sagaTransaction.SagaStatus == consts.SagaStatusFailed ||
			sagaTransaction.SagaStatus == consts.SagaStatusCompensating) {
			g.Log().Infof(ctx, "检测到补偿窗口期内的补偿上报: SagaId=%s, 当前状态=%s, 服务=%s, 动作=%s",
				input.SagaId, sagaTransaction.SagaStatus, input.ServiceName, input.Action)
			stepStatus = consts.CompensationStatusDelay
		}

		// 4. 使用 CreateOrUpdateStep 原子性地创建或更新步骤
		// 这利用了数据库的唯一约束 (saga_id, action, service_name) 来处理并发
		step, err := s.createOrUpdateStep(ctx, tx, sagaTransaction, input, stepStatus)
		if err != nil {
			g.Log().Errorf(ctx, "创建或更新步骤失败: %v", err)
			return err
		}

		g.Log().Infof(ctx, "步骤创建或更新成功: SagaId=%s, StepId=%s", input.SagaId, step.StepId)

		// 5. 如果事务在补偿窗口期内且当前是 failed 或 compensating 状态，则准备自动执行补偿
		if stepStatus == consts.CompensationStatusDelay {
			g.Log().Infof(ctx, "步骤补偿状态已更新为delay: SagaId=%s, StepId=%s", input.SagaId, step.StepId)
			// 准备补偿信息，但在事务提交后再执行
			compensationInfo = &model.ExecuteCompensationInput{
				SagaId:    input.SagaId,
				StepId:    step.StepId,
				StepIndex: step.StepIndex,
				Timeout:   s.compensationTimeout,
			}
		}

		result = &model.ReportCompensationOutput{
			Success: true,
			Message: "步骤创建或更新成功",
		}

		g.Log().Infof(ctx, "补偿信息上报成功: SagaId=%s, Action=%s, Service=%s, StepId=%s",
			input.SagaId, input.Action, input.ServiceName, step.StepId)

		return nil
	})

	if err != nil {
		g.Log().Errorf(ctx, "ReportCompensation 原子操作失败: SagaId=%s, Action=%s, 错误=%v",
			input.SagaId, input.Action, err)
		return nil, err
	}

	g.Log().Infof(ctx, "数据库事务已提交: SagaId=%s, 结果=%v, 消息=%s",
		input.SagaId, result.Success, result.Message)

	// 事务成功提交后，如果需要执行补偿，则异步执行
	if compensationInfo != nil {
		g.Log().Infof(ctx, "准备异步执行补偿: SagaId=%s, StepId=%s",
			compensationInfo.SagaId, compensationInfo.StepId)

		go func(info *model.ExecuteCompensationInput) {
			asyncCtx := context.Background()
			// 执行补偿操作
			_, execErr := s.ExecuteCompensation(asyncCtx, info)

			if execErr != nil {
				g.Log().Errorf(asyncCtx, "补偿窗口期内异步执行补偿失败: SagaId=%s, StepId=%s, Error=%v",
					info.SagaId, info.StepId, execErr)
			}
		}(compensationInfo)
	}

	return result, nil
}

// ReportCompensationWithOptimisticLock 使用乐观锁的补偿信息上报（性能对比版本）
func (s *SagaTransactionsService) ReportCompensationWithOptimisticLock(ctx context.Context, input *model.ReportCompensationInput) (*model.ReportCompensationOutput, error) {
	// 使用服务属性中的乐观锁参数
	maxRetries := s.optimisticLockMaxRetries
	baseDelay := s.optimisticLockBaseDelay

	g.Log().Infof(ctx, "开始处理补偿上报（乐观锁）: SagaId=%s, Action=%s, Service=%s",
		input.SagaId, input.Action, input.ServiceName)

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避延迟，使用服务属性参数
			delay := time.Duration(float64(baseDelay) * math.Pow(s.optimisticLockBackoffMultiplier, float64(attempt-1)))
			if delay > s.optimisticLockMaxDelay {
				delay = s.optimisticLockMaxDelay
			}
			g.Log().Infof(ctx, "乐观锁重试: SagaId=%s, 第%d次重试, 延迟%v", input.SagaId, attempt, delay)
			time.Sleep(delay)
		}

		result, err := s.tryReportCompensationWithOptimisticLock(ctx, input)
		if err != nil {
			// 检查是否是版本冲突错误
			if strings.Contains(err.Error(), "version_conflict") {
				g.Log().Warningf(ctx, "乐观锁版本冲突: SagaId=%s, 第%d次重试", input.SagaId, attempt+1)
				continue // 重试
			}
			// 其他错误直接返回
			return nil, err
		}

		g.Log().Infof(ctx, "乐观锁补偿上报成功: SagaId=%s, 尝试次数=%d", input.SagaId, attempt+1)
		return result, nil
	}

	return nil, fmt.Errorf("乐观锁重试失败: SagaId=%s, 最大重试次数=%d", input.SagaId, maxRetries)
}

// tryReportCompensationWithOptimisticLock 单次尝试使用乐观锁上报补偿信息
func (s *SagaTransactionsService) tryReportCompensationWithOptimisticLock(ctx context.Context, input *model.ReportCompensationInput) (*model.ReportCompensationOutput, error) {
	// 1. 读取当前 Saga 事务状态（无锁）
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, input.SagaId)
	if err != nil {
		return nil, err
	}

	if sagaTransaction == nil {
		return nil, fmt.Errorf("saga 事务不存在: sagaId=%s", input.SagaId)
	}

	originalVersion := sagaTransaction.CurStepIndex
	g.Log().Infof(ctx, "获取到Saga事务（乐观锁）: SagaId=%s, Status=%s, Version=%d",
		sagaTransaction.SagaId, sagaTransaction.SagaStatus, originalVersion)

	// 2. 检查 Saga 状态
	inCompensationWindow := s.isInCompensationWindow(sagaTransaction)
	allowProcessing := sagaTransaction.SagaStatus == consts.SagaStatusPending ||
		sagaTransaction.SagaStatus == consts.SagaStatusRunning ||
		(inCompensationWindow && (sagaTransaction.SagaStatus == consts.SagaStatusFailed ||
			sagaTransaction.SagaStatus == consts.SagaStatusCompensating))

	if !allowProcessing {
		// 处理不允许处理补偿信息的情况
		return s.handleDisallowedCompensationProcessing(ctx, input, sagaTransaction)
	}

	// 3. 确定步骤状态
	stepStatus := consts.CompensationStatusUninitialized
	if inCompensationWindow && (sagaTransaction.SagaStatus == consts.SagaStatusFailed ||
		sagaTransaction.SagaStatus == consts.SagaStatusCompensating) {
		stepStatus = consts.CompensationStatusDelay
	}

	// 4. 在事务中创建或更新步骤，并使用乐观锁更新 Saga
	var result *model.ReportCompensationOutput
	var compensationInfo *model.ExecuteCompensationInput

	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 4.1 创建或更新步骤
		step, err := s.createOrUpdateStepOptimistic(ctx, tx, sagaTransaction, input, stepStatus)
		if err != nil {
			return err
		}

		// 4.2 准备更新数据
		updateData := make(map[string]interface{})
		updateData[dao.SagaTransactions.Columns().UpdatedAt] = gtime.Now()

		// 更新状态
		if consts.SagaStatusRunning != sagaTransaction.SagaStatus {
			updateData[dao.SagaTransactions.Columns().SagaStatus] = consts.SagaStatusRunning
		}

		// 更新步骤索引（仅对 auto 模式有效）
		newStepIndex := originalVersion
		if sagaTransaction.StepIndexMode == consts.StepIndexModeAuto {
			newStepIndex = step.StepIndex
			updateData[dao.SagaTransactions.Columns().CurStepIndex] = step.StepIndex
		}

		// 4.3 使用乐观锁更新 Saga 事务
		success, err := dao.SagaTransactions.UpdateWithVersionCheck(ctx, input.SagaId, originalVersion, updateData)
		if err != nil {
			return err
		}

		if !success {
			return fmt.Errorf("version_conflict: 乐观锁版本冲突, SagaId=%s, 期望版本=%d", input.SagaId, originalVersion)
		}

		g.Log().Infof(ctx, "乐观锁更新成功: SagaId=%s, 版本 %d -> %d", input.SagaId, originalVersion, newStepIndex)

		// 4.4 处理补偿窗口期内的延迟补偿
		if stepStatus == consts.CompensationStatusDelay {
			compensationInfo = &model.ExecuteCompensationInput{
				SagaId:    input.SagaId,
				StepId:    step.StepId,
				StepIndex: step.StepIndex,
				Timeout:   s.compensationTimeout,
			}
		}

		result = &model.ReportCompensationOutput{
			Success: true,
			Message: "步骤创建或更新成功（乐观锁）",
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 5. 事务成功提交后，如果需要执行补偿，则异步执行
	if compensationInfo != nil {
		go func(info *model.ExecuteCompensationInput) {
			asyncCtx := context.Background()
			_, execErr := s.ExecuteCompensation(asyncCtx, info)
			if execErr != nil {
				g.Log().Errorf(asyncCtx, "补偿窗口期内异步执行补偿失败（乐观锁）: SagaId=%s, StepId=%s, Error=%v",
					info.SagaId, info.StepId, execErr)
			}
		}(compensationInfo)
	}

	return result, nil
}

// createOrUpdateStepOptimistic 乐观锁版本的创建或更新步骤
func (s *SagaTransactionsService) createOrUpdateStepOptimistic(ctx context.Context, tx gdb.TX, sagaTransaction *entity.SagaTransactions, input *model.ReportCompensationInput, stepStatus string) (*entity.SagaSteps, error) {
	// 确定步骤索引
	var stepIndex int
	var err error

	if sagaTransaction.StepIndexMode == consts.StepIndexModeAuto {
		// Auto 模式：基于当前版本计算下一个索引
		stepIndex = sagaTransaction.CurStepIndex + 1
	} else {
		// Manual 模式：从模板中确定索引
		stepIndex, err = s.determineStepIndexFromTemplate(ctx, sagaTransaction, input.Action, input.ServiceName)
		if err != nil {
			return nil, err
		}
	}

	// 创建或更新步骤
	step := &entity.SagaSteps{
		SagaId:              input.SagaId,
		StepIndex:           stepIndex,
		ServiceName:         input.ServiceName,
		Action:              input.Action,
		ContextData:         input.ContextData,
		CompensationContext: input.CompensationContext,
		CompensateEndpoint:  input.CompensateEndpoint,
		CompensationStatus:  stepStatus,
		RetryCount:          0,
		CreatedAt:           gtime.Now(),
		UpdatedAt:           gtime.Now(),
	}

	// 尝试创建步骤
	err = dao.SagaSteps.CreateOrUpdateStep(ctx, tx, step)
	if err != nil {
		return nil, fmt.Errorf("创建或更新步骤失败: %w", err)
	}

	// 查询创建的步骤以获取 StepId
	createdStep, err := dao.SagaSteps.FindByActionAndServiceWithTx(ctx, tx, input.SagaId, input.Action, input.ServiceName)
	if err != nil {
		return nil, fmt.Errorf("查询创建的步骤失败: %w", err)
	}

	return createdStep, nil
}

// determineStepIndexFromTemplate 从模板中确定步骤索引 (仅用于 Manual 模式)
func (s *SagaTransactionsService) determineStepIndexFromTemplate(ctx context.Context, sagaTransaction *entity.SagaTransactions,
	action, serviceName string) (int, error) {
	if sagaTransaction.StepTemplates == "" {
		g.Log().Warningf(ctx, "Saga 配置为 Manual 模式但 StepTemplates 为空，将使用 Auto 模式: SagaId=%s, Service=%s, Action=%s",
			sagaTransaction.SagaId, serviceName, action)
		// 当 StepTemplates 为空时，降级为 auto 模式逻辑
		return sagaTransaction.CurStepIndex + 1, nil
	}

	var templates []model.SagaStepTemplate
	err := json.Unmarshal([]byte(sagaTransaction.StepTemplates), &templates)
	if err != nil {
		return 0, fmt.Errorf("解析步骤模板失败: %w", err)
	}

	// 在模板中查找匹配的步骤
	for _, template := range templates {
		if template.Service == serviceName && template.Action == action {
			g.Log().Infof(ctx, "使用manual模式 StepIndex: SagaId=%s, Service=%s, Action=%s, StepIndex=%d",
				sagaTransaction.SagaId, serviceName, action, template.StepIndex)
			return template.StepIndex, nil
		}
	}

	return 0, fmt.Errorf("未找到匹配的步骤模板: service=%s, action=%s", serviceName, action)
}

// handleDisallowedCompensationProcessing 处理不允许处理补偿信息的情况
// 返回值：(处理结果, 错误)
// 如果返回的处理结果不为nil，表示应该直接返回该结果
// 如果返回错误不为nil，表示发生了需要向上传播的错误
func (s *SagaTransactionsService) handleDisallowedCompensationProcessing(ctx context.Context, input *model.ReportCompensationInput, sagaTransaction *entity.SagaTransactions) (*model.ReportCompensationOutput, error) {
	// 记录延迟消息的指标（用于监控）
	g.Log().Warningf(ctx, "检测到延迟的补偿上报: SagaId=%s, 当前状态=%s, 服务=%s, 动作=%s",
		input.SagaId, sagaTransaction.SagaStatus, input.ServiceName, input.Action)

	// 根据不同状态提供友好的错误消息
	switch sagaTransaction.SagaStatus {
	case consts.SagaStatusCompleted:
		return &model.ReportCompensationOutput{
			Success: false,
			Message: "事务已完成，忽略延迟的补偿信息",
		}, nil
	case consts.SagaStatusCompensating:
		// 这个分支只有在不在补偿窗口期内时才会执行
		return &model.ReportCompensationOutput{
			Success: false,
			Message: "事务正在补偿中，且超出补偿窗口期，忽略延迟的补偿信息",
		}, nil
	case consts.SagaStatusFailed:
		// 这个分支只有在不在补偿窗口期内时才会执行
		return &model.ReportCompensationOutput{
			Success: false,
			Message: "事务已失败，忽略延迟的补偿信息",
		}, nil
	default:
		return nil, fmt.Errorf("saga 状态不允许上报补偿信息: sagaId=%s, 当前状态=%s, 只有 pending 和 running 状态才能上报",
			input.SagaId, sagaTransaction.SagaStatus)
	}
}

// CommitSagaTransaction 提交分布式事务
// 检查所有步骤是否已完成，如果是则将 Saga 状态更新为 completed
// 实现幂等性：如果事务已经是 completed 状态，返回成功（幂等）
func (s *SagaTransactionsService) CommitSagaTransaction(ctx context.Context, input *model.CommitSagaTransactionInput) (*model.CommitSagaTransactionOutput, error) {
	// 使用数据库事务确保原子性和并发安全
	var result *model.CommitSagaTransactionOutput
	err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 使用悲观锁查询 Saga 事务信息（与 ReportCompensation 保持一致）
		sagaTransaction, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, input.SagaId)
		if err != nil {
			return err
		}

		if sagaTransaction == nil {
			return fmt.Errorf("saga 事务不存在: sagaId=%s", input.SagaId)
		}

		// 2. 检查 Saga 事务状态 - 实现幂等性
		canContinue, statusResult, err := s.checkSagaStatusForCommit(ctx, sagaTransaction, input.SagaId)
		if err != nil {
			return err
		}
		if !canContinue {
			result = statusResult
			return nil
		}

		// 3. 检查所有步骤是否已完成
		canCommit, completedSteps, expectedSteps, err := s.checkAllStepsCompleted(ctx, sagaTransaction)
		if err != nil {
			return fmt.Errorf("检查步骤完成状态失败: %w", err)
		}

		if !canCommit {
			result = &model.CommitSagaTransactionOutput{
				Success:        false,
				Message:        fmt.Sprintf("无法提交事务，还有步骤未完成，已完成: %d/%d", completedSteps, expectedSteps),
				SagaId:         input.SagaId,
				CompletedSteps: completedSteps,
				ExpectedSteps:  expectedSteps,
				NewStatus:      sagaTransaction.SagaStatus,
				CompletedAt:    gtime.Now(),
			}
			return nil
		}

		// 4. 更新 Saga 状态为 completed（使用事务）
		err = dao.SagaTransactions.UpdateStatusWithTx(ctx, input.SagaId, consts.SagaStatusCompleted, tx)
		if err != nil {
			g.Log().Errorf(ctx, "更新 Saga 状态为 completed 失败: %v", err)
			return fmt.Errorf("更新 Saga 状态失败: %w", err)
		}

		g.Log().Infof(ctx, "Saga 事务提交成功: SagaId=%s, 完成步骤数=%d", input.SagaId, completedSteps)

		result = &model.CommitSagaTransactionOutput{
			Success:        true,
			Message:        "分布式事务提交成功",
			SagaId:         input.SagaId,
			CompletedSteps: completedSteps,
			ExpectedSteps:  expectedSteps,
			NewStatus:      consts.SagaStatusCompleted,
			CompletedAt:    gtime.Now(),
		}
		return nil
	})

	if err != nil {
		g.Log().Errorf(ctx, "CommitSagaTransaction 事务失败: SagaId=%s, 错误=%v", input.SagaId, err)
		return nil, err
	}

	return result, nil
}

// checkSagaStatusForCommit 检查 Saga 事务状态是否允许提交
// 返回值：(是否可以继续处理, 如果不能继续处理的输出结果, 错误)
func (s *SagaTransactionsService) checkSagaStatusForCommit(ctx context.Context, sagaTransaction *entity.SagaTransactions, sagaId string) (bool, *model.CommitSagaTransactionOutput, error) {
	switch sagaTransaction.SagaStatus {
	case consts.SagaStatusCompleted:
		// 幂等性：如果事务已经是 completed 状态，返回成功
		completedSteps, err := dao.SagaSteps.CountBySagaId(ctx, sagaTransaction.SagaId)
		if err != nil {
			// 即使查询失败，也应该返回成功，因为事务已完成
			g.Log().Warningf(ctx, "查询已完成事务的步骤数失败: SagaId=%s, 错误=%v", sagaId, err)
			completedSteps = 0
		}

		g.Log().Infof(ctx, "事务已完成（幂等性）: SagaId=%s, 状态=%s", sagaId, sagaTransaction.SagaStatus)

		return false, &model.CommitSagaTransactionOutput{
			Success:        true,
			Message:        "事务已完成（幂等性）",
			SagaId:         sagaId,
			CompletedSteps: completedSteps,
			ExpectedSteps:  sagaTransaction.CurStepIndex, // 从事务记录中获取预期步骤数
			NewStatus:      consts.SagaStatusCompleted,
			CompletedAt:    sagaTransaction.UpdatedAt, // 使用原来的完成时间
		}, nil

	case consts.SagaStatusFailed:
		return false, &model.CommitSagaTransactionOutput{
			Success:     false,
			Message:     "无法提交失败的事务",
			SagaId:      sagaId,
			NewStatus:   sagaTransaction.SagaStatus,
			CompletedAt: gtime.Now(),
		}, nil

	case consts.SagaStatusCompensating:
		return false, &model.CommitSagaTransactionOutput{
			Success:     false,
			Message:     "无法提交正在补偿的事务",
			SagaId:      sagaId,
			NewStatus:   sagaTransaction.SagaStatus,
			CompletedAt: gtime.Now(),
		}, nil

	case consts.SagaStatusPending:
		return false, &model.CommitSagaTransactionOutput{
			Success:     false,
			Message:     "无法提交待处理的事务，只有 running 状态的事务才能提交",
			SagaId:      sagaId,
			NewStatus:   sagaTransaction.SagaStatus,
			CompletedAt: gtime.Now(),
		}, nil

	case consts.SagaStatusRunning:
		// 正常的提交逻辑
		return true, nil, nil

	default:
		return false, &model.CommitSagaTransactionOutput{
			Success:     false,
			Message:     fmt.Sprintf("未知的事务状态: %s", sagaTransaction.SagaStatus),
			SagaId:      sagaId,
			NewStatus:   sagaTransaction.SagaStatus,
			CompletedAt: gtime.Now(),
		}, nil
	}
}

// checkSagaStatusForRollback 检查 Saga 事务状态是否允许回滚
// 返回值：(是否可以继续处理, 如果不能继续处理的输出结果, 错误)
func (s *SagaTransactionsService) checkSagaStatusForRollback(ctx context.Context, sagaTransaction *entity.SagaTransactions, sagaId string) (bool, *model.RollbackSagaTransactionOutput, error) {
	switch sagaTransaction.SagaStatus {
	case consts.SagaStatusCompleted:
		return false, &model.RollbackSagaTransactionOutput{
			Success:   false,
			Message:   "无法回滚已完成的事务",
			SagaId:    sagaId,
			NewStatus: sagaTransaction.SagaStatus,
		}, nil

	case consts.SagaStatusFailed:
		// 幂等性：如果事务已经是 failed 状态，返回成功（已完成回滚）

		g.Log().Infof(ctx, "事务已失败（幂等性）: SagaId=%s, 状态=%s", sagaId, sagaTransaction.SagaStatus)

		return false, &model.RollbackSagaTransactionOutput{
			Success:             true,
			Message:             "事务已失败（幂等性），回滚已完成",
			SagaId:              sagaId,
			NewStatus:           consts.SagaStatusFailed,
			IsRollbackCompleted: true,
			StartedAt:           sagaTransaction.UpdatedAt, // 使用原来的失败时间
			CompletedAt:         sagaTransaction.UpdatedAt, // 使用原来的失败时间
			FailReason:          sagaTransaction.FailReason,
		}, nil

	case consts.SagaStatusCompensating:
		// 幂等性：如果事务已经是 compensating 状态，返回当前状态信息（正在回滚）

		g.Log().Infof(ctx, "事务正在补偿（幂等性）: SagaId=%s, 状态=%s", sagaId, sagaTransaction.SagaStatus)

		return false, &model.RollbackSagaTransactionOutput{
			Success:             true,
			Message:             "事务正在补偿中（幂等性），回滚已启动",
			SagaId:              sagaId,
			NewStatus:           consts.SagaStatusCompensating,
			IsRollbackCompleted: false,
			StartedAt:           sagaTransaction.UpdatedAt, // 使用原来的补偿开始时间
			FailReason:          sagaTransaction.FailReason,
		}, nil

	case consts.SagaStatusPending:
		return false, &model.RollbackSagaTransactionOutput{
			Success:   false,
			Message:   "待处理状态的事务无需回滚，因为还没有步骤上报",
			SagaId:    sagaId,
			NewStatus: sagaTransaction.SagaStatus,
		}, nil

	case consts.SagaStatusRunning:
		// 正常的回滚逻辑
		return true, nil, nil

	default:
		return false, &model.RollbackSagaTransactionOutput{
			Success:   false,
			Message:   fmt.Sprintf("未知的事务状态: %s", sagaTransaction.SagaStatus),
			SagaId:    sagaId,
			NewStatus: sagaTransaction.SagaStatus,
		}, nil
	}
}

// checkAllStepsCompleted 检查所有步骤是否已完成
func (s *SagaTransactionsService) checkAllStepsCompleted(ctx context.Context, sagaTransaction *entity.SagaTransactions) (bool, int, int, error) {
	// 获取当前已完成的步骤数
	completedSteps, err := dao.SagaSteps.CountBySagaId(ctx, sagaTransaction.SagaId)
	if err != nil {
		return false, 0, 0, fmt.Errorf("获取已完成步骤数失败: %w", err)
	}

	// 根据 StepIndexMode 计算预期步骤数
	var expectedSteps int
	var canCommit bool

	if sagaTransaction.StepIndexMode == consts.StepIndexModeAuto {
		// Auto 模式：预期步骤数等于当前步骤索引
		expectedSteps = sagaTransaction.CurStepIndex
		canCommit = completedSteps == expectedSteps && expectedSteps > 0

		g.Log().Infof(ctx, "Auto 模式检查步骤完成状态: SagaId=%s, 已完成=%d, 预期=%d, 可提交=%t",
			sagaTransaction.SagaId, completedSteps, expectedSteps, canCommit)
	} else if sagaTransaction.StepIndexMode == consts.StepIndexModeManual {
		// Manual 模式：检查所有模板中的步骤是否都已上报
		var templates []model.SagaStepTemplate
		if sagaTransaction.StepTemplates != "" {
			err := json.Unmarshal([]byte(sagaTransaction.StepTemplates), &templates)
			if err != nil {
				return false, 0, 0, fmt.Errorf("解析步骤模板失败: %w", err)
			}
		}

		expectedSteps = len(templates)
		if expectedSteps == 0 {
			// 如果没有步骤模板，降级为 Auto 模式处理
			expectedSteps = sagaTransaction.CurStepIndex
			canCommit = completedSteps == expectedSteps && expectedSteps > 0
			g.Log().Warningf(ctx, "Manual 模式但无步骤模板，降级为 Auto 模式: SagaId=%s", sagaTransaction.SagaId)
		} else {
			// 检查所有模板步骤是否都已上报
			canCommit = true
			for _, template := range templates {
				exists, err := dao.SagaSteps.CheckStepExistsBySagaIdAndTemplate(ctx, sagaTransaction.SagaId, template.Service, template.Action)
				if err != nil {
					return false, 0, 0, fmt.Errorf("检查步骤是否存在失败: %w", err)
				}
				if !exists {
					canCommit = false
					g.Log().Warningf(ctx, "步骤未完成: SagaId=%s, Service=%s, Action=%s",
						sagaTransaction.SagaId, template.Service, template.Action)
				}
			}
		}

		g.Log().Infof(ctx, "Manual 模式检查步骤完成状态: SagaId=%s, 已完成=%d, 预期=%d, 可提交=%t",
			sagaTransaction.SagaId, completedSteps, expectedSteps, canCommit)
	} else {
		return false, 0, 0, fmt.Errorf("未知的 StepIndexMode: %s", sagaTransaction.StepIndexMode)
	}

	return canCommit, completedSteps, expectedSteps, nil
}

// RollbackSagaTransaction 回滚分布式事务（统一接口）
// 支持自动执行补偿或仅标记状态，适用于Auto和Manual两种模式
// 实现幂等性：如果事务已经是 failed 状态，返回成功（幂等）；如果已经是 compensating 状态，返回当前状态信息（幂等）
func (s *SagaTransactionsService) RollbackSagaTransaction(ctx context.Context, input *model.RollbackSagaTransactionInput) (*model.RollbackSagaTransactionOutput, error) {
	var sagaTransaction *entity.SagaTransactions
	var startOutput *model.StartRollbackOutput
	var idempotentResult *model.RollbackSagaTransactionOutput

	// 使用单个事务确保从读取到更新的整个过程都是原子的
	err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 使用悲观锁查询 Saga 事务信息（与 ReportCompensation 保持一致，确保并发安全）
		var err error
		sagaTransaction, err = dao.SagaTransactions.LockAndGetSaga(ctx, tx, input.SagaId)
		if err != nil {
			return err
		}

		if sagaTransaction == nil {
			return fmt.Errorf("saga 事务不存在: sagaId=%s", input.SagaId)
		}

		// 2. 检查 Saga 事务状态 - 实现幂等性
		canContinue, statusResult, err := s.checkSagaStatusForRollback(ctx, sagaTransaction, input.SagaId)
		if err != nil {
			return err
		}
		if !canContinue {
			// 对于幂等性场景，不需要进行数据库操作，直接返回
			// 将结果保存到外部变量，事务结束后返回
			idempotentResult = statusResult
			return nil
		}

		if input.ExecutionMode == "" {
			input.ExecutionMode = consts.CompensationExecutionModeNone // 默认不执行
		}

		// 3. 开始回滚流程（在同一事务中）
		var internalErr error
		startOutput, internalErr = s.startRollbackWithTx(ctx, tx, &model.StartRollbackInput{
			SagaId:     input.SagaId,
			FailReason: input.FailReason,
			FailedStep: input.FailedStep,
		}, sagaTransaction)
		if internalErr != nil {
			return internalErr
		}

		return nil
	})

	if err != nil {
		g.Log().Errorf(ctx, "RollbackSagaTransaction 事务失败: SagaId=%s, 错误=%v", input.SagaId, err)
		return nil, err
	}

	// 如果是幂等性场景，直接返回状态检查结果
	if idempotentResult != nil {
		return idempotentResult, nil
	}

	if !startOutput.Success {
		return &model.RollbackSagaTransactionOutput{
			Success:    false,
			Message:    startOutput.Message,
			SagaId:     input.SagaId,
			NewStatus:  startOutput.NewStatus,
			StartedAt:  startOutput.StartedAt,
			FailReason: input.FailReason,
		}, nil
	}

	// 查询需要补偿的步骤（按step_index逆序）
	compensationSteps, err := s.queryCompensationSteps(ctx, input.SagaId, consts.CompensationStatusUninitialized)
	if err != nil {
		return nil, err
	}

	startOutput.CompensationSteps = compensationSteps
	startOutput.TotalStepsToRoll = len(compensationSteps)

	// 4. 根据执行模式处理补偿操作
	switch input.ExecutionMode {
	case consts.CompensationExecutionModeNone:
		// 不执行补偿，直接返回
		return &model.RollbackSagaTransactionOutput{
			Success:             true,
			Message:             "回滚已启动，等待执行补偿操作",
			SagaId:              input.SagaId,
			NewStatus:           consts.SagaStatusCompensating,
			IsRollbackCompleted: false,
			StartedAt:           startOutput.StartedAt,
			FailReason:          input.FailReason,
		}, nil

	case consts.CompensationExecutionModeSync:
		// 同步执行补偿操作
		return s.executeSyncCompensation(ctx, input, startOutput)

	case consts.CompensationExecutionModeAsync:
		// 异步执行补偿操作
		return s.executeAsyncCompensation(ctx, input, startOutput)

	default:
		return nil, fmt.Errorf("不支持的执行模式: %s", input.ExecutionMode)
	}
}

// startRollbackWithTx 在给定事务中开始回滚流程
func (s *SagaTransactionsService) startRollbackWithTx(ctx context.Context, tx gdb.TX, input *model.StartRollbackInput,
	sagaTransaction *entity.SagaTransactions) (*model.StartRollbackOutput, error) {
	// 参数验证
	if sagaTransaction == nil {
		return nil, fmt.Errorf("saga 事务不存在: sagaId=%s", input.SagaId)
	}

	// 1. 更新 Saga 状态为补偿中
	err := dao.SagaTransactions.UpdateStatusAndFailReasonWithTx(ctx, input.SagaId, consts.SagaStatusCompensating, input.FailReason, tx)
	if err != nil {
		return nil, fmt.Errorf("更新Saga状态失败: %w", err)
	}

	// 2. 批量更新指定状态的步骤补偿状态：从fromStatus改为pending
	affectedRows, err := dao.SagaSteps.BatchUpdateCompensationStatusWithTx(ctx, tx, input.SagaId,
		consts.CompensationStatusUninitialized, consts.CompensationStatusPending, "", 0)
	if err != nil {
		return nil, fmt.Errorf("批量更新步骤补偿状态失败: %w", err)
	}

	g.Log().Infof(ctx, "批量更新补偿状态成功: SagaId=%s, 从状态=%s 到状态=%s, 更新步骤数=%d",
		input.SagaId, consts.CompensationStatusUninitialized, consts.CompensationStatusPending, affectedRows)

	return &model.StartRollbackOutput{
		Success:   true,
		Message:   "回滚已启动",
		SagaId:    input.SagaId,
		NewStatus: consts.SagaStatusCompensating,
		StartedAt: gtime.Now(),
	}, nil
}

// CheckRollbackStatus 检查回滚状态
func (s *SagaTransactionsService) CheckRollbackStatus(ctx context.Context, input *model.CheckRollbackStatusInput) (*model.CheckRollbackStatusOutput, error) {
	// 1. 查询 Saga 事务信息
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, input.SagaId)
	if err != nil {
		return nil, err
	}

	if sagaTransaction == nil {
		return nil, fmt.Errorf("saga 事务不存在: sagaId=%s", input.SagaId)
	}

	// 2. 获取补偿统计信息
	stats, err := dao.SagaSteps.GetCompensationStatistics(ctx, input.SagaId)
	if err != nil {
		return nil, fmt.Errorf("获取补偿统计信息失败: %w", err)
	}

	// 3. 根据 Saga 状态判断回滚状态
	var isRollbackCompleted bool
	var isRollbackFailed bool

	switch sagaTransaction.SagaStatus {
	case consts.SagaStatusPending:
		// 待处理状态：还未开始执行，无回滚概念
		isRollbackCompleted = false
		isRollbackFailed = false

	case consts.SagaStatusRunning:
		// 运行中状态：正在执行，无回滚概念
		isRollbackCompleted = false
		isRollbackFailed = false

	case consts.SagaStatusCompleted:
		// 已完成状态：成功完成，无需回滚
		isRollbackCompleted = false
		isRollbackFailed = false

	case consts.SagaStatusCompensating:
		// 补偿中状态：正在进行回滚，需要检查补偿完成情况
		unfinishedCount := stats.Pending + stats.Running + stats.Delay
		if stats.Total == 0 {
			// 没有步骤需要补偿
			isRollbackCompleted = false
			isRollbackFailed = false
		} else if unfinishedCount == 0 && stats.Failed == 0 {
			// 所有补偿都已成功完成
			isRollbackCompleted = true
			isRollbackFailed = false
		} else if stats.Failed > 0 && unfinishedCount == 0 {
			// 有补偿失败，且没有待处理的补偿
			isRollbackCompleted = false
			isRollbackFailed = true
		} else {
			// 还有补偿正在进行中
			isRollbackCompleted = false
			isRollbackFailed = false
		}

	case consts.SagaStatusFailed:
		// 已失败状态：回滚已完成（无论成功还是失败）
		if stats.Total == 0 {
			// 没有步骤需要补偿，直接失败
			isRollbackCompleted = false
			isRollbackFailed = false
		} else if stats.Failed > 0 {
			// 有补偿失败
			isRollbackCompleted = false
			isRollbackFailed = true
		} else {
			// 所有补偿都成功完成
			isRollbackCompleted = true
			isRollbackFailed = false
		}

	default:
		// 未知状态
		isRollbackCompleted = false
		isRollbackFailed = false
	}
	// 4. 获取详细的补偿步骤信息
	compensationSteps, err := s.queryCompensationSteps(ctx, input.SagaId, consts.CompensationStatusUninitialized)
	if err != nil {
		return nil, fmt.Errorf("查询补偿步骤详情失败: %w", err)
	}

	return &model.CheckRollbackStatusOutput{
		Success:                true,
		Message:                "回滚状态检查完成",
		SagaId:                 input.SagaId,
		CurrentStatus:          sagaTransaction.SagaStatus,
		TotalStepsToRoll:       stats.Total,
		CompletedCompensations: stats.Completed,
		FailedCompensations:    stats.Failed,
		PendingCompensations:   stats.Pending,
		RunningCompensations:   stats.Running,
		DelayCompensations:     stats.Delay,
		CompensationStats:      stats,
		IsRollbackCompleted:    isRollbackCompleted,
		IsRollbackFailed:       isRollbackFailed,
		CompensationSteps:      compensationSteps,
		LastUpdatedAt:          sagaTransaction.UpdatedAt,
		FailReason:             sagaTransaction.FailReason,
	}, nil
}

// generateStepID 根据 sagaID、action、serviceName 生成确定性的 stepID
func generateStepID(sagaID, action, serviceName string) string {
	// 使用 sagaID + action + serviceName 的组合生成 MD5 哈希
	data := sagaID + "|" + action + "|" + serviceName
	hash := md5.Sum([]byte(data))
	return hex.EncodeToString(hash[:])
}

// createOrUpdateStep 创建或更新步骤，使用数据库唯一约束处理并发
func (s *SagaTransactionsService) createOrUpdateStep(ctx context.Context, tx gdb.TX,
	sagaTransaction *entity.SagaTransactions,
	input *model.ReportCompensationInput, stepStatus string) (*entity.SagaSteps, error) {
	// 1. 确定 step_index（原子操作）
	var stepIndex int
	if sagaTransaction.StepIndexMode == consts.StepIndexModeAuto {
		// Auto模式：原子性地分配递增的 step_index
		stepIndex = sagaTransaction.CurStepIndex + 1
		g.Log().Infof(ctx, "使用auto模式 StepIndex: SagaId=%s, Action=%s, 当前CurStepIndex=%d, 新StepIndex=%d",
			sagaTransaction.SagaId, input.Action, sagaTransaction.CurStepIndex, stepIndex)
	} else {
		// Manual模式：从模板中匹配 step_index
		var err error
		stepIndex, err = s.determineStepIndexFromTemplate(ctx, sagaTransaction, input.Action, input.ServiceName)
		if err != nil {
			return nil, fmt.Errorf("确定步骤索引失败: %w", err)
		}
	}

	// 2. 生成确定性的 stepID（基于 sagaID + action + serviceName 的 MD5 哈希）
	stepId := generateStepID(input.SagaId, input.Action, input.ServiceName)

	// 3. 构建步骤数据
	stepData := &entity.SagaSteps{
		StepId:              stepId,
		SagaId:              input.SagaId,
		Action:              input.Action,
		StepIndex:           stepIndex,
		ServiceName:         input.ServiceName,
		ContextData:         input.ContextData,
		CompensationContext: input.CompensationContext,
		CompensateEndpoint:  input.CompensateEndpoint,
		CompensationStatus:  stepStatus,
		LastError:           "",
		RetryCount:          0,
	}

	// 4. 使用 CreateOrUpdateStep 原子性地创建或更新步骤
	err := dao.SagaSteps.CreateOrUpdateStep(ctx, tx, stepData)
	if err != nil {
		g.Log().Errorf(ctx, "创建或更新步骤失败: SagaId=%s, Action=%s, Service=%s, 错误=%v",
			input.SagaId, input.Action, input.ServiceName, err)
		return nil, fmt.Errorf("创建或更新步骤失败: %w", err)
	}

	// 5. 获取最新的步骤记录（可能是新创建的或更新后的）
	dbStep, err := dao.SagaSteps.FindByActionAndService(ctx, input.SagaId, input.Action, input.ServiceName)
	if err != nil {
		g.Log().Errorf(ctx, "查询步骤记录失败: %v", err)
		return nil, fmt.Errorf("查询步骤记录失败: %w", err)
	}

	// 6. 更新 Saga 状态和步骤索引
	updateData := make(map[string]interface{})

	if consts.SagaStatusRunning != sagaTransaction.SagaStatus {
		updateData[dao.SagaTransactions.Columns().SagaStatus] = consts.SagaStatusRunning
	}

	// 更新步骤索引（仅对 auto 模式且为新步骤时有效）
	g.Log().Infof(ctx, "dbStep.StepIndex: %d, stepIndex: %d", dbStep.StepIndex, stepIndex)
	if sagaTransaction.StepIndexMode == consts.StepIndexModeAuto && dbStep.StepIndex == stepIndex {
		// 只有在创建新步骤时才更新 CurStepIndex
		updateData[dao.SagaTransactions.Columns().CurStepIndex] = stepIndex
		g.Log().Infof(ctx, "更新 CurStepIndex 为: %d", stepIndex)
	}

	if len(updateData) != 0 {
		updateData[dao.SagaTransactions.Columns().UpdatedAt] = gtime.Now()
		err = dao.SagaTransactions.UpdateWithTx(ctx, input.SagaId, tx, updateData)
		if err != nil {
			g.Log().Errorf(ctx, "更新 saga 状态和步骤索引失败: %v", err)
			return nil, fmt.Errorf("更新 saga 状态和步骤索引失败: %w", err)
		}
	}

	stepData.StepIndex = dbStep.StepIndex

	g.Log().Infof(ctx, "步骤创建或更新成功: SagaId=%s, Action=%s, Service=%s, StepId=%s, StepIndex=%d",
		input.SagaId, input.Action, input.ServiceName, stepData.StepId, stepData.StepIndex)

	return stepData, nil
}

// queryCompensationSteps 查询需要补偿的步骤（按step_index逆序）并转换为StepInfo格式
func (s *SagaTransactionsService) queryCompensationSteps(ctx context.Context, sagaId string, compensationStatus string) ([]model.StepInfo, error) {
	// 查询需要补偿的步骤（按step_index逆序）
	steps, err := dao.SagaSteps.FindStepsForRollback(ctx, sagaId, compensationStatus)
	if err != nil {
		return nil, fmt.Errorf("查询回滚步骤失败: %w", err)
	}

	// 转换为StepInfo格式
	compensationSteps := make([]model.StepInfo, 0, len(steps))
	for _, step := range steps {
		compensationSteps = append(compensationSteps, model.StepInfo{
			StepId:             step.StepId,
			StepIndex:          step.StepIndex,
			Action:             step.Action,
			ServiceName:        step.ServiceName,
			CompensateEndpoint: step.CompensateEndpoint,
			CompensationStatus: step.CompensationStatus,
		})
	}

	return compensationSteps, nil
}
