package service

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"saga/internal/model/entity"
)

// BaseOperation 基础操作结构
type BaseOperation struct {
	sagaService *SagaTransactionsService
}

// ReportOperation 上报操作
type ReportOperation struct {
	BaseOperation
	input            *model.ReportCompensationInput
	result           *model.ReportCompensationOutput
	compensationInfo *model.ExecuteCompensationInput
	lockStrategy     LockStrategy
}

// NewReportOperation 创建上报操作
func NewReportOperation(input *model.ReportCompensationInput, sagaService *SagaTransactionsService, lockStrategy LockStrategy) *ReportOperation {
	return &ReportOperation{
		BaseOperation: BaseOperation{sagaService: sagaService},
		input:         input,
		lockStrategy:  lockStrategy,
	}
}

func (r *ReportOperation) GetOperationName() string {
	return "ReportCompensation"
}

func (r *ReportOperation) RequiresTransaction() bool {
	return true
}

func (r *ReportOperation) Execute(ctx context.Context, saga *entity.SagaTransactions, tx gdb.TX) error {
	g.Log().Infof(ctx, "执行补偿上报操作: SagaId=%s, Action=%s, Service=%s",
		r.input.SagaId, r.input.Action, r.input.ServiceName)

	// 1. 检查Saga状态
	inCompensationWindow := r.sagaService.isInCompensationWindow(saga)
	allowProcessing := saga.SagaStatus == consts.SagaStatusPending ||
		saga.SagaStatus == consts.SagaStatusRunning ||
		(inCompensationWindow && (saga.SagaStatus == consts.SagaStatusFailed ||
			saga.SagaStatus == consts.SagaStatusCompensating))

	if !allowProcessing {
		// 处理不允许处理补偿信息的情况
		processResult, err := r.sagaService.handleDisallowedCompensationProcessing(ctx, r.input, saga)
		if err != nil {
			return err
		}
		if processResult != nil {
			r.result = processResult
			return nil
		}
	}

	// 2. 确定步骤状态
	stepStatus := consts.CompensationStatusUninitialized
	if inCompensationWindow && (saga.SagaStatus == consts.SagaStatusFailed ||
		saga.SagaStatus == consts.SagaStatusCompensating) {
		stepStatus = consts.CompensationStatusDelay
	}

	// 3. 创建或更新步骤
	var step *entity.SagaSteps
	var err error

	if r.lockStrategy.IsOptimistic() {
		step, err = r.createOrUpdateStepOptimistic(ctx, tx, saga, stepStatus)
	} else {
		step, err = r.createOrUpdateStepPessimistic(ctx, tx, saga, stepStatus)
	}

	if err != nil {
		return fmt.Errorf("创建或更新步骤失败: %w", err)
	}

	// 4. 处理补偿窗口期内的延迟补偿
	if stepStatus == consts.CompensationStatusDelay {
		r.compensationInfo = &model.ExecuteCompensationInput{
			SagaId:    r.input.SagaId,
			StepId:    step.StepId,
			StepIndex: step.StepIndex,
			Timeout:   r.sagaService.compensationTimeout,
		}
	}

	r.result = &model.ReportCompensationOutput{
		Success: true,
		Message: fmt.Sprintf("步骤创建或更新成功（%s）", r.lockStrategy.GetStrategyName()),
	}

	return nil
}

// createOrUpdateStepOptimistic 乐观锁版本的创建或更新步骤
func (r *ReportOperation) createOrUpdateStepOptimistic(ctx context.Context, tx gdb.TX, saga *entity.SagaTransactions, stepStatus string) (*entity.SagaSteps, error) {
	// 确定步骤索引
	stepIndex, err := r.determineStepIndex(ctx, saga)
	if err != nil {
		return nil, err
	}

	// 创建或更新步骤
	step, err := r.createOrUpdateStep(ctx, tx, saga, stepStatus, stepIndex)
	if err != nil {
		return nil, err
	}

	// 使用乐观锁更新Saga状态
	return r.updateSagaWithOptimisticLock(ctx, saga, step)
}

// createOrUpdateStepPessimistic 悲观锁版本的创建或更新步骤
func (r *ReportOperation) createOrUpdateStepPessimistic(ctx context.Context, tx gdb.TX, saga *entity.SagaTransactions, stepStatus string) (*entity.SagaSteps, error) {
	// 确定步骤索引
	stepIndex, err := r.determineStepIndex(ctx, saga)
	if err != nil {
		return nil, err
	}

	// 创建或更新步骤
	step, err := r.createOrUpdateStep(ctx, tx, saga, stepStatus, stepIndex)
	if err != nil {
		return nil, err
	}

	// 更新Saga状态
	return r.updateSagaWithPessimisticLock(ctx, tx, saga, step)
}

// determineStepIndex 确定步骤索引
func (r *ReportOperation) determineStepIndex(ctx context.Context, saga *entity.SagaTransactions) (int, error) {
	if saga.StepIndexMode == consts.StepIndexModeAuto {
		return saga.CurStepIndex + 1, nil
	}

	// Manual模式：从模板中确定索引
	return r.sagaService.determineStepIndexFromTemplate(ctx, saga, r.input.Action, r.input.ServiceName)
}

// createOrUpdateStep 创建或更新步骤
func (r *ReportOperation) createOrUpdateStep(ctx context.Context, tx gdb.TX, saga *entity.SagaTransactions, stepStatus string, stepIndex int) (*entity.SagaSteps, error) {
	// 生成确定性的stepID
	stepId := generateStepID(r.input.SagaId, r.input.Action, r.input.ServiceName)

	// 构建步骤数据
	stepData := &entity.SagaSteps{
		StepId:              stepId,
		SagaId:              r.input.SagaId,
		Action:              r.input.Action,
		StepIndex:           stepIndex,
		ServiceName:         r.input.ServiceName,
		ContextData:         r.input.ContextData,
		CompensationContext: r.input.CompensationContext,
		CompensateEndpoint:  r.input.CompensateEndpoint,
		CompensationStatus:  stepStatus,
		LastError:           "",
		RetryCount:          0,
	}

	// 创建或更新步骤
	err := dao.SagaSteps.CreateOrUpdateStep(ctx, tx, stepData)
	if err != nil {
		return nil, fmt.Errorf("创建或更新步骤失败: %w", err)
	}

	// 查询创建的步骤
	dbStep, err := dao.SagaSteps.FindByActionAndService(ctx, r.input.SagaId, r.input.Action, r.input.ServiceName)
	if err != nil {
		return nil, fmt.Errorf("查询步骤记录失败: %w", err)
	}

	stepData.StepIndex = dbStep.StepIndex
	return stepData, nil
}

// updateSagaWithOptimisticLock 使用乐观锁更新Saga状态
func (r *ReportOperation) updateSagaWithOptimisticLock(ctx context.Context, saga *entity.SagaTransactions, step *entity.SagaSteps) (*entity.SagaSteps, error) {
	// 准备更新数据
	updateData := make(map[string]interface{})
	updateData[dao.SagaTransactions.Columns().UpdatedAt] = gtime.Now()

	// 更新状态
	if consts.SagaStatusRunning != saga.SagaStatus {
		updateData[dao.SagaTransactions.Columns().SagaStatus] = consts.SagaStatusRunning
	}

	// 更新步骤索引（仅对auto模式有效）
	originalVersion := saga.CurStepIndex
	newStepIndex := originalVersion
	if saga.StepIndexMode == consts.StepIndexModeAuto {
		newStepIndex = step.StepIndex
		updateData[dao.SagaTransactions.Columns().CurStepIndex] = step.StepIndex
	}

	// 使用乐观锁更新
	success, err := dao.SagaTransactions.UpdateWithVersionCheck(ctx, r.input.SagaId, originalVersion, updateData)
	if err != nil {
		return nil, err
	}

	if !success {
		return nil, fmt.Errorf("version_conflict: 乐观锁版本冲突, SagaId=%s, 期望版本=%d", r.input.SagaId, originalVersion)
	}

	g.Log().Infof(ctx, "乐观锁更新成功: SagaId=%s, 版本 %d -> %d", r.input.SagaId, originalVersion, newStepIndex)
	return step, nil
}

// updateSagaWithPessimisticLock 使用悲观锁更新Saga状态
func (r *ReportOperation) updateSagaWithPessimisticLock(ctx context.Context, tx gdb.TX, saga *entity.SagaTransactions, step *entity.SagaSteps) (*entity.SagaSteps, error) {
	updateData := make(map[string]interface{})
	updateData[dao.SagaTransactions.Columns().UpdatedAt] = gtime.Now()

	if consts.SagaStatusRunning != saga.SagaStatus {
		updateData[dao.SagaTransactions.Columns().SagaStatus] = consts.SagaStatusRunning
	}

	// 更新步骤索引（仅对auto模式且为新步骤时有效）
	if saga.StepIndexMode == consts.StepIndexModeAuto && step.StepIndex > saga.CurStepIndex {
		updateData[dao.SagaTransactions.Columns().CurStepIndex] = step.StepIndex
	}

	if len(updateData) > 0 {
		updateData[dao.SagaTransactions.Columns().UpdatedAt] = gtime.Now()
		err := dao.SagaTransactions.UpdateWithTx(ctx, r.input.SagaId, tx, updateData)
		if err != nil {
			return nil, fmt.Errorf("更新saga状态和步骤索引失败: %w", err)
		}
	}

	return step, nil
}

// GetResult 获取操作结果
func (r *ReportOperation) GetResult() *model.ReportCompensationOutput {
	return r.result
}

// GetCompensationInfo 获取补偿信息
func (r *ReportOperation) GetCompensationInfo() *model.ExecuteCompensationInput {
	return r.compensationInfo
}

// CommitOperation 提交操作
type CommitOperation struct {
	BaseOperation
	input  *model.CommitSagaTransactionInput
	result *model.CommitSagaTransactionOutput
}

// NewCommitOperation 创建提交操作
func NewCommitOperation(input *model.CommitSagaTransactionInput, sagaService *SagaTransactionsService) *CommitOperation {
	return &CommitOperation{
		BaseOperation: BaseOperation{sagaService: sagaService},
		input:         input,
	}
}

func (c *CommitOperation) GetOperationName() string {
	return "CommitSagaTransaction"
}

func (c *CommitOperation) RequiresTransaction() bool {
	return true
}

func (c *CommitOperation) Execute(ctx context.Context, saga *entity.SagaTransactions, tx gdb.TX) error {
	g.Log().Infof(ctx, "执行事务提交操作: SagaId=%s", c.input.SagaId)

	// 1. 检查Saga事务状态
	canContinue, statusResult, err := c.sagaService.checkSagaStatusForCommit(ctx, saga, c.input.SagaId)
	if err != nil {
		return err
	}
	if !canContinue {
		c.result = statusResult
		return nil
	}

	// 2. 检查所有步骤是否已完成
	canCommit, completedSteps, expectedSteps, err := c.sagaService.checkAllStepsCompleted(ctx, saga)
	if err != nil {
		return fmt.Errorf("检查步骤完成状态失败: %w", err)
	}

	if !canCommit {
		c.result = &model.CommitSagaTransactionOutput{
			Success:        false,
			Message:        fmt.Sprintf("无法提交事务，还有步骤未完成，已完成: %d/%d", completedSteps, expectedSteps),
			SagaId:         c.input.SagaId,
			CompletedSteps: completedSteps,
			ExpectedSteps:  expectedSteps,
			NewStatus:      saga.SagaStatus,
			CompletedAt:    gtime.Now(),
		}
		return nil
	}

	// 3. 更新Saga状态为completed
	err = dao.SagaTransactions.UpdateStatusWithTx(ctx, c.input.SagaId, consts.SagaStatusCompleted, tx)
	if err != nil {
		return fmt.Errorf("更新Saga状态失败: %w", err)
	}

	c.result = &model.CommitSagaTransactionOutput{
		Success:        true,
		Message:        "分布式事务提交成功",
		SagaId:         c.input.SagaId,
		CompletedSteps: completedSteps,
		ExpectedSteps:  expectedSteps,
		NewStatus:      consts.SagaStatusCompleted,
		CompletedAt:    gtime.Now(),
	}

	g.Log().Infof(ctx, "Saga事务提交成功: SagaId=%s, 完成步骤数=%d", c.input.SagaId, completedSteps)
	return nil
}

// GetResult 获取操作结果
func (c *CommitOperation) GetResult() *model.CommitSagaTransactionOutput {
	return c.result
}

// RollbackOperation 回滚操作
type RollbackOperation struct {
	BaseOperation
	input  *model.RollbackSagaTransactionInput
	result *model.RollbackSagaTransactionOutput
}

// NewRollbackOperation 创建回滚操作
func NewRollbackOperation(input *model.RollbackSagaTransactionInput, sagaService *SagaTransactionsService) *RollbackOperation {
	return &RollbackOperation{
		BaseOperation: BaseOperation{sagaService: sagaService},
		input:         input,
	}
}

func (r *RollbackOperation) GetOperationName() string {
	return "RollbackSagaTransaction"
}

func (r *RollbackOperation) RequiresTransaction() bool {
	return true
}

func (r *RollbackOperation) Execute(ctx context.Context, saga *entity.SagaTransactions, tx gdb.TX) error {
	g.Log().Infof(ctx, "执行事务回滚操作: SagaId=%s", r.input.SagaId)

	// 1. 检查Saga事务状态
	canContinue, statusResult, err := r.sagaService.checkSagaStatusForRollback(ctx, saga, r.input.SagaId)
	if err != nil {
		return err
	}
	if !canContinue {
		r.result = statusResult
		return nil
	}

	// 2. 设置默认执行模式
	if r.input.ExecutionMode == "" {
		r.input.ExecutionMode = consts.CompensationExecutionModeNone
	}

	// 3. 开始回滚流程
	startOutput, err := r.sagaService.startRollbackWithTx(ctx, tx, &model.StartRollbackInput{
		SagaId:     r.input.SagaId,
		FailReason: r.input.FailReason,
		FailedStep: r.input.FailedStep,
	}, saga)
	if err != nil {
		return err
	}

	if !startOutput.Success {
		r.result = &model.RollbackSagaTransactionOutput{
			Success:    false,
			Message:    startOutput.Message,
			SagaId:     r.input.SagaId,
			NewStatus:  startOutput.NewStatus,
			StartedAt:  startOutput.StartedAt,
			FailReason: r.input.FailReason,
		}
		return nil
	}

	// 4. 查询需要补偿的步骤
	compensationSteps, err := r.sagaService.queryCompensationSteps(ctx, r.input.SagaId, consts.CompensationStatusUninitialized)
	if err != nil {
		return err
	}

	startOutput.CompensationSteps = compensationSteps
	startOutput.TotalStepsToRoll = len(compensationSteps)

	// 5. 根据执行模式处理
	switch r.input.ExecutionMode {
	case consts.CompensationExecutionModeNone:
		r.result = &model.RollbackSagaTransactionOutput{
			Success:             true,
			Message:             "回滚已启动，等待执行补偿操作",
			SagaId:              r.input.SagaId,
			NewStatus:           consts.SagaStatusCompensating,
			IsRollbackCompleted: false,
			StartedAt:           startOutput.StartedAt,
			FailReason:          r.input.FailReason,
		}
	case consts.CompensationExecutionModeSync:
		// 同步执行补偿操作（在事务外执行）
		r.result = &model.RollbackSagaTransactionOutput{
			Success:             true,
			Message:             "回滚已启动，将同步执行补偿操作",
			SagaId:              r.input.SagaId,
			NewStatus:           consts.SagaStatusCompensating,
			IsRollbackCompleted: false,
			StartedAt:           startOutput.StartedAt,
			FailReason:          r.input.FailReason,
		}
	case consts.CompensationExecutionModeAsync:
		// 异步执行补偿操作（在事务外执行）
		r.result = &model.RollbackSagaTransactionOutput{
			Success:             true,
			Message:             "回滚已启动，将异步执行补偿操作",
			SagaId:              r.input.SagaId,
			NewStatus:           consts.SagaStatusCompensating,
			IsRollbackCompleted: false,
			StartedAt:           startOutput.StartedAt,
			FailReason:          r.input.FailReason,
		}
	default:
		return fmt.Errorf("不支持的执行模式: %s", r.input.ExecutionMode)
	}

	return nil
}

// GetResult 获取操作结果
func (r *RollbackOperation) GetResult() *model.RollbackSagaTransactionOutput {
	return r.result
}

// GetOperation 查询操作（不需要锁）
type GetOperation struct {
	BaseOperation
	input  *model.GetSagaTransactionInput
	result *model.GetSagaTransactionOutput
}

// NewGetOperation 创建查询操作
func NewGetOperation(input *model.GetSagaTransactionInput, sagaService *SagaTransactionsService) *GetOperation {
	return &GetOperation{
		BaseOperation: BaseOperation{sagaService: sagaService},
		input:         input,
	}
}

func (g *GetOperation) GetOperationName() string {
	return "GetSagaTransaction"
}

func (g *GetOperation) RequiresTransaction() bool {
	return false
}

func (g *GetOperation) Execute(ctx context.Context, saga *entity.SagaTransactions, tx gdb.TX) error {
	g.result = &model.GetSagaTransactionOutput{
		SagaId:      saga.SagaId,
		Name:        saga.Name,
		Status:      saga.SagaStatus,
		CurrentStep: fmt.Sprintf("%d", saga.CurStepIndex),
		RetryCount:  0,
		CreatedAt:   saga.CreatedAt,
		UpdatedAt:   saga.UpdatedAt,
	}
	return nil
}

// GetResult 获取操作结果
func (g *GetOperation) GetResult() *model.GetSagaTransactionOutput {
	return g.result
}
