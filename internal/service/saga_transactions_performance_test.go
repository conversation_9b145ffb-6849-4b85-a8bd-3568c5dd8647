package service

import (
	"context"
	"fmt"
	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"saga/internal/model/entity"
	"sync"
	"testing"
	"time"

	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/guid"
	"github.com/stretchr/testify/assert"
)

// TestReportCompensationPerformanceComparison 性能对比测试：乐观锁 vs 悲观锁
func TestReportCompensationPerformanceComparison(t *testing.T) {
	ctx := context.Background()
	service := NewSagaTransactions()

	// 测试参数
	concurrentCount := 10       // 并发数
	operationsPerGoroutine := 5 // 每个协程的操作数

	t.Run("悲观锁性能测试", func(t *testing.T) {
		sagaID := fmt.Sprintf("perf-pessimistic-%s", guid.S()[:8])
		defer cleanupTestData(t, sagaID)

		// 创建测试事务
		createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 0, consts.SagaStatusPending)

		// 性能测试
		startTime := time.Now()
		var wg sync.WaitGroup
		var successCount int64
		var errorCount int64
		var mu sync.Mutex

		for i := 0; i < concurrentCount; i++ {
			wg.Add(1)
			go func(goroutineID int) {
				defer wg.Done()

				for j := 0; j < operationsPerGoroutine; j++ {
					input := &model.ReportCompensationInput{
						SagaId:              sagaID,
						Action:              fmt.Sprintf("action_%d_%d", goroutineID, j),
						ServiceName:         fmt.Sprintf("service_%d", goroutineID),
						ContextData:         fmt.Sprintf(`{"goroutine": %d, "operation": %d}`, goroutineID, j),
						CompensationContext: fmt.Sprintf(`{"compensate": "data_%d_%d"}`, goroutineID, j),
						CompensateEndpoint:  fmt.Sprintf("http://service-%d/compensate", goroutineID),
					}

					// 使用悲观锁版本
					_, err := service.ReportCompensation(ctx, input)

					mu.Lock()
					if err != nil {
						errorCount++
						t.Logf("悲观锁操作失败: %v", err)
					} else {
						successCount++
					}
					mu.Unlock()
				}
			}(i)
		}

		wg.Wait()
		pessimisticDuration := time.Since(startTime)

		t.Logf("悲观锁性能结果:")
		t.Logf("  总耗时: %v", pessimisticDuration)
		t.Logf("  成功操作: %d", successCount)
		t.Logf("  失败操作: %d", errorCount)
		t.Logf("  总操作数: %d", concurrentCount*operationsPerGoroutine)
		t.Logf("  平均每操作耗时: %v", pessimisticDuration/time.Duration(concurrentCount*operationsPerGoroutine))
		t.Logf("  QPS: %.2f", float64(successCount)/pessimisticDuration.Seconds())

		// 验证数据一致性
		verifyDataConsistency(t, sagaID, int(successCount))
	})

	t.Run("乐观锁性能测试", func(t *testing.T) {
		sagaID := fmt.Sprintf("perf-optimistic-%s", guid.S()[:8])
		defer cleanupTestData(t, sagaID)

		// 创建测试事务
		createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 0, consts.SagaStatusPending)

		// 性能测试
		startTime := time.Now()
		var wg sync.WaitGroup
		var successCount int64
		var errorCount int64
		var retryCount int64
		var mu sync.Mutex

		for i := 0; i < concurrentCount; i++ {
			wg.Add(1)
			go func(goroutineID int) {
				defer wg.Done()

				for j := 0; j < operationsPerGoroutine; j++ {
					input := &model.ReportCompensationInput{
						SagaId:              sagaID,
						Action:              fmt.Sprintf("action_%d_%d", goroutineID, j),
						ServiceName:         fmt.Sprintf("service_%d", goroutineID),
						ContextData:         fmt.Sprintf(`{"goroutine": %d, "operation": %d}`, goroutineID, j),
						CompensationContext: fmt.Sprintf(`{"compensate": "data_%d_%d"}`, goroutineID, j),
						CompensateEndpoint:  fmt.Sprintf("http://service-%d/compensate", goroutineID),
					}

					// 使用乐观锁版本
					_, err := service.ReportCompensationWithOptimisticLock(ctx, input)

					mu.Lock()
					if err != nil {
						errorCount++
						if contains(err.Error(), "乐观锁重试失败") {
							retryCount++
						}
						t.Logf("乐观锁操作失败: %v", err)
					} else {
						successCount++
					}
					mu.Unlock()
				}
			}(i)
		}

		wg.Wait()
		optimisticDuration := time.Since(startTime)

		t.Logf("乐观锁性能结果:")
		t.Logf("  总耗时: %v", optimisticDuration)
		t.Logf("  成功操作: %d", successCount)
		t.Logf("  失败操作: %d", errorCount)
		t.Logf("  重试失败: %d", retryCount)
		t.Logf("  总操作数: %d", concurrentCount*operationsPerGoroutine)
		t.Logf("  平均每操作耗时: %v", optimisticDuration/time.Duration(concurrentCount*operationsPerGoroutine))
		t.Logf("  QPS: %.2f", float64(successCount)/optimisticDuration.Seconds())

		// 验证数据一致性
		verifyDataConsistency(t, sagaID, int(successCount))
	})
}

// TestOptimisticLockRetryMechanism 测试乐观锁重试机制
func TestOptimisticLockRetryMechanism(t *testing.T) {
	ctx := context.Background()
	service := NewSagaTransactions()
	sagaID := fmt.Sprintf("retry-test-%s", guid.S()[:8])
	defer cleanupTestData(t, sagaID)

	// 创建测试事务
	createRealTestSagaTransaction(sagaID, consts.StepIndexModeAuto, "", 0, consts.SagaStatusPending)

	// 高并发测试，增加版本冲突概率
	concurrentCount := 20
	var wg sync.WaitGroup
	var successCount int64
	var errorCount int64
	var mu sync.Mutex

	startTime := time.Now()

	for i := 0; i < concurrentCount; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			input := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              fmt.Sprintf("concurrent_action_%d", goroutineID),
				ServiceName:         fmt.Sprintf("service_%d", goroutineID),
				ContextData:         fmt.Sprintf(`{"goroutine": %d}`, goroutineID),
				CompensationContext: fmt.Sprintf(`{"compensate": "data_%d"}`, goroutineID),
				CompensateEndpoint:  fmt.Sprintf("http://service-%d/compensate", goroutineID),
			}

			_, err := service.ReportCompensationWithOptimisticLock(ctx, input)

			mu.Lock()
			if err != nil {
				errorCount++
				t.Logf("并发操作失败: %v", err)
			} else {
				successCount++
			}
			mu.Unlock()
		}(i)
	}

	wg.Wait()
	duration := time.Since(startTime)

	t.Logf("乐观锁重试机制测试结果:")
	t.Logf("  总耗时: %v", duration)
	t.Logf("  成功操作: %d", successCount)
	t.Logf("  失败操作: %d", errorCount)
	t.Logf("  成功率: %.2f%%", float64(successCount)/float64(concurrentCount)*100)

	// 验证最终的 CurStepIndex 是否正确
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	assert.NoError(t, err)
	assert.NotNil(t, sagaTransaction)

	expectedStepIndex := int(successCount)
	assert.Equal(t, expectedStepIndex, sagaTransaction.CurStepIndex,
		"CurStepIndex 应该等于成功操作的数量")

	t.Logf("  最终 CurStepIndex: %d (期望: %d)", sagaTransaction.CurStepIndex, expectedStepIndex)
}

// 辅助函数已在 saga_transactions_test_helpers.go 中定义

func verifyDataConsistency(t *testing.T, sagaID string, expectedStepCount int) {
	ctx := context.Background()

	// 验证步骤数量
	steps, err := dao.SagaSteps.FindBySagaId(ctx, sagaID)
	assert.NoError(t, err)
	assert.Equal(t, expectedStepCount, len(steps), "步骤数量不匹配")

	// 验证 CurStepIndex
	sagaTransaction, err := dao.SagaTransactions.FindBySagaId(ctx, sagaID)
	assert.NoError(t, err)
	assert.NotNil(t, sagaTransaction)
	assert.Equal(t, expectedStepCount, sagaTransaction.CurStepIndex, "CurStepIndex 不匹配")

	// 验证步骤索引的连续性
	stepIndexes := make(map[int]bool)
	for _, step := range steps {
		stepIndexes[step.StepIndex] = true
	}

	for i := 1; i <= expectedStepCount; i++ {
		assert.True(t, stepIndexes[i], fmt.Sprintf("缺少步骤索引 %d", i))
	}
}

// contains 函数已在 saga_transactions_test_helpers.go 中定义

// BenchmarkReportCompensationPessimistic 悲观锁基准测试
func BenchmarkReportCompensationPessimistic(b *testing.B) {
	ctx := context.Background()
	service := NewSagaTransactions()
	sagaID := fmt.Sprintf("bench-pessimistic-%s", guid.S()[:8])

	// 创建测试事务
	transaction := &entity.SagaTransactions{
		SagaId:        sagaID,
		SagaStatus:    consts.SagaStatusPending,
		Name:          "benchmark-saga",
		StepIndexMode: consts.StepIndexModeAuto,
		StepTemplates: "",
		CurStepIndex:  0,
		CreatedAt:     gtime.Now(),
		UpdatedAt:     gtime.Now(),
	}
	dao.SagaTransactions.Insert(ctx, transaction)
	defer cleanupTestData(nil, sagaID)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			input := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              fmt.Sprintf("bench_action_%d", i),
				ServiceName:         fmt.Sprintf("bench_service_%d", i%10),
				ContextData:         fmt.Sprintf(`{"benchmark": %d}`, i),
				CompensationContext: fmt.Sprintf(`{"compensate": "bench_%d"}`, i),
				CompensateEndpoint:  fmt.Sprintf("http://bench-service/compensate/%d", i),
			}

			_, err := service.ReportCompensation(ctx, input)
			if err != nil {
				b.Logf("悲观锁基准测试失败: %v", err)
			}
			i++
		}
	})
}

// BenchmarkReportCompensationOptimistic 乐观锁基准测试
func BenchmarkReportCompensationOptimistic(b *testing.B) {
	ctx := context.Background()
	service := NewSagaTransactions()
	sagaID := fmt.Sprintf("bench-optimistic-%s", guid.S()[:8])

	// 创建测试事务
	transaction := &entity.SagaTransactions{
		SagaId:        sagaID,
		SagaStatus:    consts.SagaStatusPending,
		Name:          "benchmark-saga",
		StepIndexMode: consts.StepIndexModeAuto,
		StepTemplates: "",
		CurStepIndex:  0,
		CreatedAt:     gtime.Now(),
		UpdatedAt:     gtime.Now(),
	}
	dao.SagaTransactions.Insert(ctx, transaction)
	defer cleanupTestData(nil, sagaID)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			input := &model.ReportCompensationInput{
				SagaId:              sagaID,
				Action:              fmt.Sprintf("bench_action_%d", i),
				ServiceName:         fmt.Sprintf("bench_service_%d", i%10),
				ContextData:         fmt.Sprintf(`{"benchmark": %d}`, i),
				CompensationContext: fmt.Sprintf(`{"compensate": "bench_%d"}`, i),
				CompensateEndpoint:  fmt.Sprintf("http://bench-service/compensate/%d", i),
			}

			_, err := service.ReportCompensationWithOptimisticLock(ctx, input)
			if err != nil {
				b.Logf("乐观锁基准测试失败: %v", err)
			}
			i++
		}
	})
}
