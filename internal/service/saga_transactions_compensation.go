package service

import (
	"context"
	"fmt"
	"math"
	"net"
	"net/url"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"saga/internal/consts"
	"saga/internal/dao"
	"saga/internal/model"
	"saga/internal/model/entity"
)

// executeSyncCompensation 同步执行补偿操作
func (s *SagaTransactionsService) executeSyncCompensation(ctx context.Context, input *model.RollbackSagaTransactionInput, startOutput *model.StartRollbackOutput) (*model.RollbackSagaTransactionOutput, error) {
	completedCount := 0
	failedCount := 0

	for _, stepInfo := range startOutput.CompensationSteps {
		g.Log().Infof(ctx, "开始执行补偿: SagaId=%s, StepId=%s, StepIndex=%d, Action=%s, Service=%s",
			input.SagaId, stepInfo.StepId, stepInfo.StepIndex, stepInfo.Action, stepInfo.ServiceName)

		executeOutput, err := s.ExecuteCompensation(ctx, &model.ExecuteCompensationInput{
			SagaId:    input.SagaId,
			StepId:    stepInfo.StepId,
			StepIndex: stepInfo.StepIndex,
			Timeout:   s.compensationTimeout,
		})

		if err != nil {
			g.Log().Errorf(ctx, "执行补偿失败: SagaId=%s, StepId=%s, StepIndex=%d, Error=%v",
				input.SagaId, stepInfo.StepId, stepInfo.StepIndex, err)
			failedCount++
			continue
		}

		if executeOutput.Success {
			completedCount++
			g.Log().Infof(ctx, "补偿执行成功: SagaId=%s, StepId=%s, StepIndex=%d", input.SagaId, stepInfo.StepId, stepInfo.StepIndex)
		} else {
			failedCount++
			g.Log().Errorf(ctx, "补偿执行失败: SagaId=%s, StepId=%s, StepIndex=%d, Message=%s",
				input.SagaId, stepInfo.StepId, stepInfo.StepIndex, executeOutput.Message)
		}
	}

	// 检查回滚状态并更新最终状态
	pendingCount := startOutput.TotalStepsToRoll - completedCount - failedCount
	isCompleted := pendingCount == 0 && failedCount == 0

	var finalStatus string
	var completedAt *gtime.Time

	if isCompleted {
		finalStatus = consts.SagaStatusFailed // 所有补偿完成，事务最终状态为失败
		completedAt = gtime.Now()
		err := dao.SagaTransactions.UpdateStatus(ctx, input.SagaId, finalStatus)
		if err != nil {
			g.Log().Errorf(ctx, "更新最终状态失败: %v", err)
		}
		g.Log().Infof(ctx, "回滚完成: SagaId=%s, 状态=%s", input.SagaId, finalStatus)
	} else if failedCount > 0 {
		finalStatus = consts.SagaStatusCompensating // 有补偿失败，保持补偿中状态
		g.Log().Warningf(ctx, "回滚部分失败: SagaId=%s, 已完成=%d, 失败=%d, 待处理=%d",
			input.SagaId, completedCount, failedCount, pendingCount)
	} else {
		finalStatus = consts.SagaStatusCompensating // 还有待处理的补偿
	}

	return &model.RollbackSagaTransactionOutput{
		Success:             true,
		Message:             "回滚流程已执行",
		SagaId:              input.SagaId,
		NewStatus:           finalStatus,
		IsRollbackCompleted: isCompleted,
		StartedAt:           startOutput.StartedAt,
		CompletedAt:         completedAt,
		FailReason:          input.FailReason,
	}, nil
}

// executeAsyncCompensation 异步执行补偿操作
func (s *SagaTransactionsService) executeAsyncCompensation(ctx context.Context, input *model.RollbackSagaTransactionInput, startOutput *model.StartRollbackOutput) (*model.RollbackSagaTransactionOutput, error) {
	// 立即返回，表示异步处理已启动
	output := &model.RollbackSagaTransactionOutput{
		Success:             true,
		Message:             "回滚已启动，正在异步执行补偿操作",
		SagaId:              input.SagaId,
		NewStatus:           consts.SagaStatusCompensating,
		IsRollbackCompleted: false,
		StartedAt:           startOutput.StartedAt,
		FailReason:          input.FailReason,
	}

	// 在新的goroutine中异步执行补偿（但补偿操作本身是串行的）
	go func() {
		// 创建新的context，复制原始context的值但避免被取消影响异步操作
		asyncCtx := context.WithoutCancel(ctx)

		completedCount := 0
		failedCount := 0

		// 串行执行补偿操作，按照step_index逆序（与同步模式保持一致）
		for _, stepInfo := range startOutput.CompensationSteps {
			g.Log().Infof(asyncCtx, "开始异步执行补偿: SagaId=%s, StepId=%s, StepIndex=%d, Action=%s, Service=%s",
				input.SagaId, stepInfo.StepId, stepInfo.StepIndex, stepInfo.Action, stepInfo.ServiceName)

			executeOutput, err := s.ExecuteCompensation(asyncCtx, &model.ExecuteCompensationInput{
				SagaId:    input.SagaId,
				StepId:    stepInfo.StepId,
				StepIndex: stepInfo.StepIndex,
				Timeout:   s.compensationTimeout,
			})

			if err != nil {
				g.Log().Errorf(asyncCtx, "异步执行补偿失败: SagaId=%s, StepId=%s, StepIndex=%d, Error=%v",
					input.SagaId, stepInfo.StepId, stepInfo.StepIndex, err)
				failedCount++
				continue
			}

			if executeOutput.Success {
				completedCount++
				g.Log().Infof(asyncCtx, "异步补偿执行成功: SagaId=%s, StepId=%s, StepIndex=%d", input.SagaId, stepInfo.StepId, stepInfo.StepIndex)
			} else {
				failedCount++
				g.Log().Errorf(asyncCtx, "异步补偿执行失败: SagaId=%s, StepId=%s, StepIndex=%d, Message=%s",
					input.SagaId, stepInfo.StepId, stepInfo.StepIndex, executeOutput.Message)
			}
		}

		// 更新最终状态
		pendingCount := startOutput.TotalStepsToRoll - completedCount - failedCount
		isCompleted := pendingCount == 0 && failedCount == 0

		var finalStatus string
		if isCompleted {
			finalStatus = consts.SagaStatusFailed // 所有补偿完成，事务最终状态为失败
			err := dao.SagaTransactions.UpdateStatus(asyncCtx, input.SagaId, finalStatus)
			if err != nil {
				g.Log().Errorf(asyncCtx, "更新最终状态失败: %v", err)
			}
			g.Log().Infof(asyncCtx, "异步回滚完成: SagaId=%s, 状态=%s", input.SagaId, finalStatus)
		} else if failedCount > 0 {
			finalStatus = consts.SagaStatusCompensating // 有补偿失败，保持补偿中状态
			g.Log().Warningf(asyncCtx, "异步回滚部分失败: SagaId=%s, 已完成=%d, 失败=%d, 待处理=%d",
				input.SagaId, completedCount, failedCount, pendingCount)
		} else {
			finalStatus = consts.SagaStatusCompensating // 还有待处理的补偿
		}
	}()

	return output, nil
}

// ExecuteCompensation 执行单个步骤的补偿操作
func (s *SagaTransactionsService) ExecuteCompensation(ctx context.Context, input *model.ExecuteCompensationInput) (*model.ExecuteCompensationOutput, error) {
	// 1. 查询步骤信息
	step, err := dao.SagaSteps.FindStepById(ctx, input.StepId)
	if err != nil {
		return nil, err
	}

	// 2. 检查补偿状态
	if step.CompensationStatus == consts.CompensationStatusCompleted {
		return &model.ExecuteCompensationOutput{
			StepId:             input.StepId,
			Success:            true,
			Message:            "补偿已完成",
			SagaId:             input.SagaId,
			StepIndex:          input.StepIndex,
			Action:             step.Action,
			ServiceName:        step.ServiceName,
			CompensateEndpoint: step.CompensateEndpoint,
			CompensationStatus: step.CompensationStatus,
			RetryCount:         step.RetryCount,
			ExecutedAt:         gtime.Now(),
		}, nil
	}

	// 3. 更新补偿状态为运行中
	err = dao.SagaSteps.UpdateCompensationStatus(ctx, input.StepId, consts.CompensationStatusRunning, "", step.RetryCount)
	if err != nil {
		return nil, fmt.Errorf("更新补偿状态失败: %w", err)
	}

	g.Log().Infof(ctx, "开始执行补偿: SagaId=%s, StepIndex=%d, Action=%s, Endpoint=%s",
		input.SagaId, input.StepIndex, step.Action, step.CompensateEndpoint)

	// 4. 调用补偿服务
	compensationSuccess, compensationErrMsg, actualRetryCount := s.compensationCall(ctx, step, input.Timeout)

	var newStatus string
	var lastError string
	var finalRetryCount int

	if compensationSuccess {
		newStatus = consts.CompensationStatusCompleted
		lastError = ""
		finalRetryCount = step.RetryCount + actualRetryCount
		g.Log().Infof(ctx, "补偿执行成功: StepId=%s, SagaId=%s, StepIndex=%d", input.StepId, input.SagaId, input.StepIndex)
	} else {
		newStatus = consts.CompensationStatusFailed
		lastError = compensationErrMsg
		finalRetryCount = step.RetryCount + actualRetryCount
		g.Log().Errorf(ctx, "补偿执行失败: StepId=%s, SagaId=%s, StepIndex=%d, Error=%s", input.StepId, input.SagaId, input.StepIndex, compensationErrMsg)
	}

	// 5. 更新最终状态
	err = dao.SagaSteps.UpdateCompensationStatus(ctx, input.StepId, newStatus, lastError, finalRetryCount)
	if err != nil {
		return nil, fmt.Errorf("更新补偿最终状态失败: %w", err)
	}

	return &model.ExecuteCompensationOutput{
		StepId:             input.StepId,
		Success:            compensationSuccess,
		Message:            fmt.Sprintf("补偿执行%s", map[bool]string{true: "成功", false: "失败"}[compensationSuccess]),
		SagaId:             input.SagaId,
		StepIndex:          input.StepIndex,
		Action:             step.Action,
		ServiceName:        step.ServiceName,
		CompensateEndpoint: step.CompensateEndpoint,
		CompensationStatus: newStatus,
		RetryCount:         finalRetryCount,
		ExecutedAt:         gtime.Now(),
		LastError:          lastError,
	}, nil
}

// isNetworkError 检查是否为网络错误
func (s *SagaTransactionsService) isNetworkError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// 检查常见的网络错误模式
	networkErrorPatterns := []string{
		"connection refused",
		"connection timeout",
		"timeout",
		"no such host",
		"network is unreachable",
		"connection reset by peer",
		"temporary failure",
		"dial tcp",
		"i/o timeout",
		"context deadline exceeded",
		"network unreachable",
	}

	for _, pattern := range networkErrorPatterns {
		if strings.Contains(strings.ToLower(errStr), pattern) {
			return true
		}
	}

	// 检查特定的网络错误类型
	if _, ok := err.(*net.OpError); ok {
		return true
	}

	if _, ok := err.(*url.Error); ok {
		return true
	}

	// 检查DNS错误
	if _, ok := err.(*net.DNSError); ok {
		return true
	}

	return false
}

// isRetryableHttpError 检查HTTP状态码是否可重试
// 返回值：(是否可重试, 错误类型描述)
func (s *SagaTransactionsService) isRetryableHTTPError(statusCode int) (bool, string) {
	switch {
	case statusCode >= 200 && statusCode < 300:
		return false, "成功"
	case statusCode >= 400 && statusCode < 500:
		// 4xx 客户端错误 - 通常不可重试
		switch statusCode {
		case 408: // Request Timeout
			return true, "请求超时"
		case 429: // Too Many Requests
			return true, "请求过多"
		default:
			return false, "客户端错误"
		}
	case statusCode >= 500 && statusCode < 600:
		// 5xx 服务器错误 - 通常可重试
		switch statusCode {
		case 501: // Not Implemented
			return false, "未实现"
		case 505: // HTTP Version Not Supported
			return false, "HTTP版本不支持"
		default:
			return true, "服务器错误"
		}
	default:
		// 其他状态码
		return false, "未知错误"
	}
}

// isRetryableError 统一的重试决策函数
// 参数：err - 网络错误（可能为nil），statusCode - HTTP状态码（当err为nil时有效）
// 返回值：(是否可重试, 错误类型描述)
func (s *SagaTransactionsService) isRetryableError(err error, statusCode int) (bool, string) {
	// 优先检查网络错误
	if err != nil {
		if s.isNetworkError(err) {
			return true, fmt.Sprintf("网络错误: %v", err)
		}
		return false, fmt.Sprintf("非网络错误: %v", err)
	}

	// 检查HTTP状态码
	return s.isRetryableHTTPError(statusCode)
}

// calculateRetryDelay 计算指数退避延迟时间
func (s *SagaTransactionsService) calculateRetryDelay(attempt int) time.Duration {
	// 指数退避公式：initial_interval * (multiplier ^ attempt)
	delay := float64(s.initialRetryInterval) * math.Pow(s.retryMultiplier, float64(attempt))

	// 限制最大延迟时间
	delay = min(delay, float64(s.maxRetryInterval))

	return time.Duration(delay)
}

// compensationCall 执行补偿调用（带指数退避重试）
// 返回值：(是否成功, 详细错误信息, 实际重试次数)
func (s *SagaTransactionsService) compensationCall(ctx context.Context, step *entity.SagaSteps, timeoutMs time.Duration) (bool, string, int) {
	// 设置超时时间
	s.httpClient.SetTimeout(time.Duration(timeoutMs) * time.Millisecond)

	// 构造HTTP头部信息
	headers := map[string]string{
		"X-Saga-Id":      step.SagaId,
		"X-Step-Id":      step.StepId,
		"X-Step-Index":   fmt.Sprintf("%d", step.StepIndex),
		"X-Service-Name": step.ServiceName,
		"X-Action":       step.Action,
		"Content-Type":   "application/json",
	}

	// 记录补偿调用开始
	g.Log().Infof(ctx, "开始调用补偿接口: %s, 超时时间: %dms, 最大重试次数: %d, SagaId: %s, StepId: %s, StepIndex: %d",
		step.CompensateEndpoint, timeoutMs, s.maxRetries, step.SagaId, step.StepId, step.StepIndex)

	var lastErr error
	var lastStatusCode int
	var lastResponseBody string

	// 执行重试逻辑
	for attempt := 0; attempt <= s.maxRetries; attempt++ {
		// 记录重试信息
		if attempt > 0 {
			g.Log().Infof(ctx, "补偿调用重试，第 %d 次，SagaId: %s, StepId: %s, Endpoint: %s",
				attempt, step.SagaId, step.StepId, step.CompensateEndpoint)
		}

		// 发送 HTTP POST 请求
		resp, err := s.httpClient.Header(headers).Post(ctx, step.CompensateEndpoint, step.CompensationContext)

		var statusCode int
		var responseBody string

		// 处理响应
		if err == nil {
			statusCode = resp.StatusCode
			responseBody = resp.ReadAllString()
			resp.Close()
		}

		// 统一的重试决策
		isRetryable, errorType := s.isRetryableError(err, statusCode)

		// 检查是否需要重试
		if err != nil || (statusCode < 200 || statusCode >= 300) {
			// 保存错误信息用于最终返回
			lastErr = err
			lastStatusCode = statusCode
			lastResponseBody = responseBody

			// 记录错误信息
			if err != nil {
				g.Log().Errorf(ctx, "补偿调用失败: %s, 第 %d 次尝试, 错误: %v",
					step.CompensateEndpoint, attempt+1, err)
			} else {
				g.Log().Errorf(ctx, "补偿调用失败: %s, 第 %d 次尝试, HTTP状态码: %d, 响应: %s",
					step.CompensateEndpoint, attempt+1, statusCode, responseBody)
			}

			// 检查是否可重试且还有重试机会
			if isRetryable && attempt < s.maxRetries {
				// 计算延迟时间
				delay := s.calculateRetryDelay(attempt)
				g.Log().Infof(ctx, "错误可重试（%s），%v 后重试，SagaId: %s, StepId: %s",
					errorType, delay, step.SagaId, step.StepId)

				// 等待指数退避时间
				time.Sleep(delay)
				continue
			}

			// 不可重试或重试次数用完，构造详细错误信息并返回失败
			errorDetail := s.buildDetailedErrorMessage(lastErr, lastStatusCode, lastResponseBody, step.CompensateEndpoint, attempt+1)
			g.Log().Errorf(ctx, "补偿调用最终失败: %s, 总尝试次数: %d, 错误类型: %s, SagaId: %s, StepId: %s",
				step.CompensateEndpoint, attempt+1, errorType, step.SagaId, step.StepId)
			return false, errorDetail, attempt
		}

		// 补偿成功
		if attempt > 0 {
			g.Log().Infof(ctx, "补偿调用重试成功: %s, 总尝试次数: %d, HTTP状态码: %d, SagaId: %s, StepId: %s",
				step.CompensateEndpoint, attempt+1, statusCode, step.SagaId, step.StepId)
		} else {
			g.Log().Infof(ctx, "补偿调用成功: %s, HTTP状态码: %d, SagaId: %s, StepId: %s",
				step.CompensateEndpoint, statusCode, step.SagaId, step.StepId)
		}

		return true, "", attempt
	}

	// 理论上不应该到达这里，但为了保险起见
	errorDetail := s.buildDetailedErrorMessage(lastErr, lastStatusCode, lastResponseBody, step.CompensateEndpoint, s.maxRetries+1)
	g.Log().Errorf(ctx, "补偿调用意外结束: %s, SagaId: %s, StepId: %s", step.CompensateEndpoint, step.SagaId, step.StepId)
	return false, errorDetail, s.maxRetries
}

// buildDetailedErrorMessage 构建详细的错误信息
func (s *SagaTransactionsService) buildDetailedErrorMessage(err error, statusCode int, responseBody, endpoint string, totalAttempts int) string {
	if err != nil {
		// 网络错误
		return fmt.Sprintf("网络错误: %v | 端点: %s | 尝试次数: %d", err, endpoint, totalAttempts)
	}
	// HTTP状态码错误
	truncatedBody := responseBody
	if len(truncatedBody) > 500 {
		truncatedBody = truncatedBody[:500] + "..."
	}
	return fmt.Sprintf("HTTP错误: 状态码 %d | 端点: %s | 尝试次数: %d | 响应: %s", statusCode, endpoint, totalAttempts, truncatedBody)
}
