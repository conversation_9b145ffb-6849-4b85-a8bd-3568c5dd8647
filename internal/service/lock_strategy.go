package service

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"

	"saga/internal/dao"
	"saga/internal/model/entity"
)

// LockStrategy 锁策略接口
// 定义统一的锁处理机制，支持悲观锁和乐观锁
type LockStrategy interface {
	// ExecuteWithLock 在锁保护下执行操作
	ExecuteWithLock(ctx context.Context, sagaId string, operation SagaOperation) error
	
	// GetStrategyName 获取策略名称
	GetStrategyName() string
	
	// IsOptimistic 是否为乐观锁策略
	IsOptimistic() bool
}

// SagaOperation 业务操作接口
// 定义在锁保护下执行的具体业务逻辑
type SagaOperation interface {
	// Execute 执行业务操作
	// saga: 当前的Saga事务记录
	// tx: 数据库事务（悲观锁时有效，乐观锁时为nil）
	Execute(ctx context.Context, saga *entity.SagaTransactions, tx gdb.TX) error
	
	// GetOperationName 获取操作名称（用于日志）
	GetOperationName() string
	
	// RequiresTransaction 是否需要数据库事务
	RequiresTransaction() bool
}

// OptimisticLockConfig 乐观锁配置
type OptimisticLockConfig struct {
	MaxRetries        int           // 最大重试次数
	BaseDelay         time.Duration // 基础延迟时间
	MaxDelay          time.Duration // 最大延迟时间
	BackoffMultiplier float64       // 指数退避倍数
}

// PessimisticLockStrategy 悲观锁策略实现
type PessimisticLockStrategy struct {
	db gdb.DB
}

// NewPessimisticLockStrategy 创建悲观锁策略
func NewPessimisticLockStrategy(db gdb.DB) LockStrategy {
	return &PessimisticLockStrategy{
		db: db,
	}
}

func (p *PessimisticLockStrategy) GetStrategyName() string {
	return "PessimisticLock"
}

func (p *PessimisticLockStrategy) IsOptimistic() bool {
	return false
}

func (p *PessimisticLockStrategy) ExecuteWithLock(ctx context.Context, sagaId string, operation SagaOperation) error {
	g.Log().Debugf(ctx, "使用悲观锁执行操作: SagaId=%s, Operation=%s", sagaId, operation.GetOperationName())
	
	if !operation.RequiresTransaction() {
		// 不需要事务的操作，直接查询并执行
		saga, err := dao.SagaTransactions.FindBySagaId(ctx, sagaId)
		if err != nil {
			return fmt.Errorf("查询Saga事务失败: %w", err)
		}
		if saga == nil {
			return fmt.Errorf("Saga事务不存在: sagaId=%s", sagaId)
		}
		return operation.Execute(ctx, saga, nil)
	}
	
	// 需要事务的操作，使用悲观锁
	return p.db.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 使用悲观锁获取Saga事务记录
		saga, err := dao.SagaTransactions.LockAndGetSaga(ctx, tx, sagaId)
		if err != nil {
			return fmt.Errorf("锁定并获取Saga事务失败: %w", err)
		}
		
		if saga == nil {
			return fmt.Errorf("Saga事务不存在: sagaId=%s", sagaId)
		}
		
		// 执行业务操作
		return operation.Execute(ctx, saga, tx)
	})
}

// OptimisticLockStrategy 乐观锁策略实现
type OptimisticLockStrategy struct {
	config OptimisticLockConfig
}

// NewOptimisticLockStrategy 创建乐观锁策略
func NewOptimisticLockStrategy(config OptimisticLockConfig) LockStrategy {
	return &OptimisticLockStrategy{
		config: config,
	}
}

func (o *OptimisticLockStrategy) GetStrategyName() string {
	return "OptimisticLock"
}

func (o *OptimisticLockStrategy) IsOptimistic() bool {
	return true
}

func (o *OptimisticLockStrategy) ExecuteWithLock(ctx context.Context, sagaId string, operation SagaOperation) error {
	g.Log().Debugf(ctx, "使用乐观锁执行操作: SagaId=%s, Operation=%s, MaxRetries=%d", 
		sagaId, operation.GetOperationName(), o.config.MaxRetries)
	
	if !operation.RequiresTransaction() {
		// 不需要事务的操作，直接查询并执行
		saga, err := dao.SagaTransactions.FindBySagaId(ctx, sagaId)
		if err != nil {
			return fmt.Errorf("查询Saga事务失败: %w", err)
		}
		if saga == nil {
			return fmt.Errorf("Saga事务不存在: sagaId=%s", sagaId)
		}
		return operation.Execute(ctx, saga, nil)
	}
	
	// 需要事务的操作，使用乐观锁重试机制
	for attempt := 0; attempt < o.config.MaxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避延迟
			delay := o.calculateRetryDelay(attempt - 1)
			g.Log().Infof(ctx, "乐观锁重试: SagaId=%s, 第%d次重试, 延迟%v", sagaId, attempt, delay)
			time.Sleep(delay)
		}
		
		// 无锁读取当前状态
		saga, err := dao.SagaTransactions.FindBySagaId(ctx, sagaId)
		if err != nil {
			return fmt.Errorf("查询Saga事务失败: %w", err)
		}
		
		if saga == nil {
			return fmt.Errorf("Saga事务不存在: sagaId=%s", sagaId)
		}
		
		// 在事务中执行操作（包含版本检查）
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			return operation.Execute(ctx, saga, tx)
		})
		
		if err != nil {
			// 检查是否是版本冲突错误
			if o.isVersionConflictError(err) {
				g.Log().Warningf(ctx, "乐观锁版本冲突: SagaId=%s, 第%d次尝试", sagaId, attempt+1)
				continue // 重试
			}
			// 其他错误直接返回
			return err
		}
		
		g.Log().Infof(ctx, "乐观锁操作成功: SagaId=%s, 尝试次数=%d", sagaId, attempt+1)
		return nil
	}
	
	return fmt.Errorf("乐观锁重试失败: SagaId=%s, 最大重试次数=%d", sagaId, o.config.MaxRetries)
}

// calculateRetryDelay 计算重试延迟时间
func (o *OptimisticLockStrategy) calculateRetryDelay(attempt int) time.Duration {
	// 指数退避公式：baseDelay * (backoffMultiplier ^ attempt)
	delay := float64(o.config.BaseDelay) * math.Pow(o.config.BackoffMultiplier, float64(attempt))
	
	// 限制最大延迟时间
	if delay > float64(o.config.MaxDelay) {
		delay = float64(o.config.MaxDelay)
	}
	
	return time.Duration(delay)
}

// isVersionConflictError 检查是否是版本冲突错误
func (o *OptimisticLockStrategy) isVersionConflictError(err error) bool {
	if err == nil {
		return false
	}
	
	errMsg := err.Error()
	return strings.Contains(errMsg, "version_conflict") || 
		   strings.Contains(errMsg, "乐观锁版本冲突")
}

// LockStrategyFactory 锁策略工厂
type LockStrategyFactory struct{}

// CreateLockStrategy 根据配置创建锁策略
func (f *LockStrategyFactory) CreateLockStrategy(optimisticEnabled bool, optimisticConfig OptimisticLockConfig) LockStrategy {
	if optimisticEnabled {
		g.Log().Info(context.Background(), "创建乐观锁策略")
		return NewOptimisticLockStrategy(optimisticConfig)
	}
	
	g.Log().Info(context.Background(), "创建悲观锁策略")
	return NewPessimisticLockStrategy(g.DB())
}
