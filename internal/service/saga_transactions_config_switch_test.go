package service

import (
	"context"
	"saga/internal/model"
	"testing"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/test/gtest"
)

// TestReportCompensationConfigSwitch 测试通过配置切换锁机制
func TestReportCompensationConfigSwitch(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 清理测试数据
		defer func() {
			cleanupTestData(t.T, "config-switch-test-pessimistic")
			cleanupTestData(t.T, "config-switch-test-optimistic")
		}()

		// 测试悲观锁模式（默认）
		t.Log("=== 测试悲观锁模式 ===")
		testPessimisticLockMode(t)

		// 测试乐观锁模式
		t.Log("=== 测试乐观锁模式 ===")
		testOptimisticLockMode(t)
	})
}

// testPessimisticLockMode 测试悲观锁模式
func testPessimisticLockMode(t *gtest.T) {
	// 创建服务实例（默认悲观锁）
	service := NewSagaTransactions()

	// 确保配置为悲观锁模式
	service.optimisticLockEnabled = false

	ctx := context.Background()
	sagaId := "config-switch-test-pessimistic"

	// 创建测试事务
	createRealTestSagaTransaction(sagaId, "auto", "", 0, "pending")

	// 测试补偿上报
	input := &model.ReportCompensationInput{
		SagaId:              sagaId,
		Action:              "test_action",
		ServiceName:         "test_service",
		ContextData:         `{"test": "data"}`,
		CompensationContext: `{"compensate": "data"}`,
		CompensateEndpoint:  "http://test-service/compensate",
	}

	result, err := service.ReportCompensation(ctx, input)
	t.AssertNil(err)
	t.AssertNE(result, nil)
	t.Assert(result.Success, true)

	g.Log().Info(ctx, "悲观锁模式测试完成")
}

// testOptimisticLockMode 测试乐观锁模式
func testOptimisticLockMode(t *gtest.T) {
	// 创建服务实例
	service := NewSagaTransactions()

	// 配置为乐观锁模式
	service.optimisticLockEnabled = true
	service.optimisticLockMaxRetries = 3
	service.optimisticLockBaseDelay = 5 * time.Millisecond
	service.optimisticLockMaxDelay = 50 * time.Millisecond
	service.optimisticLockBackoffMultiplier = 2.0

	ctx := context.Background()
	sagaId := "config-switch-test-optimistic"

	// 创建测试事务
	createRealTestSagaTransaction(sagaId, "auto", "", 0, "pending")

	// 测试补偿上报
	input := &model.ReportCompensationInput{
		SagaId:              sagaId,
		Action:              "test_action",
		ServiceName:         "test_service",
		ContextData:         `{"test": "data"}`,
		CompensationContext: `{"compensate": "data"}`,
		CompensateEndpoint:  "http://test-service/compensate",
	}

	result, err := service.ReportCompensation(ctx, input)
	t.AssertNil(err)
	t.AssertNE(result, nil)
	t.Assert(result.Success, true)

	g.Log().Info(ctx, "乐观锁模式测试完成")
}

// TestReportCompensationConfigFromFile 测试从配置文件读取锁模式配置
func TestReportCompensationConfigFromFile(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 清理测试数据
		defer cleanupTestData(t.T, "config-file-test")

		// 创建服务实例（会自动从配置文件读取）
		service := NewSagaTransactions()

		ctx := context.Background()
		sagaId := "config-file-test"

		// 创建测试事务
		createRealTestSagaTransaction(sagaId, "auto", "", 0, "pending")

		// 测试补偿上报
		input := &model.ReportCompensationInput{
			SagaId:              sagaId,
			Action:              "test_action",
			ServiceName:         "test_service",
			ContextData:         `{"test": "data"}`,
			CompensationContext: `{"compensate": "data"}`,
			CompensateEndpoint:  "http://test-service/compensate",
		}

		result, err := service.ReportCompensation(ctx, input)
		t.AssertNil(err)
		t.AssertNE(result, nil)
		t.Assert(result.Success, true)

		// 输出当前配置信息
		g.Log().Infof(ctx, "配置文件测试完成 - 乐观锁启用状态: %v, 最大重试次数: %d",
			service.optimisticLockEnabled, service.optimisticLockMaxRetries)
	})
}
