package service

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"

	"saga/internal/consts"
	"saga/internal/model"
)

// SagaTransactionsServiceV2 重构后的Saga事务服务
// 使用策略模式分离锁机制和业务逻辑
type SagaTransactionsServiceV2 struct {
	// 原有的配置保持不变
	httpClient *gclient.Client
	// 补偿重试配置
	maxRetries           int
	initialRetryInterval time.Duration
	maxRetryInterval     time.Duration
	retryMultiplier      float64
	compensationTimeout  time.Duration
	// ReportCompensation 重试配置
	reportMaxRetries int
	reportRetryDelay time.Duration
	// 补偿窗口期配置
	defaultCompensationWindow int

	// 锁策略
	lockStrategy LockStrategy
	
	// 原有服务实例（用于复用现有方法）
	originalService *SagaTransactionsService
}

// NewSagaTransactionsV2 创建重构后的服务实例
func NewSagaTransactionsV2() *SagaTransactionsServiceV2 {
	// 创建原有服务实例
	originalService := NewSagaTransactions()
	
	// 创建锁策略
	factory := &LockStrategyFactory{}
	optimisticConfig := OptimisticLockConfig{
		MaxRetries:        originalService.optimisticLockMaxRetries,
		BaseDelay:         originalService.optimisticLockBaseDelay,
		MaxDelay:          originalService.optimisticLockMaxDelay,
		BackoffMultiplier: originalService.optimisticLockBackoffMultiplier,
	}
	
	lockStrategy := factory.CreateLockStrategy(originalService.optimisticLockEnabled, optimisticConfig)
	
	service := &SagaTransactionsServiceV2{
		httpClient:                originalService.httpClient,
		maxRetries:                originalService.maxRetries,
		initialRetryInterval:      originalService.initialRetryInterval,
		maxRetryInterval:          originalService.maxRetryInterval,
		retryMultiplier:           originalService.retryMultiplier,
		compensationTimeout:       originalService.compensationTimeout,
		reportMaxRetries:          originalService.reportMaxRetries,
		reportRetryDelay:          originalService.reportRetryDelay,
		defaultCompensationWindow: originalService.defaultCompensationWindow,
		lockStrategy:              lockStrategy,
		originalService:           originalService,
	}
	
	g.Log().Infof(context.Background(), "创建SagaTransactionsServiceV2: 锁策略=%s", lockStrategy.GetStrategyName())
	return service
}

// SetLockStrategy 设置锁策略
func (s *SagaTransactionsServiceV2) SetLockStrategy(strategy LockStrategy) {
	s.lockStrategy = strategy
	g.Log().Infof(context.Background(), "切换锁策略: %s", strategy.GetStrategyName())
}

// GetLockStrategy 获取当前锁策略
func (s *SagaTransactionsServiceV2) GetLockStrategy() LockStrategy {
	return s.lockStrategy
}

// CreateSagaTransaction 创建分布式事务（保持原有接口不变）
func (s *SagaTransactionsServiceV2) CreateSagaTransaction(ctx context.Context, input *model.CreateSagaTransactionInput) (*model.CreateSagaTransactionOutput, error) {
	// 直接调用原有实现，创建操作不涉及锁竞争
	return s.originalService.CreateSagaTransaction(ctx, input)
}

// GetSagaTransaction 获取Saga事务信息
func (s *SagaTransactionsServiceV2) GetSagaTransaction(ctx context.Context, input *model.GetSagaTransactionInput) (*model.GetSagaTransactionOutput, error) {
	g.Log().Infof(ctx, "获取Saga事务信息: SagaId=%s, 锁策略=%s", input.SagaId, s.lockStrategy.GetStrategyName())
	
	// 创建查询操作
	operation := NewGetOperation(input, s.originalService)
	
	// 执行操作
	err := s.lockStrategy.ExecuteWithLock(ctx, input.SagaId, operation)
	if err != nil {
		g.Log().Errorf(ctx, "获取Saga事务信息失败: SagaId=%s, 错误=%v", input.SagaId, err)
		return nil, err
	}
	
	return operation.GetResult(), nil
}

// ReportCompensation 服务上报补偿操作结果
func (s *SagaTransactionsServiceV2) ReportCompensation(ctx context.Context, input *model.ReportCompensationInput) (*model.ReportCompensationOutput, error) {
	g.Log().Infof(ctx, "上报补偿信息: SagaId=%s, Action=%s, Service=%s, 锁策略=%s",
		input.SagaId, input.Action, input.ServiceName, s.lockStrategy.GetStrategyName())
	
	// 创建上报操作
	operation := NewReportOperation(input, s.originalService, s.lockStrategy)
	
	// 执行操作
	err := s.lockStrategy.ExecuteWithLock(ctx, input.SagaId, operation)
	if err != nil {
		g.Log().Errorf(ctx, "上报补偿信息失败: SagaId=%s, Action=%s, 错误=%v", input.SagaId, input.Action, err)
		return nil, err
	}
	
	result := operation.GetResult()
	compensationInfo := operation.GetCompensationInfo()
	
	// 如果需要异步执行补偿，在事务提交后执行
	if compensationInfo != nil {
		g.Log().Infof(ctx, "准备异步执行补偿: SagaId=%s, StepId=%s", compensationInfo.SagaId, compensationInfo.StepId)
		
		go func(info *model.ExecuteCompensationInput) {
			asyncCtx := context.Background()
			_, execErr := s.originalService.ExecuteCompensation(asyncCtx, info)
			if execErr != nil {
				g.Log().Errorf(asyncCtx, "补偿窗口期内异步执行补偿失败: SagaId=%s, StepId=%s, Error=%v",
					info.SagaId, info.StepId, execErr)
			}
		}(compensationInfo)
	}
	
	return result, nil
}

// CommitSagaTransaction 提交分布式事务
func (s *SagaTransactionsServiceV2) CommitSagaTransaction(ctx context.Context, input *model.CommitSagaTransactionInput) (*model.CommitSagaTransactionOutput, error) {
	g.Log().Infof(ctx, "提交Saga事务: SagaId=%s, 锁策略=%s", input.SagaId, s.lockStrategy.GetStrategyName())
	
	// 创建提交操作
	operation := NewCommitOperation(input, s.originalService)
	
	// 执行操作
	err := s.lockStrategy.ExecuteWithLock(ctx, input.SagaId, operation)
	if err != nil {
		g.Log().Errorf(ctx, "提交Saga事务失败: SagaId=%s, 错误=%v", input.SagaId, err)
		return nil, err
	}
	
	return operation.GetResult(), nil
}

// RollbackSagaTransaction 回滚分布式事务
func (s *SagaTransactionsServiceV2) RollbackSagaTransaction(ctx context.Context, input *model.RollbackSagaTransactionInput) (*model.RollbackSagaTransactionOutput, error) {
	g.Log().Infof(ctx, "回滚Saga事务: SagaId=%s, 锁策略=%s", input.SagaId, s.lockStrategy.GetStrategyName())
	
	// 创建回滚操作
	operation := NewRollbackOperation(input, s.originalService)
	
	// 执行操作
	err := s.lockStrategy.ExecuteWithLock(ctx, input.SagaId, operation)
	if err != nil {
		g.Log().Errorf(ctx, "回滚Saga事务失败: SagaId=%s, 错误=%v", input.SagaId, err)
		return nil, err
	}
	
	result := operation.GetResult()
	
	// 根据执行模式处理补偿操作（在事务外执行）
	if result.Success && input.ExecutionMode != consts.CompensationExecutionModeNone {
		// 查询需要补偿的步骤
		compensationSteps, err := s.originalService.queryCompensationSteps(ctx, input.SagaId, consts.CompensationStatusUninitialized)
		if err != nil {
			g.Log().Errorf(ctx, "查询补偿步骤失败: SagaId=%s, 错误=%v", input.SagaId, err)
			return result, nil // 返回回滚结果，但记录错误
		}
		
		// 创建StartRollbackOutput用于补偿执行
		startOutput := &model.StartRollbackOutput{
			Success:           true,
			CompensationSteps: compensationSteps,
			TotalStepsToRoll:  len(compensationSteps),
			StartedAt:         result.StartedAt,
		}
		
		switch input.ExecutionMode {
		case consts.CompensationExecutionModeSync:
			// 同步执行补偿
			syncResult, err := s.originalService.executeSyncCompensation(ctx, input, startOutput)
			if err != nil {
				g.Log().Errorf(ctx, "同步执行补偿失败: SagaId=%s, 错误=%v", input.SagaId, err)
			} else {
				// 更新结果
				result.IsRollbackCompleted = syncResult.IsRollbackCompleted
				result.CompletedAt = syncResult.CompletedAt
				result.NewStatus = syncResult.NewStatus
			}
			
		case consts.CompensationExecutionModeAsync:
			// 异步执行补偿
			_, err := s.originalService.executeAsyncCompensation(ctx, input, startOutput)
			if err != nil {
				g.Log().Errorf(ctx, "启动异步补偿失败: SagaId=%s, 错误=%v", input.SagaId, err)
			}
		}
	}
	
	return result, nil
}

// CheckRollbackStatus 检查回滚状态（保持原有接口不变）
func (s *SagaTransactionsServiceV2) CheckRollbackStatus(ctx context.Context, input *model.CheckRollbackStatusInput) (*model.CheckRollbackStatusOutput, error) {
	// 直接调用原有实现，状态查询不涉及锁竞争
	return s.originalService.CheckRollbackStatus(ctx, input)
}

// ExecuteCompensation 执行单个步骤的补偿操作（保持原有接口不变）
func (s *SagaTransactionsServiceV2) ExecuteCompensation(ctx context.Context, input *model.ExecuteCompensationInput) (*model.ExecuteCompensationOutput, error) {
	// 直接调用原有实现，补偿执行不涉及Saga状态锁竞争
	return s.originalService.ExecuteCompensation(ctx, input)
}

// SwitchToOptimisticLock 切换到乐观锁模式
func (s *SagaTransactionsServiceV2) SwitchToOptimisticLock(config OptimisticLockConfig) {
	s.lockStrategy = NewOptimisticLockStrategy(config)
	g.Log().Info(context.Background(), "已切换到乐观锁模式")
}

// SwitchToPessimisticLock 切换到悲观锁模式
func (s *SagaTransactionsServiceV2) SwitchToPessimisticLock() {
	s.lockStrategy = NewPessimisticLockStrategy(g.DB())
	g.Log().Info(context.Background(), "已切换到悲观锁模式")
}

// GetLockStrategyInfo 获取当前锁策略信息
func (s *SagaTransactionsServiceV2) GetLockStrategyInfo() map[string]interface{} {
	info := map[string]interface{}{
		"strategy_name": s.lockStrategy.GetStrategyName(),
		"is_optimistic": s.lockStrategy.IsOptimistic(),
	}
	
	if s.lockStrategy.IsOptimistic() {
		if optimisticStrategy, ok := s.lockStrategy.(*OptimisticLockStrategy); ok {
			info["max_retries"] = optimisticStrategy.config.MaxRetries
			info["base_delay"] = optimisticStrategy.config.BaseDelay.String()
			info["max_delay"] = optimisticStrategy.config.MaxDelay.String()
			info["backoff_multiplier"] = optimisticStrategy.config.BackoffMultiplier
		}
	}
	
	return info
}
