package service

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// SagaServiceConfig Saga服务配置
type SagaServiceConfig struct {
	// 乐观锁配置
	OptimisticLock OptimisticLockSettings `json:"optimistic_lock"`
	
	// 补偿配置
	Compensation CompensationSettings `json:"compensation"`
	
	// 重试配置
	Retry RetrySettings `json:"retry"`
}

// OptimisticLockSettings 乐观锁设置
type OptimisticLockSettings struct {
	Enabled           bool          `json:"enabled"`
	MaxRetries        int           `json:"max_retries"`
	BaseDelay         time.Duration `json:"base_delay"`
	MaxDelay          time.Duration `json:"max_delay"`
	BackoffMultiplier float64       `json:"backoff_multiplier"`
}

// CompensationSettings 补偿设置
type CompensationSettings struct {
	Timeout               time.Duration `json:"timeout"`
	DefaultWindowSec      int           `json:"default_window_sec"`
	ReportMaxRetries      int           `json:"report_max_retries"`
	ReportRetryDelay      time.Duration `json:"report_retry_delay"`
}

// RetrySettings 重试设置
type RetrySettings struct {
	MaxRetries           int           `json:"max_retries"`
	InitialInterval      time.Duration `json:"initial_interval"`
	MaxInterval          time.Duration `json:"max_interval"`
	Multiplier           float64       `json:"multiplier"`
}

// SagaServiceFactory Saga服务工厂
type SagaServiceFactory struct {
	config *SagaServiceConfig
}

// NewSagaServiceFactory 创建服务工厂
func NewSagaServiceFactory() *SagaServiceFactory {
	factory := &SagaServiceFactory{}
	factory.loadConfig()
	return factory
}

// loadConfig 从配置文件加载配置
func (f *SagaServiceFactory) loadConfig() {
	ctx := context.Background()
	
	// 设置默认配置
	f.config = &SagaServiceConfig{
		OptimisticLock: OptimisticLockSettings{
			Enabled:           false,
			MaxRetries:        3,
			BaseDelay:         10 * time.Millisecond,
			MaxDelay:          100 * time.Millisecond,
			BackoffMultiplier: 2.0,
		},
		Compensation: CompensationSettings{
			Timeout:               30 * time.Second,
			DefaultWindowSec:      300, // 5分钟
			ReportMaxRetries:      3,
			ReportRetryDelay:      1 * time.Second,
		},
		Retry: RetrySettings{
			MaxRetries:           3,
			InitialInterval:      1 * time.Second,
			MaxInterval:          30 * time.Second,
			Multiplier:           2.0,
		},
	}
	
	// 从配置文件读取
	if g.Cfg().Available(ctx) {
		// 乐观锁配置
		f.config.OptimisticLock.Enabled = g.Cfg().MustGet(ctx, "saga.optimisticLock.enabled", false).Bool()
		f.config.OptimisticLock.MaxRetries = g.Cfg().MustGet(ctx, "saga.optimisticLock.maxRetries", 3).Int()
		f.config.OptimisticLock.BaseDelay = g.Cfg().MustGet(ctx, "saga.optimisticLock.baseDelay", "10ms").Duration()
		f.config.OptimisticLock.MaxDelay = g.Cfg().MustGet(ctx, "saga.optimisticLock.maxDelay", "100ms").Duration()
		f.config.OptimisticLock.BackoffMultiplier = g.Cfg().MustGet(ctx, "saga.optimisticLock.backoffMultiplier", 2.0).Float64()
		
		// 补偿配置
		f.config.Compensation.Timeout = g.Cfg().MustGet(ctx, "saga.compensation.timeout", "30s").Duration()
		f.config.Compensation.DefaultWindowSec = g.Cfg().MustGet(ctx, "saga.compensation.defaultWindowSec", 300).Int()
		f.config.Compensation.ReportMaxRetries = g.Cfg().MustGet(ctx, "saga.compensation.reportMaxRetries", 3).Int()
		f.config.Compensation.ReportRetryDelay = g.Cfg().MustGet(ctx, "saga.compensation.reportRetryDelay", "1s").Duration()
		
		// 重试配置
		f.config.Retry.MaxRetries = g.Cfg().MustGet(ctx, "saga.retry.maxRetries", 3).Int()
		f.config.Retry.InitialInterval = g.Cfg().MustGet(ctx, "saga.retry.initialInterval", "1s").Duration()
		f.config.Retry.MaxInterval = g.Cfg().MustGet(ctx, "saga.retry.maxInterval", "30s").Duration()
		f.config.Retry.Multiplier = g.Cfg().MustGet(ctx, "saga.retry.multiplier", 2.0).Float64()
		
		g.Log().Infof(ctx, "Saga服务配置加载完成: 乐观锁=%v, 补偿超时=%v, 重试次数=%d",
			f.config.OptimisticLock.Enabled, f.config.Compensation.Timeout, f.config.Retry.MaxRetries)
	}
}

// CreateService 创建Saga服务实例
func (f *SagaServiceFactory) CreateService() *SagaTransactionsServiceV2 {
	g.Log().Info(context.Background(), "创建Saga服务实例")
	
	// 创建锁策略
	lockStrategy := f.createLockStrategy()
	
	// 创建原有服务实例（用于复用现有方法）
	originalService := f.createOriginalService()
	
	// 创建V2服务实例
	service := &SagaTransactionsServiceV2{
		httpClient:                originalService.httpClient,
		maxRetries:                f.config.Retry.MaxRetries,
		initialRetryInterval:      f.config.Retry.InitialInterval,
		maxRetryInterval:          f.config.Retry.MaxInterval,
		retryMultiplier:           f.config.Retry.Multiplier,
		compensationTimeout:       f.config.Compensation.Timeout,
		reportMaxRetries:          f.config.Compensation.ReportMaxRetries,
		reportRetryDelay:          f.config.Compensation.ReportRetryDelay,
		defaultCompensationWindow: f.config.Compensation.DefaultWindowSec,
		lockStrategy:              lockStrategy,
		originalService:           originalService,
	}
	
	g.Log().Infof(context.Background(), "Saga服务实例创建完成: 锁策略=%s", lockStrategy.GetStrategyName())
	return service
}

// createLockStrategy 创建锁策略
func (f *SagaServiceFactory) createLockStrategy() LockStrategy {
	if f.config.OptimisticLock.Enabled {
		g.Log().Info(context.Background(), "创建乐观锁策略")
		return NewOptimisticLockStrategy(OptimisticLockConfig{
			MaxRetries:        f.config.OptimisticLock.MaxRetries,
			BaseDelay:         f.config.OptimisticLock.BaseDelay,
			MaxDelay:          f.config.OptimisticLock.MaxDelay,
			BackoffMultiplier: f.config.OptimisticLock.BackoffMultiplier,
		})
	}
	
	g.Log().Info(context.Background(), "创建悲观锁策略")
	return NewPessimisticLockStrategy(g.DB())
}

// createOriginalService 创建原有服务实例
func (f *SagaServiceFactory) createOriginalService() *SagaTransactionsService {
	return NewSagaTransactionsWithFullConfig(
		f.config.Retry.MaxRetries,
		f.config.Retry.InitialInterval,
		f.config.Retry.MaxInterval,
		f.config.Retry.Multiplier,
		f.config.Compensation.Timeout,
		f.config.Compensation.ReportMaxRetries,
		f.config.Compensation.ReportRetryDelay,
		f.config.Compensation.DefaultWindowSec,
	)
}

// GetConfig 获取当前配置
func (f *SagaServiceFactory) GetConfig() *SagaServiceConfig {
	return f.config
}

// UpdateOptimisticLockConfig 更新乐观锁配置
func (f *SagaServiceFactory) UpdateOptimisticLockConfig(enabled bool, config OptimisticLockConfig) {
	f.config.OptimisticLock.Enabled = enabled
	f.config.OptimisticLock.MaxRetries = config.MaxRetries
	f.config.OptimisticLock.BaseDelay = config.BaseDelay
	f.config.OptimisticLock.MaxDelay = config.MaxDelay
	f.config.OptimisticLock.BackoffMultiplier = config.BackoffMultiplier
	
	g.Log().Infof(context.Background(), "乐观锁配置已更新: enabled=%v, maxRetries=%d", enabled, config.MaxRetries)
}

// CreateServiceWithCustomConfig 使用自定义配置创建服务
func (f *SagaServiceFactory) CreateServiceWithCustomConfig(config *SagaServiceConfig) *SagaTransactionsServiceV2 {
	// 临时保存原配置
	originalConfig := f.config
	
	// 使用自定义配置
	f.config = config
	
	// 创建服务
	service := f.CreateService()
	
	// 恢复原配置
	f.config = originalConfig
	
	return service
}

// SagaServiceManager 服务管理器
type SagaServiceManager struct {
	factory *SagaServiceFactory
	service *SagaTransactionsServiceV2
}

// NewSagaServiceManager 创建服务管理器
func NewSagaServiceManager() *SagaServiceManager {
	factory := NewSagaServiceFactory()
	service := factory.CreateService()
	
	return &SagaServiceManager{
		factory: factory,
		service: service,
	}
}

// GetService 获取服务实例
func (m *SagaServiceManager) GetService() *SagaTransactionsServiceV2 {
	return m.service
}

// SwitchLockStrategy 切换锁策略
func (m *SagaServiceManager) SwitchLockStrategy(useOptimistic bool, config *OptimisticLockConfig) {
	if useOptimistic {
		if config != nil {
			m.service.SwitchToOptimisticLock(*config)
		} else {
			// 使用默认配置
			defaultConfig := OptimisticLockConfig{
				MaxRetries:        3,
				BaseDelay:         10 * time.Millisecond,
				MaxDelay:          100 * time.Millisecond,
				BackoffMultiplier: 2.0,
			}
			m.service.SwitchToOptimisticLock(defaultConfig)
		}
	} else {
		m.service.SwitchToPessimisticLock()
	}
}

// GetCurrentConfig 获取当前配置
func (m *SagaServiceManager) GetCurrentConfig() *SagaServiceConfig {
	return m.factory.GetConfig()
}

// GetLockStrategyInfo 获取锁策略信息
func (m *SagaServiceManager) GetLockStrategyInfo() map[string]interface{} {
	return m.service.GetLockStrategyInfo()
}

// ReloadConfig 重新加载配置
func (m *SagaServiceManager) ReloadConfig() {
	g.Log().Info(context.Background(), "重新加载Saga服务配置")
	
	// 重新加载配置
	m.factory.loadConfig()
	
	// 重新创建服务
	m.service = m.factory.CreateService()
	
	g.Log().Info(context.Background(), "Saga服务配置重新加载完成")
}
